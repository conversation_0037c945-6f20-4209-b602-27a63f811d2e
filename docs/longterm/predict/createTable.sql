DROP TABLE IF EXISTS longterm_predict_category_config;
CREATE TABLE IF NOT EXISTS longterm_predict_category_config
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time         TIMESTAMP             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         TIMESTAMP             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted             TINYINT(1)            DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',
    category            VARCHAR(255) NOT NULL DEFAULT '',
    interval_month      INT          NOT NULL DEFAULT 0,
    dims_name           JSON                  DEFAULT NULL,
    scope_customer      VARCHAR(64)           DEFAULT '',
    scope_product       VARCHAR(64)           DEFAULT '',
    scope_resource_pool VARCHAR(64)           DEFAULT ''
);

DROP TABLE IF EXISTS longterm_predict_task;
CREATE TABLE IF NOT EXISTS longterm_predict_task
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time         TIMESTAMP   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted             TINYINT(1)  DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    category_id         BIGINT NOT NULL,

    is_enable           TINYINT(1)  DEFAULT 0, # 0: disable, 1: enable

    predict_end         DATE        DEFAULT NULL,
    predict_start       DATE        DEFAULT NULL,
    condition_sql       TEXT        DEFAULT NULL,
    input_sql           TEXT        DEFAULT NULL,
    dims_name           JSON        DEFAULT NULL,
    scope_resource_pool VARCHAR(64) DEFAULT '',
    scope_product       VARCHAR(64) DEFAULT '',
    scope_customer      VARCHAR(64) DEFAULT ''
);

###################################################### input #####################################################
DROP TABLE IF EXISTS longterm_predict_args_trade;
CREATE TABLE IF NOT EXISTS longterm_predict_args_trade
(
    id                BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time       TIMESTAMP      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       TIMESTAMP      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted           TINYINT(1)     DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    category_id       BIGINT NOT NULL,
    date              DATE           DEFAULT NULL,
    core_num          DECIMAL(10, 2) DEFAULT 0.00,
    new_core_num      DECIMAL(10, 2) DEFAULT 0.00,
    ret_core_num      DECIMAL(10, 2) DEFAULT 0.00,
    purchase_core_num DECIMAL(10, 2) DEFAULT 0.00,
    replace_rate      DECIMAL(10, 2) DEFAULT 0.00,
    purchase_rate     DECIMAL(10, 2) DEFAULT 0.00
);


DROP TABLE IF EXISTS longterm_predict_input_args;
CREATE TABLE IF NOT EXISTS longterm_predict_input_args
(
    id                BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time       TIMESTAMP             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       TIMESTAMP             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted           TINYINT(1)            DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id           BIGINT       NOT NULL DEFAULT 0,
    category_id       BIGINT       NOT NULL DEFAULT 0,

    strategy_type     VARCHAR(255) NOT NULL DEFAULT '',
    scale_growth_rate DECIMAL(10, 2)        DEFAULT 0.00,
    replace_rate      DECIMAL(10, 2)        DEFAULT 0.00,
    purchase_rate     DECIMAL(10, 2)        DEFAULT 0.00,
    note              MEDIUMTEXT            DEFAULT NULL,
    attachment        JSON                  DEFAULT NULL,
    start_date        DATE                  DEFAULT NULL,
    end_date          DATE                  DEFAULT NULL,
    date_name         VARCHAR(255)          DEFAULT ''
);

DROP TABLE IF EXISTS longterm_predict_input_scale;
CREATE TABLE IF NOT EXISTS longterm_predict_input_scale
(
    id                 BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time        TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted            TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id            BIGINT NOT NULL DEFAULT 0,
    category_id        BIGINT NOT NULL DEFAULT 0,

    cur_core           DECIMAL(10, 2)  DEFAULT 0.00,

    date               DATE            DEFAULT NULL,
    `year_month`       VARCHAR(255)    DEFAULT '',
    zone_name          VARCHAR(255)    DEFAULT '',
    region_name        VARCHAR(255)    DEFAULT '',
    biz_range_type     VARCHAR(255)    DEFAULT '',
    customer_obs_title VARCHAR(255)    DEFAULT '',
    instance_type      VARCHAR(255)    DEFAULT '',
    instance_family    VARCHAR(255)    DEFAULT ''
);

DROP TABLE IF EXISTS longterm_predict_input_purchase;
CREATE TABLE IF NOT EXISTS longterm_predict_input_purchase
(
    id           BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time  TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted      TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id      BIGINT NOT NULL DEFAULT 0,
    category_id  BIGINT NOT NULL DEFAULT 0,

    date         DATE            DEFAULT NULL,
    `year_month` VARCHAR(255)    DEFAULT '',
    cur_core     DECIMAL(10, 2)  DEFAULT 0.00,
    zone_name    VARCHAR(255)    DEFAULT '',
    region_name  VARCHAR(255)    DEFAULT '',
    campus       VARCHAR(255)    DEFAULT '',
    region       VARCHAR(255)    DEFAULT '',
    device_type  VARCHAR(255)    DEFAULT ''
);

################################################ output #################################################

DROP TABLE IF EXISTS longterm_predict_output_purchase;
CREATE TABLE IF NOT EXISTS longterm_predict_output_purchase
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted     TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id     BIGINT NOT NULL DEFAULT 0,
    category_id BIGINT NOT NULL DEFAULT 0,

    date_start  DATE            DEFAULT NULL,
    date_end    DATE            DEFAULT NULL,
    date        DATE            DEFAULT NULL,
    date_name   VARCHAR(255)    DEFAULT '',

    core_num    DECIMAL(10, 2)  DEFAULT 0.00
);

DROP TABLE IF EXISTS longterm_predict_output_scale;
CREATE TABLE IF NOT EXISTS longterm_predict_output_scale
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted     TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id     BIGINT NOT NULL DEFAULT 0,
    category_id BIGINT NOT NULL DEFAULT 0,

    date        DATE            DEFAULT NULL,
    date_name   VARCHAR(255)    DEFAULT '',
    core_num    DECIMAL(10, 2)  DEFAULT 0.00

);

DROP TABLE IF EXISTS longterm_predict_output_split_version;
CREATE TABLE IF NOT EXISTS longterm_predict_output_split_version
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted     TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id     BIGINT NOT NULL DEFAULT 0,
    category_id BIGINT NOT NULL DEFAULT 0,

    name        VARCHAR(255)    DEFAULT '',
    note        TEXT            DEFAULT NULL,
    split_arg   JSON            DEFAULT NULL
);

DROP TABLE IF EXISTS longterm_predict_output_purchase_split;
CREATE TABLE IF NOT EXISTS longterm_predict_output_purchase_split
(
    id               BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted          TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id          BIGINT NOT NULL DEFAULT 0,
    category_id      BIGINT NOT NULL DEFAULT 0,
    split_version_id BIGINT NOT NULL DEFAULT 0,
    output_id        BIGINT NOT NULL DEFAULT 0,

    date             DATE            DEFAULT NULL,
    `year_month`     VARCHAR(255)    DEFAULT '',
    date_name        VARCHAR(255)    DEFAULT '',
    year_short       INT             DEFAULT 0,
    customer_type    VARCHAR(255)    DEFAULT '',
    biz_type         VARCHAR(255)    DEFAULT '',
    region_name      VARCHAR(255)    DEFAULT '',
    zone_name        VARCHAR(255)    DEFAULT '',
    industry_dept    VARCHAR(255)    DEFAULT '',

    core_num         DECIMAL(10, 2)  DEFAULT 0.00,
    core_num_update  DECIMAL(10, 2)  DEFAULT 0.00
);

DROP TABLE IF EXISTS longterm_predict_output_scale_split;
CREATE TABLE IF NOT EXISTS longterm_predict_output_scale_split
(
    id               BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    create_time      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted          TINYINT(1)      DEFAULT 0 COMMENT '删除标志，0表示未删除，1表示已删除',

    task_id          BIGINT NOT NULL DEFAULT 0,
    category_id      BIGINT NOT NULL DEFAULT 0,
    split_version_id BIGINT NOT NULL DEFAULT 0,
    output_id        BIGINT NOT NULL DEFAULT 0,

    date             DATE            DEFAULT NULL,
    date_name        VARCHAR(255)    DEFAULT '',
    year_short       INT             DEFAULT 0,
    core_num         DECIMAL(10, 2)  DEFAULT 0.00,
    core_num_origin  DECIMAL(10, 2)  DEFAULT 0.00,

    customer_title   VARCHAR(255)    DEFAULT '',
    region_name      VARCHAR(255)    DEFAULT '',
    zone_name        VARCHAR(255)    DEFAULT '',
    device_type      VARCHAR(255)    DEFAULT '',
    instance_family  VARCHAR(255)    DEFAULT ''
);

