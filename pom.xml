<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>yunti.apps</groupId>
  <artifactId>cloud-demand-lab</artifactId>
  <version>1.0.0-SNAPSHOT</version>

  <parent>
    <groupId>yunti</groupId>
    <artifactId>yunti-spring-boot-parent</artifactId>
    <version>2.0.5.3-SNAPSHOT</version>
  </parent>

  <dependencies>
    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-core</artifactId>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-dao</artifactId>
      <!--不用这个驱动了,直接排除掉-->
      <exclusions>
        <exclusion>
          <groupId>ru.yandex.clickhouse</groupId>
          <artifactId>clickhouse-jdbc</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-zeebe</artifactId>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-spring-boot-oss</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <scope>runtime</scope>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>report-utils</groupId>-->
<!--      <artifactId>report-utils</artifactId>-->
<!--      <scope>system</scope>-->
<!--      <version>1.0.14</version>-->
<!--      <systemPath>/Users/<USER>/IdeaProjects/report-utils/target/report-utils-1.0.14.jar</systemPath>-->
<!--    </dependency>-->

<!--    <dependency>-->
<!--      <artifactId>fiber-support</artifactId>-->
<!--      <groupId>erp.base</groupId>-->
<!--      <version>1.0.0</version>-->
<!--    </dependency>-->

<!--    报表工具类-->
    <dependency>
      <groupId>report-utils</groupId>
      <artifactId>report-utils</artifactId>
      <version>1.0.21</version>
    </dependency>

    <!--http接口框架 -->
    <dependency>
      <groupId>easily</groupId>
      <artifactId>cs-easily-tp</artifactId>
      <version>2.0.12</version>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>yunti</groupId>
      <artifactId>yunti-teg-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.pugwoo</groupId>
      <artifactId>nimble-orm</artifactId>
      <version>1.7.6</version>
    </dependency>
    <dependency>
      <groupId>com.pugwoo</groupId>
      <artifactId>redis-helper</artifactId>
      <version>1.5.4</version>
    </dependency>
    <dependency>
      <groupId>com.pugwoo</groupId>
      <artifactId>woo-utils</artifactId>
      <version>1.3.11</version>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.10.0</version>
    </dependency>

    <dependency>
      <groupId>com.qcloud</groupId>
      <artifactId>cos_api</artifactId>
      <version>5.6.227</version>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-core</artifactId>
      <version>5.8.29</version>
    </dependency>

    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>4.4.0</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>2.2.11</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.2.1-jre</version>
    </dependency>

    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.32</version>
    </dependency>

    <dependency>
      <groupId>org.jfree</groupId>
      <artifactId>jfreechart</artifactId>
      <version>1.5.3</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.7.3</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>2.0.52</version>
    </dependency>
    <dependency>
      <groupId>com.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.7.1</version>
    </dependency>
    <dependency>
      <groupId>org.lz4</groupId>
      <artifactId>lz4-java</artifactId>
      <version>1.8.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents.client5</groupId>
      <artifactId>httpclient5</artifactId>
      <version>5.3.1</version>
    </dependency>

    <dependency>
      <groupId>task-run</groupId>
      <artifactId>task-run-exporter</artifactId>
      <version>1.0.12</version>
    </dependency>

    <!-- 线性拟合等算法使用 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-math3</artifactId>
      <version>3.6.1</version>
    </dependency>

  </dependencies>


  <build>
    <plugins>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <configuration>
          <skipSource>true</skipSource>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
      </plugin>
    </plugins>

  </build>

</project>
