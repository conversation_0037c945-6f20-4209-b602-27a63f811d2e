<html>
<head>
  <title>cloud_demand_lab</title>
</head>
<body>
<h1 style="padding:20px; margin:20px auto; width:100%">cloud_demand_lab 已就绪！</h1>
<ul>
  <li><a th:href="@{/swagger-ui.html}">接口Swagger文档</a></li>
</ul>
<button onClick="copy()">复制Cookie</button>
</body>

<script>
  function copy() {
    const text = document.cookie;
    // text是复制文本
    // 创建input元素
    const el = document.createElement('input')
    // 给input元素赋值需要复制的文本
    el.setAttribute('value', text)
    // 将input元素插入页面
    document.body.appendChild(el)
    // 选中input元素的文本
    el.select()
    // 复制内容到剪贴板
    document.execCommand('copy')
    // 删除input元素
    document.body.removeChild(el)
  }
</script>
</html>
