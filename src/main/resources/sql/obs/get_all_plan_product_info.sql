-- 查询全量的规划产品及其对应的规划产品、部门、事业群信息
-- （包含EnableFlag=0的，这样和接口YuntiRequirementOrderGetProductInfo数量一致）
SELECT pp.PlanProductId   planProductId,
       pp.PlanProductName planProductName,
       d.DeptId           deptId,
       d.DeptName         deptName,
       bg.BgId            bgId,
       bg.BgName          bgName,
       bg.BgShortName     bgShortName
FROM   bas_obs_plan_product pp
           LEFT JOIN bas_obs_business_dept d ON d.DeptId = pp.VirtualDeptId
           LEFT JOIN bas_obs_bg bg ON bg.BgId = d.BgId