SELECT year_month_str,
       SUM(purchase_core) OVER (ORDER BY year_month_str) AS purchase_core,
        SUM(purchase_core_by_demand_month_delivered) OVER (ORDER BY year_month_str) AS purchase_core_by_demand_month_delivered,
        SUM(purchase_core_by_demand_month_total) OVER (ORDER BY year_month_str) AS purchase_core_by_demand_month_total
FROM (
         SELECT year_month_str,
                SUM(purchase_core) AS purchase_core,
                SUM(purchase_core_by_demand_month_delivered) AS purchase_core_by_demand_month_delivered,
                SUM(purchase_core_by_demand_month_total) AS purchase_core_by_demand_month_total
         FROM longterm_predict_input_purchase
         WHERE deleted = 0 AND task_id = :taskId AND YEAR(stat_time) = :year
GROUP BY year_month_str
    ) AS subquery
ORDER BY year_month_str