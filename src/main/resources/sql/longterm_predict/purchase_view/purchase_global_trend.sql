select task_id,
       sum(predict_core) as predict_core,
       sum(purchase_core) as purchase_core,
       sum(purchase_core_by_demand_month_delivered) as purchase_core_by_demand_month_delivered,
       sum(purchase_core_by_demand_month_total) as purchase_core_by_demand_month_total
from
(
    -- 预测
    select task_id,sum(purchase_core) as predict_core,
           0 as purchase_core, 0 as purchase_core_by_demand_month_delivered, 0 as purchase_core_by_demand_month_total
    from longterm_predict_output_purchase_split
    where deleted=0 and split_version_id in (:splitVersionIds) and year=:year and strategy_type=:strategyType
    group by task_id

    union all

    -- 采购量，3个口径
    select task_id,
           sum(purchase_core) as predict_core,
           sum(purchase_core) as purchase_core,
           sum(purchase_core_by_demand_month_delivered) as purchase_core_by_demand_month_delivered,
           sum(purchase_core_by_demand_month_total) as purchase_core_by_demand_month_total
    from longterm_predict_input_purchase
    where deleted=0 and task_id in (:taskIds) and year(stat_time)=:year
    group by task_id
) t
group by task_id