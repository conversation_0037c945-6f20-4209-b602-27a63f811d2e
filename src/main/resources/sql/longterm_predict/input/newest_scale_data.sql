-- 最新日期的存量数据，分境内外、内外部统计
with raw as
         (
             select stat_time,'外部业务' as biz_range_type1,cur_bill_core as cur_service_core1,month_end_date,zone_name,region_name,customhouse_title,instance_type
             from std_crp.dwd_txy_scale_df
             where cur_bill_core>0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               and stat_time=(select max(stat_time) from std_crp.dwd_txy_scale_df)

             union all
             select stat_time,'内部业务' as biz_range_type1,cur_service_core as cur_service_core1,month_end_date,zone_name,region_name,customhouse_title,instance_type
             from std_crp.dwd_txy_scale_df
             where cur_bill_core=0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               and stat_time=(select max(stat_time) from std_crp.dwd_txy_scale_df)

             union all
             select stat_time,'内部业务' as biz_range_type1,cur_service_core-cur_bill_core as cur_service_core1,month_end_date,zone_name,region_name,customhouse_title,instance_type
             from std_crp.dwd_txy_scale_df
             where cur_bill_core>0 and cur_service_core-cur_bill_core>0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               and stat_time=(select max(stat_time) from std_crp.dwd_txy_scale_df)
         )

select any(stat_time) as stat_time, biz_range_type1 as biz_range_type, customhouse_title,
    sum(cur_service_core1) as cur_core
from raw
group by biz_range_type1,customhouse_title