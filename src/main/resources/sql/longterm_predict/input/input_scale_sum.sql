-- 这个sql用于计算两个时间之间的存量变化

select stat_time,
       sum(case when stat_time=month_end_date then cur_service_core else 0 end) as cur_core
from std_crp.dwd_txy_scale_df
where

  -- condition from category
    ${CATEGORY_CONDITION}

    and stat_time in (?)

  -- performance optimize
  and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
)

group by stat_time
