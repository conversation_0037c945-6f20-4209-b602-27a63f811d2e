select stat_time,campus,device_type,sum(purchase_core) as purchase_core,
       sum(purchase_core_by_demand_month_delivered) as purchase_core_by_demand_month_delivered,
       sum(purchase_core_by_demand_month_total) as purchase_core_by_demand_month_total
from
(
    (
        -- 采购到货量（交付月份）
        select stat_time, quota_campus_name as campus, device_type,
               sum(core) as purchase_core,
               0 as purchase_core_by_demand_month_delivered,
               0 as purchase_core_by_demand_month_total
        from (select -- cloud_business_type,
                     stat_time,
                     quota_campus_name,
                     device_type,
                     sum(cpu_logic_core) as core --,
              -- (case when obs_business_type='云业务' then '云业务'
              --      when obs_business_type='内部业务' and cloud_business_type != '自研上云' then '自研业务'
              --      when cloud_business_type='自研上云' then '自研上云' else '' end) as biz_type
              from cubes.demandMarket
              where DAY = (select max (DAY) from cubes.demandMarket)
                and cloud_delivery_time>='2021-01-01' and delivery_status='erp已交付' and

              -- condition from category
                  ${CATEGORY_CONDITION}

              group by -- obs_business_type, cloud_business_type,
                  quota_campus_name, device_type,
                  toStartOfMonth(addMonths(toDateTime(cloud_delivery_time), 1)) - INTERVAL 1 DAY as stat_time)
        where
            -- condition from predict input date range
            ${DATE_RANGE}
        group by stat_time, quota_campus_name, device_type
        having purchase_core > 0
    )

    union all

    (   -- 采购到货量（需求月份）
        select stat_time,quota_campus_name as campus,device_type,
                        0 as purchase_core,
                        sum(core) as purchase_core_by_demand_month_delivered,
                        0 as purchase_core_by_demand_month_total
        from(
                select    stat_time,
                          quota_campus_name,
                          device_type,
                          sum(cpu_logic_core) as core --,
                from cubes.demandMarket
                where DAY = (select max(DAY) from cubes.demandMarket)
                  and quota_use_time>='2021-01-01' and delivery_status='erp已交付'
                  AND match_status = '已匹配'
                  AND (match_type IN ('外部标准','标准采购') or cloud_delivery_time>='2020-01-01') and

                -- condition from category
                    ${CATEGORY_CONDITION}

                group by -- obs_business_type, cloud_business_type,
                    quota_campus_name, device_type,
                    toStartOfMonth(addMonths(toDateTime(quota_use_time), 1)) - INTERVAL 1 DAY as stat_time
            )
        -- where
            -- 需求月份的到货量，不需要限定时间范围
        group by stat_time,quota_campus_name,device_type
        having purchase_core_by_demand_month_delivered>0
    )

    union all

    (   -- 采购下单量
        select stat_time,quota_campus_name as campus,device_type,
               0 as purchase_core,
               0 as purchase_core_by_demand_month_delivered,
               sum(core) as purchase_core_by_demand_month_total
        from(
                select    stat_time,
                          quota_campus_name,
                          device_type,
                          sum(cpu_logic_core) as core --,
                from cubes.demandMarket
                where DAY = (select max(DAY) from cubes.demandMarket)
                  and quota_use_time>='2021-01-01'  -- 等于是不限制delivery_status
                  AND match_status = '已匹配'
                  AND (match_type IN ('外部标准','标准采购') or cloud_delivery_time>='2020-01-01') and

                -- condition from category
                    ${CATEGORY_CONDITION}

                group by -- obs_business_type, cloud_business_type,
                    quota_campus_name, device_type,
                    toStartOfMonth(addMonths(toDateTime(quota_use_time), 1)) - INTERVAL 1 DAY as stat_time
            )
        -- where
            -- 需求月份的到货量，不需要限定时间范围
        group by stat_time,quota_campus_name,device_type
        having purchase_core_by_demand_month_total>0
    )
)
group by stat_time,campus,device_type
