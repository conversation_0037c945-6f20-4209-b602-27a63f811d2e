with raw as
         (
             select stat_time,'外部业务' as biz_range_type1,cur_bill_core as cur_service_core1,month_end_date,customhouse_title,industry_dept
             from std_crp.dwd_txy_scale_df
             where cur_bill_core>0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               -- condition from predict input date range
               and ${DATE_RANGE}
               -- performance optimize
               and stat_time in (
                 SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
                 FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
             )

             union all
             select stat_time,'内部业务' as biz_range_type1,cur_service_core as cur_service_core1,month_end_date,customhouse_title,industry_dept
             from std_crp.dwd_txy_scale_df
             where cur_bill_core=0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               -- condition from predict input date range
               and ${DATE_RANGE}
               -- performance optimize
               and stat_time in (
                 SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
                 FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
             )

             union all
             select stat_time,'内部业务' as biz_range_type1,cur_service_core-cur_bill_core as cur_service_core1,month_end_date,customhouse_title,industry_dept
             from std_crp.dwd_txy_scale_df
             where cur_bill_core>0 and cur_service_core-cur_bill_core>0 and
               -- condition from category
                 ${CATEGORY_CONDITION}
               -- condition from predict input date range
               and ${DATE_RANGE}
               -- performance optimize
               and stat_time in (
                 SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
                 FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
             )
         )

select stat_time,
       biz_range_type1 as biz_range_type,
       customhouse_title,
       industry_dept,
       sum(case when stat_time=month_end_date then cur_service_core1 else 0 end) as cur_core
from raw
group by stat_time,biz_range_type1,customhouse_title,industry_dept
having cur_core>0