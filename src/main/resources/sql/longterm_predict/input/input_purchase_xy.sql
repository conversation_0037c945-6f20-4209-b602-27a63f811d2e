select stat_time,campus,device_type,industry,
       sum(purchase_core) as purchase_core,
       sum(purchase_core_by_demand_month_delivered) as purchase_core_by_demand_month_delivered,
       sum(purchase_core_by_demand_month_total) as purchase_core_by_demand_month_total
from
(
    (   -- 采购到货量（交付月份）
        select stat_time1 as stat_time,quota_campus_name as campus,device_type,industry,
               sum(core) as purchase_core,
               0 as purchase_core_by_demand_month_delivered,
               0 as purchase_core_by_demand_month_total
        from    (
                    select toDate(delivery_time) as stat_time1,
                        zone as quota_campus_name,
                        device_type,
                        industry, sum(logic_core_num) as core
                        from cloud_demand.report_purchase_order_detail
                    where stat_time=(select max(stat_time) from cloud_demand.report_purchase_order_detail)
                      and  delivery_time >= '2021-01-01 00:00:00' and

                        -- condition from category
                        ${CATEGORY_CONDITION}

                    group by zone,device_type,toDate(delivery_time),industry
            ) t
        where
            -- condition from predict input date range
            ${DATE_RANGE}
        group by stat_time,quota_campus_name,device_type,industry
        having purchase_core>0
    )
--     union all
--     (  -- 采购到货量（需求月份）
--         select stat_time,quota_campus_name as campus,device_type,industry,
--             0 as purchase_core,
--             sum(core) as purchase_core_by_demand_month_delivered,
--             0 as purchase_core_by_demand_month_total
--         from    (
--             select date(expect_delivery_date) as stat_time,
--             zone as quota_campus_name,
--             device_type,
--             industry, sum(logic_core_num) as core
--             from report_purchase_order_detail
--             where stat_time=(select max(stat_time) from report_purchase_order_detail)
--             and  delivery_time >= '2020-01-01 00:00:00' -- 这一行在这里是作为已到货的意思
--             and
--
--             -- condition from category
--             ${CATEGORY_CONDITION}
--
--             group by zone,device_type,date(expect_delivery_date),industry
--             ) t
--         -- where
--             -- 需求月份的到货量，不需要限定时间范围
--         group by stat_time,quota_campus_name,device_type,industry
--         having purchase_core_by_demand_month_delivered>0
--     )
--     union all
--     (  -- 采购下单量
--         select stat_time,quota_campus_name as campus,device_type,industry,
--             0 as purchase_core,
--             0 as purchase_core_by_demand_month_delivered,
--             sum(core) as purchase_core_by_demand_month_total
--         from    (
--             select date(expect_delivery_date) as stat_time,
--             zone as quota_campus_name,
--             device_type,
--             industry, sum(logic_core_num) as core
--             from report_purchase_order_detail
--             where stat_time=(select max(stat_time) from report_purchase_order_detail)
--             and -- 说明：这里没有限定delivery_time，即不管delivery_time有没有值都要，即不管是否已到货
--
--             -- condition from category
--             ${CATEGORY_CONDITION}
--
--             group by zone,device_type,date(expect_delivery_date),industry
--             ) t
--         -- where
--         -- 需求月份的到货量，不需要限定时间范围
--         group by stat_time,quota_campus_name,device_type,industry
--         having purchase_core_by_demand_month_total>0
--     )
) a
group by stat_time,campus,device_type,industry
