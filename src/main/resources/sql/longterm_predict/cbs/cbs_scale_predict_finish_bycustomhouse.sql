with finish_end as (
    SELECT
        SUM(cur_service_disk) AS `sum_disk`,customhouse_title
    FROM
        std_crp.dwd_txy_cbs_scale_agg_df
    WHERE stat_time=:finishEndDate
--       and stat_time in
--         (SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
--         FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))
GROUP BY
    year_month,customhouse_title
order by
    year_month desc
    ),
    last_end as(
SELECT
    SUM(cur_service_disk) AS `sum_disk`,customhouse_title
FROM
    std_crp.dwd_txy_cbs_scale_agg_df
WHERE stat_time=:lastYearEnd
--       and stat_time in
--         (SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
--         FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))
GROUP BY
    year_month,customhouse_title
order by
    year_month desc
    )
select finish_end.sum_disk-last_end.sum_disk as sum_disk,customhouse_title
from finish_end left join last_end on finish_end.customhouse_title=last_end.customhouse_title ;