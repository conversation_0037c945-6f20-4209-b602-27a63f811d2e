SELECT
    year_month_str,
    instance_type_from_device_type AS cvm_instance_type,
    strategy_type,
    if(customhouse_title = '境内' or country_name='美国', region_name, country_name) as region_name,
    SUM(purchase_core) AS sum_core,
    customhouse_title
FROM
    cloud_demand_lab.longterm_predict_output_purchase_split
WHERE
    task_id = :task_id
  AND split_version_id = :split_version_id
  AND year_month_str BETWEEN :predict_start AND :predict_end
GROUP BY
    year_month_str,
    instance_type_from_device_type,
    strategy_type,
    region_name,
    customhouse_title
ORDER BY
    year_month_str,
    instance_type_from_device_type,
    strategy_type,
    region_name;