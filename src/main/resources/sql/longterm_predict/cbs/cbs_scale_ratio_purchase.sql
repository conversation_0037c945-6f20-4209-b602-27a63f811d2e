with pred as (select strategy_type,
    year,
    0                  as finish_purchase_core,
    sum(purchase_core) as predict_purchase_core
from longterm_predict_output_purchase_split
where split_version_id = :splitVersionId
  and year_month_str between :predStartDate and :predEndDate
group by strategy_type, year),
    finish as (select year(stat_time)    as year,
    sum(purchase_core) as finish_purchase_core,
    0                  as predict_purchase_core
from longterm_predict_input_purchase
where task_id = :taskId
  and year_month_str between :finishStartDate and :finishEndDate
group by  year)

select a.year                               as `year`,
       a.strategy_type                      as strategy_type,
       COALESCE(b.finish_purchase_core, 0) + COALESCE(a.predict_purchase_core, 0) as sum_core
from pred a
         left join finish b on a.year = b.year;