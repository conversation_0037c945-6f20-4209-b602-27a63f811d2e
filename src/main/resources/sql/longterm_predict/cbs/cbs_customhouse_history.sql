-- 境内外配比历史趋势
SELECT
    stat_time,
    customhouse_title,
    cur AS sum_cur_core_12
--     SUM(cur) OVER (
--         PARTITION BY customhouse_title
--         ORDER BY stat_time
--         ROWS BETWEEN 11 PRECEDING AND CURRENT ROW
--         ) AS sum_cur_core_12
from(
        select stat_time, customhouse_title, sum(change_service_disk_from_last_month) as cur
        from dwd_txy_cbs_scale_df where  stat_time in (
            SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
            FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)) and customhouse_title in ('境内','境外')
        group by stat_time, customhouse_title
        order by stat_time, customhouse_title);