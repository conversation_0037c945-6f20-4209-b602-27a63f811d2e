select sum(cur_core) as sum_core,
       substr(year_month_str,1,4) as year,
       '存量' as strategy_type
from longterm_predict_input_scale
where task_id=:taskId and year_month_str = :scaleYearMonth
group by year_month_str
union all
(select sum(cur_core) as sum_core,
    substr(year_month_str,1,4) as year,
        strategy_type
 from longterm_predict_output_scale_split
 where task_id=:taskId and split_version_id= :splitVersionId and year_month_str in (:currentYearEnd,:nextYearEnd)
 group by strategy_type,year_month_str)
order by year;