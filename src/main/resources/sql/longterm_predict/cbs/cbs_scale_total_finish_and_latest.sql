with pred as(
    SELECT
        SUM(cur_service_disk) AS `sum_disk`,
        year_month as year_month_str,
        stat_time,customhouse_title
    FROM
        std_crp.dwd_txy_cbs_scale_agg_df
    WHERE stat_time>'2021-01-01'
        and stat_time =:finishEndDate or stat_time=(select max(stat_time) from dwd_txy_cbs_scale_agg_df)
    GROUP BY
        year_month,stat_time,customhouse_title
    order by
        year_month,stat_time,customhouse_title desc
),scale as(
    SELECT
        SUM(cur_service_disk) AS `sum_disk`,
        year_month as year_month_str,
        stat_time,customhouse_title
    FROM
        std_crp.dwd_txy_cbs_scale_agg_df
    WHERE stat_time>'2021-01-01' and stat_time in
                                     (SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
                                      FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))
      and year_month =:scaleLastYear
    GROUP BY
        year_month,stat_time,customhouse_title
    order by
        year_month,stat_time,customhouse_title desc
) select (pred.sum_disk-scale.sum_disk) as sum_disk,customhouse_title,pred.stat_time,pred.year_month_str
from pred left join scale on scale.customhouse_title=pred.customhouse_title;