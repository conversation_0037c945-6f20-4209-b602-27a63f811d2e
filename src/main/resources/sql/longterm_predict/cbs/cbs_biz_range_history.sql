-- 内外部配比历史趋势
SELECT
    stat_time,
    if(is_inner =1,'内部','外部') as biz_range_type,
    region_name,
    cur
--     SUM(cur) OVER (
--         PARTITION BY is_inner,region_name
--         ORDER BY stat_time
--         ROWS BETWEEN 11 PRECEDING AND CURRENT ROW
--         ) AS sum_cur_core_12
from(
        select stat_time, is_inner, sum(change_service_disk_from_last_month) as cur,if(customhouse_title = '境内' or country_name='美国', region_name, country_name) as region_name
        from dwd_txy_cbs_scale_df where  stat_time in (
            SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
            FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)) and is_inner in (1,0)
        group by stat_time, is_inner,region_name,country_name
        order by stat_time, is_inner);