select SUM(cur_service_disk) AS sum_disk,
       year_month
FROM std_crp.dwd_txy_cbs_scale_agg_df
WHERE stat_time IN (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), now()) + 1)
    ORDER BY last_day_of_month DESC
) and year_month >= :year_month_start and year_month<= :year_month_end
    ${region_or_country_condition}
    ${customhouse_title_condition}
group by stat_time,year_month
order by year_month