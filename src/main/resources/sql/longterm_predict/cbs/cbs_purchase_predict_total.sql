select strategy_type,
    year,
    0                  as finish_purchase_core,
    sum(purchase_core) as predict_purchase_core,
    instance_type_from_device_type as cvm_instance_type,
    customhouse_title
from longterm_predict_output_purchase_split
where split_version_id = :splitVersionId
  and (year=:currentYear or year=:nextYear)
group by strategy_type, year,instance_type_from_device_type,customhouse_title;