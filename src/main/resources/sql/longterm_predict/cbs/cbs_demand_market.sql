
select (case when area_type='海外' then '境外' else '境内' end)as customhouse_title,
       count(*) as unit,
    any(subtractDays(DAY,1)) as day,device_type
from cubes.demandMarket
where DAY = (select max (DAY) from cubes.demandMarket)
  and delivery_status='erp已交付' and quota_plan_product_name in ('腾讯云CBS','腾讯云-CBS')
  and YEAR(cloud_delivery_time)=${YEAR}
group by area_type,device_type
order by area_type,device_type;