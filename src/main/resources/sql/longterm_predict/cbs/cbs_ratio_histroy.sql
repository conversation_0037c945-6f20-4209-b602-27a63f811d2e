select stat_time, 'CBS' as type, instance_type, cur_disk as cur ,customhouse_title
from
    (
        select stat_time, instance_type, sum(change_service_disk_from_last_month) as cur_disk,customhouse_title
        from dwd_txy_cbs_scale_agg_df where stat_time>'2021-01-31' and stat_time in (
            SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
            FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1))
                                    and  app_id not in (1258344706, 1251316161)
                                    and instance_type not like 'RS%' and instance_type not like 'RM%'
        group by stat_time, instance_type,customhouse_title
        order by stat_time, instance_type,customhouse_title
    )

union all
select stat_time, 'CVM' as type, instance_type, cur_core as cur ,customhouse_title
from
    (
        select stat_time, instance_type, sum(change_service_core_from_last_month) as cur_core,customhouse_title
        from dwd_txy_scale_df
        where stat_time>'2021-01-31' and stat_time in (
            SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
            FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
        )
          -- 这个是CVM的预测范围
          and biz_type = 'cvm' and (customhouse_title='境外' or app_id not in (1258344706, 1251316161)) and cpu_or_gpu='CPU'
          and app_role != 'LH' and instance_type not like 'RS%' and instance_type not like 'RM%'
        group by stat_time, instance_type,customhouse_title
        order by stat_time, instance_type,customhouse_title
    );