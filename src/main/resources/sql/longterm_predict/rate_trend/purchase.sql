select sum(core) as purchase_core from
    (
        select    cloud_business_type,
                  quota_campus_name,
                  device_type,
                  sum(cpu_logic_core) as core --,
        -- (case when obs_business_type='云业务' then '云业务'
        --      when obs_business_type='内部业务' and cloud_business_type != '自研上云' then '自研业务'
        --      when cloud_business_type='自研上云' then '自研上云' else '' end) as biz_type
        from cubes.demandMarket
        where DAY = (select max(DAY) from cubes.demandMarket)
          and

        -- condition from category
            ${CATEGORY_CONDITION}

        and cloud_delivery_time between :startDate and :endDate

        group by obs_business_type, cloud_business_type,
            quota_campus_name, device_type
    )
