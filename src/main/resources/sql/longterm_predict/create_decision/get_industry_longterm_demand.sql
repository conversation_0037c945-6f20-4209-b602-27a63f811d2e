select
    demand_year,
    (case when customhouse_title='境内' then '中国内地' else '' end) as country_name,
    (case when customhouse_title!='境内' then region_name else '' end) as region_name,
    sum(case when demand_type in ('RETURN') then -abs(core_num) else abs(core_num) end) as net_core
from cloud_demand.longterm_version_group_record_item a
    left join cloud_demand.longterm_version_group_record b on a.version_group_record_id=b.id
    left join cloud_demand.longterm_version_group c on a.version_group_id=c.id
where a.deleted=0 and b.deleted=0 and c.deleted=0 and b.id in (
    select max(id) from cloud_demand.longterm_version_group_record where deleted=0 and version_code=:versionCode
    group by version_group_id
) and (a.product='CVM&CBS' or a.product='PAAS产品' and c.industry_dept='内部业务部')
group by demand_year,(case when customhouse_title='境内' then '中国内地' else region_name end)