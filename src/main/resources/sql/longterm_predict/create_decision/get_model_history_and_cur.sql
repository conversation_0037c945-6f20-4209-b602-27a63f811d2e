select stat_time,
       (case when country_name in (:country) then country_name else '' end) as country_name,
       (case when country_name not in (:country) then region_name else '' end) as region_name,
       sum(cur_core) as cur_core
from longterm_predict_input_scale where deleted=0 and task_id=:taskId and stat_time in (:dates)
group by stat_time,(case when country_name in (:country) then country_name else region_name end)