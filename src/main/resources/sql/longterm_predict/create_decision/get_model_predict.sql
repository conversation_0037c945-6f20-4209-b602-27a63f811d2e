select date as stat_time,
    (case when country_name in (:country) then country_name else '' end) as country_name,
    (case when country_name not in (:country) then region_name else '' end) as region_name,
    strategy_type,
    sum(cur_core) as cur_core
from longterm_predict_output_scale_split where deleted=0 and split_version_id=:splitVersionId and date in (:dates)
group by date,(case when country_name in (:country) then country_name else region_name end),strategy_type