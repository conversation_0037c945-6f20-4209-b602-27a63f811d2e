-- 查询sop的全年需求

SELECT holiday_year,
    any(case when country_name in (:country) then country_name else '' end) as country_name1,
    any(case when country_name not in (:country) then city_name else '' end) as region_name,
    toInt32((COALESCE(SUM(core_num),0))) AS `core_num`
FROM std_crp.`ads_sop_demand_report_mif` t
WHERE
    version = :versionNum
  AND holiday_year in (:years)
  AND code_version in ('1.0','(空值)')
  AND (res_type in ('物理机')) AND (obs_project_type in ('常规项目'))
  AND (plan_product_name in ('腾讯云CVM'))
group by holiday_year,(case when country_name in (:country) then country_name else city_name end)