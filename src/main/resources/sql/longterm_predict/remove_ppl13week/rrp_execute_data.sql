-- 【nebula_shuttle】库
select plan_month, zone as campus, deviceType, proj_set_name, mod_business_type_name,
    customer_name, industry,
    sum(totalNum) as amount
from device_apply
where plan_month >= :startMonth
  and plan_month <= :endMonth
  and plan_product = '腾讯云CVM'
  and proj_set_name  != '自研上云'
  and status != 8000127
  and order_type in (1)
  and business1 not in (
    '[自研云][CBS_云架平]',
    '[自研云][CBS_基架]',
    '[腾讯云][信息流_架平]',
    '[自研云][云网关_基架]',
    '[N][腾讯云VPC网关_自研]',
    '自研上云_安全',
    '云硬盘CBS',
    '[自研云][CBS_云架平]',
    '[自研云][CES_云架平]',
    '[自研云][CKV+_云架平]',
    '[自研云][CKV_云架平]',
    '[自研云][COS_云架平]',
    '[自研云][CTSDB_云架平]',
    '[自研云][NCDB_云架平]',
    '[自研云][国内TGW_云架平]',
    '[自研云][海外TGW_云架平]',
    '[自研云][国内STGW_云架平]',
    '[自研云][海外STGW_云架平]')
  and business2 not in (
    '[自研上云][专属资源]',
    '[自研上云][TEG信安专区]',
    '[自研上云][财付通专区]'
    )
group by plan_month, zone, deviceType, proj_set_name, mod_business_type_name,
    customer_name, industry