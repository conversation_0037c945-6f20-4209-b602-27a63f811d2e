select month,
    customer_name,
    industry,
    mod_business_type_name,
    obs_project_type,
    reason,
    campus,
    zone,
    deviceType,sum(amount) as amount
from product_info
where (backup is null or backup = '')
  and flowId = :flowId and month between :startMonth and :endMonth
group by month,
    customer_name,
    industry,
    mod_business_type_name,
    obs_project_type,
    reason,
    campus,
    zone,
    deviceType
having sum(amount) > 0