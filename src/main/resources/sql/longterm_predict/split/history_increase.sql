

with
    detail as (select customhouse_title,
                      instance_type,
                      instance_family,
                      zone_name,
                      sum(if(year_month_str = :end_date, cur_core, 0)) -
                      sum(if(year_month_str = :start_date, cur_core, 0)) as diff_core
               from cloud_demand_lab.longterm_predict_input_scale
               where task_id = :task_id
                 and year_month_str in (:start_date, :end_date)
                 -- 后续加上pdd 和xhs
                 and instance_family in ('内存型', '大数据型', '标准型', '高IO型', '计算型')
                 and zone_name in (
                                   '北京八区', '北京七区', '北京六区', '南京三区', '南京一区', '上海八区', '上海五区',
                                   '上海二区', '广州七区', '广州六区',
                                   '新加坡二区', '香港三区', '香港二区', '东京二区', '东京一区',
                                   '弗吉尼亚二区', '首尔一区', '法兰克福二区', '法兰克福一区', '新加坡四区',
                                   '新加坡三区', '圣保罗一区', '雅加达二区', '雅加达一区', '曼谷二区',
                                   '曼谷一区', '弗吉尼亚一区', '首尔二区', '硅谷二区', '硅谷一区'
                   )
               group by customhouse_title, instance_family, instance_type, zone_name)

select *
from detail where diff_core > 0
order by customhouse_title,instance_family,instance_type,zone_name