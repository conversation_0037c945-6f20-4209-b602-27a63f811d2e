SELECT GROUP_CONCAT(DISTINCT a.customhouse_title SEPARATOR '||||')                         as customhouseTitle,
       GROUP_CONCAT(DISTINCT a.biz_range_type SEPARATOR '||||')                            as bizRangeType,
       GROUP_CONCAT(DISTINCT a.instance_family SEPARATOR '||||')                           as instanceFamily,
       min(a.year_month_str) as year<PERSON>onth,
       GROUP_CONCAT(DISTINCT
        CASE
            WHEN a.customhouse_title = '境外' and a.country_name!='美国' THEN a.country_name
            ELSE a.region_name
        END SEPARATOR '||||')                                          as regionOrCountry
FROM longterm_predict_input_scale a
WHERE task_id = ?;