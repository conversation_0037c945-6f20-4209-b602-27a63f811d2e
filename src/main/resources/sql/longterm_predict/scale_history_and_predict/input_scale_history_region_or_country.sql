-- 添加历史存量部分 按年月+地区维度
SELECT
    `year_month_str`,
    COALESCE(SUM(`cur_core`), 0) AS `cur_core`,
    region_name as `dim_name`
FROM
    longterm_predict_input_scale a
WHERE
    task_id = :task_id
    AND `year_month_str` >= :year_month_start
    AND `year_month_str` <= :year_month_end
    AND (customhouse_title ='境内' or country_name='美国')
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${region_condition}
    ${instance_family_condition}
GROUP By
    `year_month_str`,region_name
UNION ALL
(SELECT
     `year_month_str`,
     COALESCE(SUM(`cur_core`), 0) AS `cur_core`,
     country_name as `dims_Name`
 FROM
     longterm_predict_input_scale a
 WHERE
    task_id = :task_id
    AND `year_month_str` >= :year_month_start
    AND `year_month_str` <= :year_month_end
    AND (customhouse_title ='境外' and country_name!='美国')
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${country_condition}
    ${instance_family_condition}
 GROUP BY
     `year_month_str`,country_name )
ORDER BY
    `year_month_str`,cur_core DESC;