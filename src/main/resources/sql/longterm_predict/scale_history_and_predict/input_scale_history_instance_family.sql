-- 添加历史存量部分 按年月+机型大类维度
SELECT
    `year_month_str`,
    COALESCE(SUM(`cur_core`), 0) AS `cur_core`,
    `instance_family` as `dim_name`
FROM
    longterm_predict_input_scale a
WHERE
    task_id = :task_id
  AND `year_month_str` >= :year_month_start
  AND `year_month_str` <= :year_month_end
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${region_or_country_condition}
    ${instance_family_condition}
GROUP By
    `year_month_str`,instance_family
ORDER BY
    `year_month_str`,cur_core DESC;