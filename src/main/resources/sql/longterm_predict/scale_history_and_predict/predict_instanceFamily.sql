-- 预测曲线 按照年月+机型大类
SELECT
    `year_month_str`,
    SUM(`cur_core`) AS `cur_core`,
    input_args_id,
    instance_family as `dim_name`
FROM
    longterm_predict_output_scale_split a
WHERE
    task_id = :task_id
  AND split_version_id = :split_version_id
  AND `year_month_str` >= :year_month_start
  AND `year_month_str` <= :year_month_end
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${region_or_country_condition}
    ${instance_family_condition}
GROUP By
    `year_month_str`,input_args_id ,instance_family
ORDER BY
    `year_month_str`,cur_core DESC;