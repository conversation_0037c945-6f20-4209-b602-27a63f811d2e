
SELECT GROUP_CONCAT(DISTINCT a.customhouse_title SEPARATOR '||||') as customhouseTitle,
       GROUP_CONCAT(DISTINCT a.instance_family SEPARATOR '||||')   as instanceFamily,
       GROUP_CONCAT(DISTINCT a.device_type SEPARATOR '||||')       as deviceType,
       GROUP_CONCAT(DISTINCT a.biz_range_type SEPARATOR '||||')    as biz_range_type,
       GROUP_CONCAT(DISTINCT a.industry_dept SEPARATOR '||||')     as industry_dept,
       min(a.year_month_str)                                       as minYear<PERSON>onth,
       max(a.year_month_str)                                       as maxYear<PERSON><PERSON>h,
       GROUP_CONCAT(DISTINCT IF(a.customhouse_title = '境外' and a.country_name!='美国', a.country_name, a.region_name)
                    SEPARATOR '||||')                              as regionOrCountry
FROM (select customhouse_title,
             instance_family,
             device_type,
             year_month_str,
             country_name,
             region_name,
             '' biz_range_type,
             '' industry_dept
      from longterm_predict_input_purchase l1
      WHERE task_id = :taskId
      union
      select customhouse_title,
             instance_family,
             device_type,
             year_month_str,
             country_name,
             region_name,
             '' biz_range_type,
             '' industry_dept
      from longterm_predict_output_purchase_split l2
      WHERE task_id = :taskId
      union
      select customhouse_title,
             '' instance_family,
             '' device_type,
             year_month_str,
             '' country_name,
             '' region_name,
             biz_range_type,
             industry_dept
      from longterm_predict_output_purchase_split_industry_dept l3
      WHERE task_id = :taskId
      union
      select customhouse_title,
             instance_family,
             '' device_type,
             year_month_str,
             country_name,
             region_name,
             biz_range_type,
             '' industry_dept
      from longterm_predict_input_scale l4
      WHERE task_id = :taskId
      ) a
;