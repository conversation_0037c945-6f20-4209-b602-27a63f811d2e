-- 预测曲线按照区域
SELECT
    `year_month_str`,
    COALESCE(SUM(`cur_core`), 0) AS `cur_core`,
    input_args_id ,
    region_name as `dim_Name`
FROM
    longterm_predict_output_scale_split a
WHERE
    task_id = :task_id
  AND split_version_id = :split_version_id
  AND `year_month_str` >= :year_month_start
  AND `year_month_str` <= :year_month_end
  AND (customhouse_title ='境内' or country_name='美国')
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${region_or_country_condition}
    ${instance_family_condition}
GROUP By
    `year_month_str`,region_name ,input_args_id
UNION ALL
(SELECT
     `year_month_str`,
     COALESCE(SUM(`cur_core`), 0) AS `cur_core`,
     input_args_id ,
     country_name as `dims_Name`
 FROM
     longterm_predict_output_scale_split a
 WHERE
     task_id = :task_id
   AND split_version_id = :split_version_id
   AND `year_month_str` >= :year_month_start
   AND `year_month_str` <= :year_month_end
   AND (customhouse_title ='境外' and country_name!='美国')
     ${customhouse_title_condition}
     ${biz_range_type_condition}
     ${region_or_country_condition}
     ${instance_family_condition}
 GROUP BY
     `year_month_str`,country_name,input_args_id )
ORDER BY
    `year_month_str` ,cur_core DESC;