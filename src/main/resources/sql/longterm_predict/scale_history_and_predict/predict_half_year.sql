SELECT SUM(cur_core) as cur_core ,year_month_str ,strategy_type
FROM longterm_predict_output_scale_split lposs
WHERE task_id = :task_id AND split_version_id = :split_version_id
    ${customhouse_title_condition}
    ${biz_range_type_condition}
    ${region_or_country_condition}
    ${instance_family_condition}
  AND SUBSTR(year_month_str,6)  in('12','06')
GROUP BY year_month_str,input_args_id
ORDER BY year_month_str ;

