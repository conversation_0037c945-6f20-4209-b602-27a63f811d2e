select *
from (select oi.order_number                                       as order_number,
             oi.available_status                                   as available_status,
             oi.order_status                                       as order_status,
             oi.order_node_code                                    as order_node_code,
             oi.flow_no                                            as flow_no,
             oi.industry_dept                                      as industry_dept,
             oi.war_zone                                           as war_zone,
             oi.customer_short_name                                as customer_short_name,
             oi.customer_name                                      as customer_name,
             oi.customer_uin                                       as customer_uin,
             oi.app_role                                           as app_role,
             oi.order_category                                     as order_category,
             case
                 when order_category = 'CVM' then oi.product
                 when order_category = 'DATABASE' then oi.sub_product
                 else '(空值)' end                                 as product,
             case
                 when order_category = 'CVM' then '核心'
                 when order_category = 'DATABASE' then '部署内存'
                 else '(空值)' end                                 as statistical_caliber,
             oi.order_type                                         as order_type,
             oi.elastic_type                                       as elastic_type,
             oi.app_id                                             as app_id,
             oi.begin_elastic_date                                 as begin_elastic_date,
             oi.end_elastic_date                                   as end_elastic_date,
             oi.project_type                                       as project_type,
             oi.project_name                                       as project_name,
             oi.demand_scene                                       as demand_scene,
             detail.demand_customhouse_title                       as customhouse_title,
             detail.demand_instance_type                           as instance_type,
             detail.demand_region_name                             as region_name,
             detail.demand_zone_name                               as zone_name,
             detail.consensus_begin_buy_date                       as begin_buy_date,
             date_format(detail.consensus_begin_buy_date, '%Y-%m') as begin_year_month,
             detail.consensus_end_buy_date                         as end_buy_date,
             case
                 when oi.order_category = 'CVM' then detail.consensus_demand_cpu_num
                 when oi.order_category = 'DATABASE' then detail.total_memory
                 else 0 end                                        as total_amount,
             case
                 when oi.order_category = 'CVM' then detail.satisfied_cpu_num
                 when oi.order_category = 'DATABASE' then detail.satisfied_memory
                 else 0 end                                        as satisfy_amount
      from order_info oi
               left join order_consensus_demand_detail detail on
          oi.order_number = detail.order_number
      where oi.available_status = 'available'
        and detail.available_status = 'available'
        and oi.report_bit_num & 4 != 0 -- 过滤掉不统计的订单
        and oi.deleted = 0
        and detail.deleted = 0
     ) t
    ${where}