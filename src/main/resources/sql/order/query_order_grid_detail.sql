select imp_date            as stat_time,
       order_number        as order_number,
       customer_uin        as customer_uin,
       plan_instance_type  as instance_type,
       plan_zone_name      as zone_name,
       sum(peak_buy_cpu)   as buy_core,
       sum(peak_total_cpu) as satisfy_core
from std_crp.dwd_crp_orderdemand_order_grid_detail_df
    ${where}
group by imp_date,
         order_number,
         customer_uin,
         plan_instance_type,
         plan_zone_name