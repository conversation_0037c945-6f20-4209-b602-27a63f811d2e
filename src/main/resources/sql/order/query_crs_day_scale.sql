select stat_time,
       formatDateTime(stat_time, '%Y-%m')                          year_month,
       if('${fuzzy_area}' = 'zone_name', zone_name,region_name) as fuzzy_area,
       '(空值)' as instance_type,
       sum(cur_service_mem) as cur_service_total,
       sum(change_service_mem_from_last_month) as change_service_total
from std_crp.dwd_txy_crs_scale_df
         ${whereCondition}
group by stat_time,year_month,fuzzy_area,instance_type
order by stat_time asc