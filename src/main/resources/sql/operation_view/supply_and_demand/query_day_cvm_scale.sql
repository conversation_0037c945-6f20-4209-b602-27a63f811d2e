select stat_time,
       formatDateTime(stat_time, '%Y-%m')                                  as year_month,
       if(${has_product}, product_class, null)                             as any_product,
       if(${has_industry_dept}, origin_industry_dept, null)                as any_industry_dept,
       if(${has_war_zone}, crp_war_zone, null)                             as any_war_zone,
       if(${has_common_customer_short_name}, un_customer_short_name, null) as any_common_customer_short_name,
       if(${has_customer_uin}, uin, null)                                  as any_customer_uin,
       if(${has_customer_short_name}, customer_short_name, null)           as any_customer_short_name,
       if(${has_customhouse_title}, customhouse_title, null)               as any_customhouse_title,
       if(${has_country_name}, country_name, null)                         as any_country_name,
       if(${has_area_name}, area_name, null)                               as any_area_name,
       if(${has_region_name}, region_name, null)                           as any_region_name,
       if(${has_zone_category}, zone_category, null)                       as any_zone_category,
       if(${has_zone_name}, zone_name, null)                               as any_zone_name,
       if(${has_instance_category}, instance_category, null)               as any_instance_category,
       if(${has_instance_group}, instance_group, null)                     as any_instance_group,
       if(${has_instance_type}, instance_type, null)                       as any_instance_type,
       sum(change_core)                                                    as total_amount
from (select stat_time,
             formatDateTime(stat_time, '%Y-%m')            as year_month,
             product_class,
             origin_industry_dept,
             crp_war_zone,
             ${un_customer_short_name}                     as un_customer_short_name,
             ${uin}                                        as uin,
             customer_short_name,
             customhouse_title,
             ${country_name}                               as country_name,
             ${area_name}                                  as area_name,
             region_name,
             ${zone_category}                              as zone_category,
             zone_name,
             ${instance_category}                          as instance_category,
             ${instance_group}                             as instance_group,
             instance_type,
             concat(customhouse_title, '@', instance_type) as instance_config,
             sum(if('${queryRange}' = '服务用量', change_service_core_from_last_month,
                    change_bill_core_from_last_month))     as change_core
      from std_crp.dwd_txy_scale_df_view ${where}
      group by stat_time, year_month, product_class, origin_industry_dept, crp_war_zone, un_customer_short_name, uin,
               customer_short_name,
               customhouse_title, country_name, area_name, region_name, zone_category, zone_name,
               instance_category, instance_group, instance_category, instance_type)
group by stat_time, year_month, any_product, any_industry_dept, any_war_zone, any_common_customer_short_name,
         any_customer_uin, any_customer_short_name,
         any_customhouse_title, any_country_name, any_area_name, any_region_name, any_zone_category, any_zone_name,
         any_instance_category, any_instance_group, any_instance_type

