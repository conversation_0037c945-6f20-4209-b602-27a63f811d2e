select stat_time,
       year_month,
       if(data_source = 'ORDER', '订单', '需求PPL')              as any_data_source,
       if(${has_bill_number}, if(data_source = 'ORDER', order_number, ppl_order),
          null)                                                  as any_bill_number,
       if(${has_product}, product, null)                         as any_product,
       if(${has_industry_dept}, industry_dept, null)             as any_industry_dept,
       if(${has_war_zone}, war_zone, null)                       as any_war_zone,
       if(${has_common_customer_short_name}, common_customer_short_name,
          null)                                                  as any_common_customer_short_name,
       if(${has_customer_short_name}, customer_short_name, null) as any_customer_short_name,
       if(${has_customer_uin}, customer_uin, null)               as any_customer_uin,
       if(${has_project_name}, project_name, NULL)               as any_project_name,
       if(${has_demand_scene}, demand_scene, NULL)               as any_demand_scene,
       if(${has_demand_type},
          multiIf(demand_type = 'RETURN', '退回需求', demand_type = 'ELASTIC', '弹性需求', '新增需求'),
          NULL)                                                  as any_demand_type,
       if(${has_customhouse_title}, customhouse_title, null)     as any_customhouse_title,
       if(${has_country_name}, country_name, null)               as any_country_name,
       if(${has_area_name}, area_name, null)                     as any_area_name,
       if(${has_region_name}, region_name, null)                 as any_region_name,
       if(${has_zone_category}, zone_category, null)             as any_zone_category,
       if(${has_zone_name}, zone_name, null)                     as any_zone_name,
       if(${has_instance_category}, instance_category, NULL)     as any_instance_category,
       if(${has_instance_group}, instance_group, NULL)           as any_instance_group,
       if(${has_instance_type}, instance_type, NULL)             as any_instance_type,
       if(${has_begin_buy_date}, begin_buy_date, NULL)           as any_begin_buy_date,
       if(${has_end_buy_date}, end_buy_date, NULL)               as any_end_buy_date,
       if(data_source = 'ORDER', '订单', 'PPL')                  as data_type,
       'false'                                                   as last_year,
       if(${adjust_excel}, remark, null)                         as remark,
       if(${adjust_excel}, '是', null)                           as intervene,
       if(${adjust_excel}, error_msg, null)                      as error_msg,
       sum(instance_num_amount)                                  as instance_num,
       sum(total_core_amount)                                    as total_core,
       sum(cross_month_core_amount)                              as cross_month_core,
       sum(current_month_core_amount)                            as current_month_core,
       sum(wait_buy_total_core_amount)                           as wait_buy_total_core,
       sum(buy_total_core_amount)                                as buy_total_core
from (select stat_time,
             year_month,
             data_source,
             order_number,
             ppl_order,
             product,
             industry_dept,
             war_zone,
             common_customer_short_name,
             customer_short_name,
             customer_uin,
             project_name,
             demand_scene,
             demand_type,
             customhouse_title,
             country_name,
             area_name,
             region_name,
             zone_category,
             zone_name,
             instance_category,
             instance_group,
             instance_type,
             begin_buy_date,
             end_buy_date,
             sum(case
                     when month_type = '当月' then
                         case
                             when data_source = 'ORDER' then
                                 case
                                     when '${orderRange}' = '已履约量' then
                                         buy_total_core
                                     when '${orderRange}' = '待履约量' then
                                         wait_buy_total_core
                                     else total_core
                                     end
                             else total_core
                             end
                     else 0 end
                 )                                               as current_month_core_amount,
             sum(case
                     when month_type = '跨月' then
                         case
                             when data_source = 'ORDER' then
                                 case
                                     when '${orderRange}' = '已履约量' then
                                         buy_total_core
                                     when '${orderRange}' = '待履约量' then
                                         wait_buy_total_core
                                     else total_core
                                     end
                             else total_core
                             end
                     else 0 end
                 )                                               as cross_month_core_amount,
             current_month_core_amount + cross_month_core_amount as total_core_amount,
             sum(instance_num)                                   as instance_num_amount,
             sum(wait_buy_total_core)                            as wait_buy_total_core_amount,
             sum(buy_total_core)                                 as buy_total_core_amount
      from ${table_name} ${where}
      group by stat_time, year_month, data_source, order_number, ppl_order, product, industry_dept, war_zone,
               common_customer_short_name, customer_uin, customer_short_name,
               project_name, demand_scene, demand_type, customhouse_title, country_name, area_name, region_name,
               zone_category,
               zone_name,
               instance_category, instance_group, instance_type, begin_buy_date, end_buy_date)
group by stat_time, year_month, any_data_source, any_bill_number, any_product, any_industry_dept, any_war_zone,
         any_common_customer_short_name, any_customer_short_name,
         any_customer_uin, any_project_name, any_demand_scene, any_demand_type, any_customhouse_title, any_country_name,
         any_area_name,
         any_region_name, any_zone_category,
         any_zone_name, any_instance_category, any_instance_group, any_instance_type, any_begin_buy_date,
         any_end_buy_date, data_type, last_year, remark,
         intervene, error_msg
