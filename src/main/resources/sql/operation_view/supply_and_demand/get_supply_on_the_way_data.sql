select DAY, cloud_business_type, quota_id,  quota_plan_product_name,  quota_device_class, quota_region_name,
    quota_campus_name, campus, module ,quota_use_time,sla_date_expect, est_arrive_date,
    produce_status, is_delivery, xy_industry, xy_customer_name, pos_pre_start_datetime, is_fake_position, cpu_logic_core, count(*) as num, cpu_logic_core * num as total_core
from demandMarket
where DAY = ?
and cloud_business_type = '云业务'
and quota_plan_product_name in (?)
and (produce_status not in ('完成', '需求单已作废') or (produce_status = '完成' and is_delivery = 'erp已交付'))
group by
    DAY, cloud_business_type, quota_id, quota_plan_product_name,quota_device_class, quota_region_name,
    quota_campus_name, campus, module,quota_use_time,sla_date_expect, est_arrive_date,
    produce_status, is_delivery, xy_industry, xy_customer_name, pos_pre_start_datetime, is_fake_position, cpu_logic_core;