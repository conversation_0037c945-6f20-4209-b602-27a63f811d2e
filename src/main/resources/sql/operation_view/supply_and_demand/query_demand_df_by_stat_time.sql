select stat_time,
       year_month,
       product,
       order_number,
       ppl_order,
       industry_dept,
       war_zone,
       customer_short_name,
       customer_uin,
       common_customer_short_name,
       project_name,
       demand_scene,
       customhouse_title,
       country_name,
       area_name,
       region,
       region_name,
       zone_category,
       `zone`,
       zone_name,
       instance_category,
       instance_group,
       instance_type,
       status,
       if(data_source = 'ORDER', '订单', '需求PPL') data_source,
       order_source,
       multiIf(demand_type = 'RETURN', '退回需求', demand_type = 'ELASTIC', '弹性需求', '新增需求') demand_type,
       product_category,
       month_type,
       begin_buy_date,
       end_buy_date,
       volume_type,
       disk_type,
       db_storage_type,
       db_deploy_type,
       db_framework_type,
       is_inner,
       total_core,
       buy_total_core,
       wait_buy_total_core,
       instance_num,
       disk_num,
       demand_caliber,
       disk_storage,
       buy_disk_storage,
       wait_buy_disk_storage,
       db_memory,
       buy_db_memory,
       wait_buy_db_memory,
       project_type,
       if(${adjustExcel}, remark, null)                         as remark,
       if(${adjustExcel}, '是', null)                           as intervene,
       if(${adjustExcel}, error_msg, null)                      as error_msg
from ${table_name}
${where}
