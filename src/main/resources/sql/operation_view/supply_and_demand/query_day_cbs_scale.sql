select stat_time,
       `year_month`,
       if(${has_customhouse_title}, customhouse_title, null) as any_customhouse_title,
       if(${has_country_name}, country_name, null)           as any_country_name,
       if(${has_area_name}, area_name, null)                 as any_area_name,
       if(${has_region_name}, region_name, null)             as any_region_name,
       if(${has_zone_category}, zone_category, null)         as any_zone_category,
       if(${has_zone_name}, zone_name, null)                 as any_zone_name,
       if(${has_volume_type}, volume_type, null)             as any_volume_type,
       sum(cur_amount)                                       as cur_amount
from (select stat_time,
             date_format(stat_time, '%Y-%m')                               as `year_month`,
             if(device_type in ('throughput', 'premium'), '高性能', 'SSD') as volume_type,
             customhouse_title,
             ${country_name}                                               as country_name,
             area_name,
             region_name,
             ${zone_category}                                              as zone_category,
             zone_name,
             sum(logic_num)                                                as cur_amount
      from nebula_rrp.report_plan_detail ${where}
      group by stat_time, `year_month`,
               customhouse_title, country_name, area_name, region_name, zone_category, zone_name, volume_type) aa
group by stat_time, `year_month`,
         any_customhouse_title, any_country_name, any_area_name, any_region_name, any_zone_category, any_zone_name,any_volume_type

