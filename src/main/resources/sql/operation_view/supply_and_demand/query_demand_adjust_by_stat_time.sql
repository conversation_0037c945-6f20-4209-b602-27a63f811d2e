select version_code                                                                   as stat_time,
       demand_year_month                                                              as `year_month`,
       'CVM'                                                                          as product_category,
       product,
       industry_dept,
       '(空值)' as war_zone,
       customer_short_name                                                            as common_customer_short_name,
       customhouse_title,
       country                                                                        as country_name,
       area_name,
       region_name,
       zone_category,
       zone_name,
       instance_category,
       gins_family                                                                    as instance_group,
       instance_type,
       if(demand_type = 'RETURN', '退回需求', '新增需求')                             as demand_type,
       if(demand_type = 'RETURN', adjust_return_total_num, adjust_wait_buy_total_num) as wait_buy_total_core
from cloud_demand.ppl_and_order_adjust_version_data ${where}
