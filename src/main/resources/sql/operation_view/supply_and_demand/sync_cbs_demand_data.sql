select stat_time,
       concat(toString(`year`), '-', if(`month` < 10, concat('0', toString(`month`)),
                                        toString(`month`)))                           as `year_month`,
       `product`,
       `order_number`,
       `ppl_order`,
       `industry_dept`,
       `war_zone`,
       `customer_short_name`,
       `customer_uin`,
       `common_customer_short_name`,
       `project_name`,
       `demand_scene`,
       `customhouse_title`,
       `area_name`,
       `region_name`,
       `zone`,
       `zone_name`,
       `instance_type`,
       `status`,
       `data_source`,
       `order_source`,
       demand_type,
       begin_buy_date,
       end_buy_date,
       sum(if(demand_type = 'RETURN', -`instance_num`, `instance_num`))               as instance_num,
       sum(if(demand_type = 'RETURN', -`total_core`, `total_core`))                   as total_core,
       sum(if(demand_type = 'RETURN', -`buy_total_core`, `buy_total_core`))           as buy_total_core,
       sum(if(demand_type = 'RETURN', -`wait_buy_total_core`, `wait_buy_total_core`)) as wait_buy_total_core,
       system_disk_type,
       data_disk_type,
       system_disk,
       data_disk,
       system_disk_num                                                                as single_system_disk_num,
       system_disk_storage                                                            as single_system_disk_storage,
       data_disk_num                                                                  as single_data_disk_num,
       data_disk_storage                                                              as single_data_disk_storage,
       cbs_is_spike
from std_crp.ads_crp_ppl_join_order_version_newest_df
where demand_type in ('NEW', 'ELASTIC', 'RETURN')
  and source in ('IMPORT', 'COMD_INTERVENE', 'APPLY_AUTO_FILL', 'FORECAST')
  and is_comd != 1
  and bitAnd(report_bit_num,16) != 0
  and product not in ('数据库')
  and stat_time = ?
group by stat_time,
    year_month,
    product,
    order_number,
    ppl_order,
    industry_dept,
    war_zone,
    customer_short_name,
    customer_uin,
    common_customer_short_name,
    project_name,
    demand_scene,
    customhouse_title,
    area_name,
    region_name,
    `zone`,
    zone_name,
    instance_type,
    status,
    data_source,
    order_source,
    demand_type,
    begin_buy_date,
    end_buy_date,
    system_disk_type,
    single_system_disk_storage,
    single_system_disk_num,
    data_disk_type,
    single_data_disk_num,
    single_data_disk_storage,
    system_disk,
    data_disk,
    cbs_is_spike
