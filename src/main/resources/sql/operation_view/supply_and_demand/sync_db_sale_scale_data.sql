select DATE_FORMAT(STR_TO_DATE(data_date, '%Y%m%d'), '%Y-%m-%d') as stat_time,
       case
           when cloud_product_name_1 in ('CDB存储', 'CDB内存') then 'CDB'
           else 'CRS' end                                        as product,
       category_name_2                                           as category_name,
       customhouse_title,
       '(空值)'                                                   as country_name,
       area_name,
       region_name,
       '(空值)'                                                   as zone_category,
       zone_name,
       sum(case
               when resource_type_name = '内存' then amount
               else 0 end)                                       as memory,
       sum(case
               when resource_type_name = '磁盘' then amount
               else 0 end)                                       as storage
from end_to_end_provide.cloud_end_to_end_utilization ceteu
where data_date = ?
  and category_name_1 = '售卖规模'
  and resource_type_name in ('内存', '磁盘')
group by stat_time,
         category_name,
         customhouse_title,
         area_name,
         region_name,
         zone_name