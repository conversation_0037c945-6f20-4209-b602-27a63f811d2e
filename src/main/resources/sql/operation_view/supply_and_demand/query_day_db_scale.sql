select stat_time,
       formatDateTime(stat_time, '%Y-%m')                                 as `year_month`,
       product,
       customhouse_title,
       country_name,
       area_name,
       region_name,
       zone_category,
       zone_name,
       sum(case when '${unit}' = '逻辑容量' then storage else memory end) as cur_amount
from std_crp.dwd_db_sale_scale_df ${where}
group by stat_time, `year_month`, product,
         customhouse_title, country_name, area_name, region_name, zone_category, zone_name

