select
    stat_time,
    case
        when customhouse_title='' then '(空值)'
        else customhouse_title
    end as customhouse_title,
    case
        when area_name='' then '(空值)'
        else area_name
    end as area_name,
    case
        when region_name='' then '(空值)'
        else region_name
    end as region_name,
    case
        when zone_name='' then '(空值)'
        else zone_name
    end as zone_name,
    '(空值)' as volume_type,
    case
        when instance_type='' then '(空值)'
        else instance_type
    end as instance_type,
    supply_type,
    product_type,
    product_type as product,
    line_type,
    material_type,
    inv_detail_type,
    quota_id,
    sum(cores) as cores,
    sum(total_num) as total_num,
    sum(logic_cores) as logic_cores,
    sum(sale_cores) as sale_cores,
    0 as disk_storage,
    0 as memory
from
    cloud_demand.dws_inventory_health_supply_summary_df
where
    stat_time = ?
    and supply_type = '库存'
    and product_type = 'CVM'
group by
    stat_time,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    instance_type,
    supply_type,
    product_type,
    line_type,
    material_type,
    inv_detail_type,
    quota_id