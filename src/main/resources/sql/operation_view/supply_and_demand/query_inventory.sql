select stat_time,
       if(${has_zone_category}, zone_category, null)                                              as any_zone_category,
       if(${has_instance_category}, instance_category, null)                                      as any_instance_category,
       if(${has_customhouse_title}, customhouse_title, null)                                      as any_customhouse_title,
       if(${has_country_name}, country_name, null)                                                as any_country_name,
       if(${has_area_name}, area_name, null)                                                      as any_area_name,
       if(${has_region_name}, region_name, null)                                                  as any_region_name,
       if(${has_zone_name}, zone_name, null)                                                      as any_zone_name,
       if(${has_instance_group}, instance_group, null)                                            as any_instance_group,
       if(${has_instance_type}, instance_type, null)                                              as any_instance_type,
       if(${has_volume_type}, volume_type, null)                                                  as any_volume_type,
       if(${has_product}, product, null)                                                          as any_product,
       sum(case
               when '${unit}' = '逻辑核' then cores
               when '${unit}' = '逻辑容量' then disk_storage * 1024
               else memory end)                                                                   as begin_inventory,
       sum(case
               when product_type = 'CVM' and inv_detail_type = '用户预扣' then cores
               else 0 end)                                                                        as withhold_inventory_core
from cloud_demand.ads_inventory_health_supply_summary_df ${where}
group by stat_time, any_zone_category, any_instance_category, any_customhouse_title, any_country_name, any_area_name,
         any_region_name,
         any_zone_name, any_instance_group, any_instance_type, any_volume_type, product_type,any_product
