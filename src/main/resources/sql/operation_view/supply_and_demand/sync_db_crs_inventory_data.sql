-- CDB
select stat_time         as stat_time,
       customhouse_title as customhouse_title,
       area_name         as area_name,
       region_name       as region_name,
       zone_name         as zone_name,
       '(空值)'          as volume_type,
       '(空值)'          as instance_type,
       '库存'            as supply_type,
       '数据库'          as product_type,
       'CDB'             as product,
       '(空值)'          as line_type,
       '(空值)'          as material_type,
       '(空值)'          as inv_detail_type,
       '(空值)'          as quota_id,
       0                 as cores,
       0                 as total_num,
       0                 as logic_cores,
       0                 as sale_cores,
       sum(rest_disk)    as disk_storage,
       sum(rest_mem)     as memory
from cloud_demand.dws_cdb_service_level_data
where stat_time = ?
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id
union all
-- CRS
select stat_time         as stat_time,
       customhouse_title as customhouse_title,
       area_name         as area_name,
       region_name       as region_name,
       zone_name         as zone_name,
       '(空值)'          as volume_type,
       '(空值)'          as instance_type,
       '库存'            as supply_type,
       '数据库'          as product_type,
       'CRS'             as product,
       '(空值)'          as line_type,
       '(空值)'          as material_type,
       '(空值)'          as inv_detail_type,
       '(空值)'          as quota_id,
       0                 as cores,
       0                 as total_num,
       0                 as logic_cores,
       0                 as sale_cores,
       0                 as disk_storage,
       sum(rest_mem)     as memory
from cloud_demand.dws_crs_service_level_data
where stat_time = ?
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id