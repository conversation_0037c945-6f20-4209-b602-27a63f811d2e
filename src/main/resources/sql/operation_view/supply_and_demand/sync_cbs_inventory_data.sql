-- 好料-线上可售卖 union all 好料-线下可售卖 union all 差料 union all 呆料
-- 好料-线上可售卖
select add_date                                                      as stat_time,
       '(空值)'                                                      as customhouse_title,
       '(空值)'                                                      as area_name,
       '(空值)'                                                      as region_name,
       SUBSTRING_INDEX(zone_name, '-', 1)                            as zone_name,
       if(volume_type in ('throughput', 'premium'), '高性能', 'SSD') as volume_type,
       '(空值)'                                                      as instance_type,
       '库存'                                                        as supply_type,
       'CBS'                                                         as product_type,
       'CBS'                                                         as product,
       '好料'                                                        as line_type,
       '线上可售卖'                                                  as material_type,
       '(空值)'                                                      as inv_detail_type,
       '(空值)'                                                      as quota_id,
       0                                                             as cores,
       0                                                             as total_num,
       0                                                             as logic_cores,
       0                                                             as sale_cores,
       sum(online_good_stock)                                        as disk_storage,
       0                                                             as memory
from cbs_hawkeye.cbs_safe_stock_by_zone
where add_date = ?
  and position_flag = 1
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id
union all
-- 好料-线下可售卖
select add_date                                                      as stat_time,
       '(空值)'                                                      as customhouse_title,
       '(空值)'                                                      as area_name,
       '(空值)'                                                      as region_name,
       SUBSTRING_INDEX(zone_name, '-', 1)                            as zone_name,
       if(volume_type in ('throughput', 'premium'), '高性能', 'SSD') as volume_type,
       '(空值)'                                                      as instance_type,
       '库存'                                                        as supply_type,
       'CBS'                                                         as product_type,
       'CBS'                                                         as product,
       '好料'                                                        as line_type,
       '线下可售卖'                                                  as material_type,
       '(空值)'                                                      as inv_detail_type,
       '(空值)'                                                      as quota_id,
       0                                                             as cores,
       0                                                             as total_num,
       0                                                             as logic_cores,
       0                                                             as sale_cores,
       sum(offline_stock)                                            as disk_storage,
       0                                                             as memory
from cbs_hawkeye.cbs_safe_stock_by_zone
where add_date = ?
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id
union all
-- 差料
select add_date                                                      as stat_time,
       '(空值)'                                                      as customhouse_title,
       '(空值)'                                                      as area_name,
       '(空值)'                                                      as region_name,
       SUBSTRING_INDEX(zone_name, '-', 1)                            as zone_name,
       if(volume_type in ('throughput', 'premium'), '高性能', 'SSD') as volume_type,
       '(空值)'                                                      as instance_type,
       '库存'                                                        as supply_type,
       'CBS'                                                         as product_type,
       'CBS'                                                         as product,
       '差料'                                                        as line_type,
       '(空值)'                                                      as material_type,
       '(空值)'                                                      as inv_detail_type,
       '(空值)'                                                      as quota_id,
       0                                                             as cores,
       0                                                             as total_num,
       0                                                             as logic_cores,
       0                                                             as sale_cores,
       sum(online_bad_stock)                                         as disk_storage,
       0                                                             as memory
from cbs_hawkeye.cbs_safe_stock_by_zone
where add_date = ?
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id
union all
-- 差料
select add_date                                                      as stat_time,
       '(空值)'                                                      as customhouse_title,
       '(空值)'                                                      as area_name,
       '(空值)'                                                      as region_name,
       SUBSTRING_INDEX(zone_name, '-', 1)                            as zone_name,
       if(volume_type in ('throughput', 'premium'), '高性能', 'SSD') as volume_type,
       '(空值)'                                                      as instance_type,
       '库存'                                                        as supply_type,
       'CBS'                                                         as product_type,
       'CBS'                                                         as product,
       '呆料'                                                        as line_type,
       '(空值)'                                                      as material_type,
       '(空值)'                                                      as inv_detail_type,
       '(空值)'                                                      as quota_id,
       0                                                             as cores,
       0                                                             as total_num,
       0                                                             as logic_cores,
       0                                                             as sale_cores,
       sum(online_idle_stock)                                        as disk_storage,
       0                                                             as memory
from cbs_hawkeye.cbs_safe_stock_by_zone
where add_date = ?
group by stat_time,
         customhouse_title,
         area_name,
         region_name,
         zone_name,
         volume_type,
         instance_type,
         supply_type,
         product_type,
         product,
         line_type,
         material_type,
         inv_detail_type,
         quota_id