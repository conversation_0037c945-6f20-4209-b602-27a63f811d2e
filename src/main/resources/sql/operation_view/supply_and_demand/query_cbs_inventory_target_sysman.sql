SELECT stat_time as add_date,
       volume_type,
       zone_name,
       target_inventory as safe_stock
FROM cloud_demand.suppl_and_demand_cbs_inventory_target AS t
join (
    SELECT max(id) max_id
    FROM cloud_demand.suppl_and_demand_cbs_inventory_target AS a
    where stat_time in (?)
    group by stat_time,
             volume_type,
             zone_name
) as t_max
on t_max.max_id = t.id
where stat_time in (?)
