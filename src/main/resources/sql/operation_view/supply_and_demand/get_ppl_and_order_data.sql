select '${statTime}'                                                        as `stat_time`,
       concat(toString(`year`), '-', if(`month` < 10, concat('0', toString(`month`)),
                                        toString(`month`)))                 as `year_month`,
       `product`,
       `order_number`,
       `ppl_order`,
       `industry_dept`,
       `war_zone`,
       `customer_short_name`,
       `customer_uin`,
       `common_customer_short_name`,
       `project_name`,
       `demand_scene`,
       `customhouse_title`,
       '(空值)'                                                             as `country_name`,
       `area_name`,
       '(空值)'                                                             as `region`,
       `region_name`,
       '(空值)'                                                             as `zone_category`,
       `zone`,
       `zone_name`,
       '(空值)'                                                             as `instance_category`,
       '(空值)'                                                             as `instance_group`,
       `instance_type`,
       `status`,
       `data_source`,
       `order_source`,
       demand_type,
       '当月'                                                               as month_type,
       begin_buy_date,
       end_buy_date,
       sum(if(demand_type = 'RETURN', -`instance_num`, `instance_num`))     as instance_num,
       sum(if(demand_type = 'RETURN', -`total_core`, `total_core`))         as total_core,
       sum(if(demand_type = 'RETURN', -`buy_total_core`, `buy_total_core`)) as buy_total_core,
       sum(if(demand_type = 'RETURN', -`wait_buy_total_core`,
              `wait_buy_total_core`))                                       as wait_buy_total_core
from std_crp.dws_crp_ppl_join_order_version_newest_cf
where product in
      ('CVM&CBS', '弹性MapReduce', 'Elasticsearch Service', '云数据仓库', 'EKS官网', '数据湖DLC', 'CSIG容器平台')
  and demand_type in ('NEW', 'ELASTIC', 'RETURN')
  and source in ('IMPORT', 'COMD_INTERVENE', 'APPLY_AUTO_FILL', 'FORECAST')
  and is_comd != 1
group by stat_time,
    year_month,
    product,
    order_number,
    ppl_order,
    industry_dept,
    war_zone,
    customer_short_name,
    customer_uin,
    common_customer_short_name,
    project_name,
    demand_scene,
    customhouse_title,
    country_name,
    area_name,
    region,
    region_name,
    zone_category,
    `zone`,
    zone_name,
    instance_category,
    instance_group,
    instance_type,
    status,
    data_source,
    order_source,
    demand_type,
    begin_buy_date,
    end_buy_date
having total_core != 0
