select '' as year,
       '' as month,
       customhouse_title as customhouseTitle,
       area_name areaName,
       region_name regionName,
       zone_name zoneName,
       zone_id zoneId,
       gins_category ginsCategory,
       ginsfamily ginsFamily,
       ginstype ginsType,
       cpu_category cpuCategory,
       sum(case when customer_category_new = '内部' then timecpu_diff else billcpu_diff end)  as saleCores
from

    (SELECT end_to_end_cvm_sale_ka_appid_zone_ginstype.stattime,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.customer_category,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.customer_category_new,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.appid as appid,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.appid_type,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.bg_name,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.is_inner,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.sname,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.customhouse_title,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.region_name,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.area_name,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.zone_name,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.zone_id,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.ginsfamily,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.ginsfamily_name,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.ginstype,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.device_type,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.gins_category,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.cpu_count,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.is_good_ginstype,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.cpu_category,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.buy_timecpu,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.rtn_timecpu,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.timecpu_diff,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.buy_billcpu,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.rtn_billcpu,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.billcpu_diff,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.cur_billcpu,
    end_to_end_cvm_sale_ka_appid_zone_ginstype.cur_timecpu
    FROM cloud_demand.end_to_end_cvm_sale_ka_appid_zone_ginstype
    UNION ALL
    SELECT end_to_end_cvm_sale_longtail_zone_ginstype.stattime,
    end_to_end_cvm_sale_longtail_zone_ginstype.customer_category,
    end_to_end_cvm_sale_longtail_zone_ginstype.customer_category_new,
    0 as appid,
    end_to_end_cvm_sale_longtail_zone_ginstype.appid_type,
    end_to_end_cvm_sale_longtail_zone_ginstype.bg_name,
    end_to_end_cvm_sale_longtail_zone_ginstype.is_inner,
    '中长尾' as sname,
    end_to_end_cvm_sale_longtail_zone_ginstype.customhouse_title,
    end_to_end_cvm_sale_longtail_zone_ginstype.region_name,
    end_to_end_cvm_sale_longtail_zone_ginstype.area_name,
    end_to_end_cvm_sale_longtail_zone_ginstype.zone_name,
    end_to_end_cvm_sale_longtail_zone_ginstype.zone_id,
    end_to_end_cvm_sale_longtail_zone_ginstype.ginsfamily,
    end_to_end_cvm_sale_longtail_zone_ginstype.ginsfamily_name,
    end_to_end_cvm_sale_longtail_zone_ginstype.ginstype,
    end_to_end_cvm_sale_longtail_zone_ginstype.device_type,
    end_to_end_cvm_sale_longtail_zone_ginstype.gins_category,
    end_to_end_cvm_sale_longtail_zone_ginstype.cpu_count,
    end_to_end_cvm_sale_longtail_zone_ginstype.is_good_ginstype,
    end_to_end_cvm_sale_longtail_zone_ginstype.cpu_category,
    end_to_end_cvm_sale_longtail_zone_ginstype.buy_timecpu,
    end_to_end_cvm_sale_longtail_zone_ginstype.rtn_timecpu,
    end_to_end_cvm_sale_longtail_zone_ginstype.timecpu_diff,
    end_to_end_cvm_sale_longtail_zone_ginstype.buy_billcpu,
    end_to_end_cvm_sale_longtail_zone_ginstype.rtn_billcpu,
    end_to_end_cvm_sale_longtail_zone_ginstype.billcpu_diff,
    end_to_end_cvm_sale_longtail_zone_ginstype.cur_billcpu,
    end_to_end_cvm_sale_longtail_zone_ginstype.cur_timecpu
    FROM cloud_demand.end_to_end_cvm_sale_longtail_zone_ginstype)

where device_type = 'CPU'
-- 排除了战略客户部
  and appid not in (1251021019,1251203745,1251246104,1251317268,1251369785,1251473052,1251479438,1251524319,1251542863,1251624741,1251625956,1251726671,1251743181,1251885757,1251890769,1251911170,1252081001,1252106939,1252257541,1252293814,1252317822,1252347920,1252452736,1252555112,1252693259,1252726293,1252729387,1252755701,1252768022,1253127976,1253177085,1253428821,1253539814,1253690380,1253792912,1253964652,1254236265,1254246377,1255305554,1255395388,1255563463,1255606608,1255607631,1255644275,1255747144,1255940660,1256053731,1256121031,1256212241,1256359555,1256369225,1256641228,1256676069,1256701535,1256756216,1256830457,1256883318,1257181305,1257326524,1257458877,1257459638,1257471003,1257673840,1257704684,1257811752,1257811798,1257933671,1258572856,1258678869,1258876180,1259038230,1259068930,1259068968,1259068998,1259069317,1259069382,1259069406,1259069428,1259069451,1259069488,1259119906,1259227829,1300321457,1300456045,1300456055,1300456095,1300456097,1300456115,1300456135,1300456142,1300456152,1300456157,1300457808,1300466638,1300635550,1300860423,1301152170,1301189748,1301229000,1301864055,1301982183,1302451400,1302493068,1302592199,1302686331,1302924058,1303183042,1303188486,1303697587,1304125667,1304252906,1304285275,1304408131,1304452184,1304589815,1304631925,1304774729,1304810127,1304816572,1304975707,1305116985,1305116987,1305116988,1305117011,1305134745,1305173352,1305249549,1305250531,1305270734,1305275684,1305463203,1305574582,1305657741,1305880627,1305950504,1306014067,1306040087,1306044604,1306367951,1306393749,1306413518,1306662331,1306681469,1306827112,1306924377,1307001368,1307022421,1307051582,1307059987,1307392909,1307756307,1307813532,1307824561,1308009133,1308104368,1308328017,1308381771,1308383062,1308476845,1308701810,1309284905,1309309140,1309315942,1309356444,1309982160,1310316113,1310494239,1310603472,1311483431,1311487792,1312387170,1312721990,1312796392,1312815093,1313002395,1313003034,1313126154,1313506393,1313740832,1313768743,1313941012,1314050613,1314991419,1315378974)
  and stattime between ? and ?
group by year, month, customhouse_title,
    area_name, region_name, zone_name, zone_id, gins_category,
    ginsfamily, ginstype, cpuCategory

