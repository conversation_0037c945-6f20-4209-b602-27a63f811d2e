-- 安全库存-未来周峰算法(头部非战略客户部数据部分）
select begin_buy_date,
       win_rate,
       region_name,
       zone_name,
       instance_type,
       case when demand_type = 'RETURN' then -total_core else total_core end `logic_num`
from dwd_crp_ppl_item_cf
where source = 'IMPORT'
  and product = 'CVM&CBS'
  and demand_type <> 'ELASTIC'
  and industry_dept <> '战略客户部'
  and begin_buy_date between :startDate and :endDate

