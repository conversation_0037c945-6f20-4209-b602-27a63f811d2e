select stat_time,
       customhouse_title, area_name, region_name, zone_name,
       instance_type,
       sum(max_cpucore - min_cpucore) `bufferCores`
from dwd_txy_buffer_scale_zone_instance_type_df
where stat_time >= '2023-07-01'
  and stat_time between :start and :end
  and paymode = '2'
  and app_role not in ('LH')
  and product_type = 'CVM'
group by stat_time, customhouse_title, area_name, region_name, zone_name, instance_type