-- 安全库存-历史周峰算法
select week_year year, week_month month, week, product,
       customhouse_title, area_name, region_name, zone_name,
       instance_type,
       max(sum) logic_num,
       max(bill_sum) bill_num,
       max(service_sum) service_num
 from (
         select stat_time, week_year, week_month, week, product,
                customhouse_title, area_name, region_name, zone_name,
                instance_type,
                sum(case when product = 'GPU' and biz_range_type = '外部业务' then change_bill_gpu_from_last_week
                    when product = 'GPU' and biz_range_type = '内部业务' then change_service_gpu_from_last_week
                    when product <> 'GPU' and biz_range_type = '外部业务' then change_bill_core_from_last_week
                    when product <> 'GPU' and biz_range_type = '内部业务' then change_service_core_from_last_week
                    else 0 end) sum,
                 sum(case when product = 'GPU' then change_bill_gpu_from_last_week
                    when product = 'CVM' then change_bill_core_from_last_week
                    else 0 end) bill_sum,
                 sum(case when product = 'GPU' then change_service_gpu_from_last_week
                    when product = 'CVM' then change_service_core_from_last_week
                    else 0 end) service_sum
         from dwd_txy_scale_df
         where stat_time between :start and :end
             and customer_tab_type in (:customerTabTypeList)
             and product = 'CVM' -- 本次只有CVM
             and paymode_range_type = '包年包月'
             ${UIN_CONDITION} -- 支持自己筛选或排除uin
           ${CUSTOMER_NAME_CONDITION}  -- 支持自己筛选或排除客户名称/简称
         group by stat_time, week_year, week_month, week, product,
                  customhouse_title, area_name, region_name, zone_name,
                  instance_type) t
group by year, month, week, product,
         customhouse_title, area_name, region_name, zone_name,
         instance_type