select campus,
       quota_device_class device_type,
       DeviceFamilyName device_family,
       quota_use_time,
       erp_actual_date,
       xy_create_time,
       quota_create_time,
       cloud_delivery_time,
       date_diff('h', xy_create_time, toDateTime(quota_create_time)) / 24            create_time_diff,
       if(toDate(quota_use_time) >= toDate(erp_actual_date), '如期交付', '延期交付') delivery_status,
       abs(date_diff('h', toDate(quota_create_time), toDate(erp_actual_date))) / 24     delivery_days,
       count(1) num
from cubes.demandMarket
where DAY = ?
  and toDate(erp_actual_date) between ? and ?
  and cloud_business_type = '云业务'
  and quota_plan_product_name in ('腾讯云CBS', '腾讯云-CBS')
group by campus, device_type, quota_use_time, erp_actual_date, xy_create_time, quota_create_time, cloud_delivery_time, DeviceFamilyName