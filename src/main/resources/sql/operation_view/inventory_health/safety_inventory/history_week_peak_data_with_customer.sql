-- 安全库存-历史周峰算法
select week_year year, week_month month, week, product,
       customhouse_title,
       industry_dept,customer_short_name,uin,
       max(sum) logic_num,
       max(bill_sum) bill_num,
       max(service_sum) service_num
 from (
         select stat_time, week_year, week_month, week, product,
                customhouse_title,
                industry_dept,
                if(customer_tab_type = '中长尾客户','中长尾',customer_short_name) as customer_short_name,
                if(customer_tab_type = '中长尾客户',0,uin) as uin,
                sum(case when product = 'GPU' and biz_range_type = '外部业务' then change_bill_gpu_from_last_week
                    when product = 'GPU' and biz_range_type = '内部业务' then change_service_gpu_from_last_week
                    when product <> 'GPU' and biz_range_type = '外部业务' then change_bill_core_from_last_week
                    when product <> 'GPU' and biz_range_type = '内部业务' then change_service_core_from_last_week
                    else 0 end) sum,
                 sum(case when product = 'GPU' then change_bill_gpu_from_last_week
                    when product = 'CVM' then change_bill_core_from_last_week
                    else 0 end) bill_sum,
                 sum(case when product = 'GPU' then change_service_gpu_from_last_week
                    when product = 'CVM' then change_service_core_from_last_week
                    else 0 end) service_sum
         from std_crp.dwd_txy_scale_df
         where stat_time between :start and :end
             and product = 'CVM' -- 本次只有CVM
             and paymode_range_type = '包年包月'
         group by stat_time, week_year, week_month, week, product,customhouse_title,
                  industry_dept,customer_short_name,uin) t
group by year, month, week, product,
    customhouse_title,industry_dept,customer_short_name,uin