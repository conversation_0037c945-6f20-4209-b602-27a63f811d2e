select
    device_type,
    xy_industry,
    quota_campus_name,
    xy_customer_name,
    quota_create_time,
    quota_id,
    sla_date_expect,
    substr(quota_use_time,1,10) as quota_use_time ,
    sum(cpu_logic_core) as demand_core_num,
    sum(if(produce_status != '完成',cpu_logic_core,0)) as no_arrival_core_num
from demandMarket
where DAY = :statTime
  ${START_END}
  and cloud_business_type = '云业务'
  and quota_plan_product_name = '腾讯云CVM'
  and cpu_logic_core != 0
  and produce_status <> '需求单已作废'
  ${CUSTOMHOUSE_TITLE}
group by
    device_type,
    xy_industry,
    quota_campus_name,
    xy_customer_name,
    quota_id,
    sla_date_expect,
    quota_use_time,
    quota_create_time