select zone_name,
       instance_type,
       instance_model,
       sum(startBill)            start_bill,
       sum(startServe)           start_serve,
       sum(endBill)              end_bill,
       sum(endServe)             end_serve,
       (end_bill - start_bill)   change_bill,
       (end_serve - start_serve) change_serve
from (select zone_name,
             instance_type,
             instance_model,
             sum(cur_bill_core)    startBill,
             sum(cur_service_core) startServe,
             0                     endBill,
             0                     endServe
      from std_crp.dwd_txy_scale_df
      where stat_time = ?
        and product = 'CVM'
        and zone_name in (?)
        and instance_type in (?)
        and customer_tab_type in (?)
      group by zone_name, instance_type, instance_model
      union all
      select zone_name,
             instance_type,
             instance_model,
             0                     startBill,
             0                     startServe,
             sum(cur_bill_core)    endBill,
             sum(cur_service_core) endServe
      from std_crp.dwd_txy_scale_df
      where stat_time = ?
        and product = 'CVM'
        and zone_name in (?)
        and instance_type in (?)
        and customer_tab_type in (?)
      group by zone_name, instance_type, instance_model) t
group by zone_name, instance_type, instance_model
