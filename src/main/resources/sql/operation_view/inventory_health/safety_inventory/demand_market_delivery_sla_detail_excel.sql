select quota_id,
       physics_pcCode,
       quota_device_class,
       campus,
       xy_industry,
       xy_customer_name,
       quota_use_time,
       xy_create_time,
       quota_create_time,
       erp_actual_date,
       xy_create_time,
       quota_create_time,
       round(date_diff('h', xy_create_time, toDateTime(quota_create_time)) / 24, 1)  xy_approval_days,
       28                                                                            sla,
       erp_actual_date,
       if(toDate(quota_use_time) >= toDate(erp_actual_date), '如期交付', '延期交付') delivery_status,
       abs(date_diff('h', toDate(quota_create_time), toDate(erp_actual_date))) / 24  delivery_days,
       cloud_delivery_time,
       1                                                                             num,
       cpu_logic_core                                                                core
from cubes.demandMarket
where DAY = ?
  and toDate(erp_actual_date) between ?
  and ?
  and campus in (?)
  and device_type in (?)
  and cloud_business_type = '云业务'
  and quota_plan_product_name = '腾讯云CVM'