-- 安全库存-未来周峰算法(中长尾数据部分）
select holiday_year                                                 `year`,
       holiday_month                                                `month`,
       holiday_week                                                 `week`,
       'CVM' `product`,
       region_name                                                  `region_name`,
       zone_name                                                    `zone_name`,
       gins_family                                                  `instance_type`,
       sum(case when type = 'RET' then -core_num else core_num end) `logic_num`
from ppl_forecast_predict_result_latest
where (holiday_year, holiday_week) in (:yearWeekList) -- 取对应的时间范围
group by year, month, week, product, zone_name, instance_type;