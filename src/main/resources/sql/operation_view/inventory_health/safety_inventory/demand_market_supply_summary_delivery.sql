-- 安全库存 - 供应汇总表 - 到货信息查询
select 'CVM'               product_type,
       quota_zone_name                city_zone,
       quota_campus_name              campus_name,
       device_type,
       xy_industry,
       xy_customer_name,
       quota_id,
       cloud_delivery_time total_delivery_time,
       toDate(quota_use_time)      expect_delivery_date,
       sla_date_expect     promise_delivery_time,
       toDate(xy_create_time)      submit_time,
       produce_status,
       count()             total_num,
       sum(cpu_logic_core) cores,
       cpu_logic_core logic_cores
from cubes.demandMarket
where DAY = ?
  and cloud_business_type = '云业务'
  and quota_plan_product_name = '腾讯云CVM'
  and (cloud_delivery_time >= '2023-07-01' or sla_date_expect >= '2023-07-01' or quota_use_time >= '2023-07-01')
group by city_zone, campus_name, device_type, cloud_delivery_time, expect_delivery_date,
    promise_delivery_time, submit_time, cpu_logic_core, produce_status, xy_industry, xy_customer_name,quota_id