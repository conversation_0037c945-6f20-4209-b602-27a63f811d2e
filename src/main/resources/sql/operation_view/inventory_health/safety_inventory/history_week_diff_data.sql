-- 安全库存-历史周净增算法
select year, month, week, product,
       customhouse_title, area_name, region_name, zone_name,
       instance_type,
       sum(case when product = 'GPU' and biz_range_type = '外部业务' then change_bill_gpu_from_last_week
                when product = 'GPU' and biz_range_type = '内部业务' then change_service_gpu_from_last_week
                when product <> 'GPU' and biz_range_type = '外部业务' then change_bill_core_from_last_week
                when product <> 'GPU' and biz_range_type = '内部业务' then change_service_core_from_last_week
                else 0 end) logic_num
from dwd_txy_scale_df
where stat_time in (:statTimes)
    and customer_tab_type in (:customerTabTypeList)
    and paymode_range_type = '包年包月'
    and product = 'CVM' -- 本次只有CVM
    ${UIN_CONDITION} -- 支持自己筛选或排除uin
    ${CUSTOMER_NAME_CONDITION}  -- 支持自己筛选或排除客户名称/简称
group by year, month, week, product,
         customhouse_title, area_name, region_name, zone_name,
         instance_type
