-- 直接从item拿预测数据
SELECT gins_family                                             `instance_type`,
       '' `instance_model`,
       region_name                                             `region_name`,
       zone_name                                               `zone_name`,
       holiday_week_start                                      `begin_buy_date`,
       holiday_week_end                                        `end_buy_date`,
       '中长尾' `industry_dept`,
       case when type = 'RET' then -core_num else core_num end `total_core`, -- 退回取负数
       'FORECAST' `source`,
       source_type                                             forecast_model_source_type
from ppl_forecast_predict_result_latest

union all

SELECT a.instance_type                                                           `instance_type`,
       a.instance_model                                                          `instance_model`,
       a.region_name                                                             `region_name`,
       a.zone_name                                                               `zone_name`,
       a.begin_buy_date                                                          `begin_buy_date`,
       a.end_buy_date                                                            `end_buy_date`,
       b.industry_dept                                                           `industry_dept`,
       case when demand_type = 'RETURN' then -a.total_core else a.total_core end `total_core`, -- 退回取负数
       'IMPORT' `source`,
       ''                                                                        forecast_model_source_type
from ppl_item a
         join ppl_order b on a.ppl_order = b.ppl_order
where a.deleted = 0
  and b.deleted = 0
  and b.source = 'IMPORT'
  and a.ppl_order like 'PN%'
  and product = 'CVM&CBS';