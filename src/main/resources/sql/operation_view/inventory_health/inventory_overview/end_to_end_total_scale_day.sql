select
    zone_name,
    device_model as device_type,
    category,
    sale_type,
    item,
    sum(cpu_count) as core_num
from public.end_to_end_zone_device_model
where data_date = ?-- 20240305
  -- 分类一 & 售卖类型 & 分类二
  and product_type = 'CVM' -- 产品类型：CVM or GPU
  and cpu_count != 0
  and zone_name in (?)
  and device_model in (?)
group by
    zone_name,
    device_model,
    category,
    sale_type,
    item