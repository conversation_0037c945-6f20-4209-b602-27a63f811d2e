stage: "DEV" # 环境：DEV为开发环境，TEST为测试环境，PROD为正式环境

yunti:
  app-name: "cloud-demand-lab" # 应用服务名
  web:
    port: 80 # 提供服务的端口号
    context-path: "/cloud-demand-lab" # 服务接口根路径
  rainbow:
    enabled: true # 是否启用七彩石配置
    app-id: "c8269224-03c9-4d55-a081-589c74e09f5e" # 七彩石项目id
    main-group: "${stage}.${yunti.app-name}" # 读取的七彩石配置分组名
  jsonrpc-url: "http://xor.oa.com/jsonrpc"



management:
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      app: "${yunti.app-name}"