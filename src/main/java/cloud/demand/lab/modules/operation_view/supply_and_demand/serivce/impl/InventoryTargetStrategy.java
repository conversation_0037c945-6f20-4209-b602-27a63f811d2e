package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetContext;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryTargetService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/4/7 16:47
 */
@Service
public class InventoryTargetStrategy implements InitializingBean {

    @Resource
    private List<InventoryTargetService> inventoryTargetServices;

    private final Map<String, InventoryTargetService> inventoryTargetMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        if (ListUtils.isEmpty(inventoryTargetServices)) {
            return;
        }
        // bean初始化完成后，扫描所有的inventoryTargetServices，注册进来
        for (InventoryTargetService service : inventoryTargetServices) {
            inventoryTargetMap.put(service.getProductCategory(), service);
        }
    }

    public InventoryTargetContext initContext(InventoryTargetReq req) {
        InventoryTargetContext context = new InventoryTargetContext();
        context.setTargetMap(queryInventoryTarget(req));
        context.setMatchFlag(matchFlag(req));
        return context;
    }

    public Map<String, InventoryTargetVO> queryInventoryTarget(InventoryTargetReq req) {
        InventoryTargetService service = getService(req.getProductCategory());
        return service.getInventoryTarget(req);
    }

    public Boolean matchFlag(InventoryTargetReq req) {
        InventoryTargetService service =  getService(req.getProductCategory());
        return service.matchFlag(req);
    }

    private InventoryTargetService getService(String productCategory) {
        InventoryTargetService service = inventoryTargetMap.get(productCategory);
        if (Objects.isNull(service)) {
            throw new BizException("未找到对应的productCategory:" + productCategory);
        }
        return service;
    }
}
