package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplAndDemandInventoryTargetDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplAndDemandInventoryTargetDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryTargetService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:26
 */
@Service
public class InventoryTargetCvmServiceImpl implements InventoryTargetService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Override
    public Map<String, InventoryTargetVO> getInventoryTarget(InventoryTargetReq req) {
        List<String> statTime = req.getStatTime();
        List<SupplAndDemandInventoryTargetDfDO> data = ListUtils.newArrayList();
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        if (ListUtils.contains(statTime, item -> StringUtils.equals(item, today))) {
            //获取今天的数据
            List<SupplAndDemandInventoryTargetDO> targetList = demandDBHelper.getAll(SupplAndDemandInventoryTargetDO.class,
                    " where product = ? order by id asc", getProductCategory());
            List<SupplAndDemandInventoryTargetDfDO> toDayList = ListUtils.transform(targetList, item -> SupplAndDemandInventoryTargetDfDO.transform(item, LocalDate.now()));
            data.addAll(toDayList);
            //去掉今天的日期
            statTime.remove(today);
        }
        if (ListUtils.isNotEmpty(statTime)) {
            data.addAll(ckcldDBHelper.getAll(SupplAndDemandInventoryTargetDfDO.class,
                    "where stat_time in (?) and product = ? order by id asc", statTime, getProductCategory()));
        }
        //key: statTime@product@instanceType@regionName
        return ListUtils.toMap(data,k -> k.getInventoryTargetGroupKey(), v -> {
                    InventoryTargetVO inventoryTargetVO = new InventoryTargetVO(v.getInventoryTargetGroupKey(),BigDecimal.valueOf(v.getInventoryTarget()));
                    return inventoryTargetVO;
                });
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CVM.getName();
    }


    public Boolean matchFlag(InventoryTargetReq req) {
        List<String> zoneName = req.getZoneName();
        List<String> dims = req.getDims();
        if (ListUtils.isEmpty(dims) || ListUtils.isNotEmpty(zoneName) || ListUtils.contains(dims, item -> StringUtils.equals(item, "zoneName"))) {
            return Boolean.FALSE;
        }
        if ((dims.contains("instanceType") && dims.contains("regionName")) || dims.contains("regionName")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
