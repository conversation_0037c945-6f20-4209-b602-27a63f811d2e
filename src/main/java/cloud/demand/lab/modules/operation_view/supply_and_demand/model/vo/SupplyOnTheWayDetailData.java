package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;


import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


@Data
public class SupplyOnTheWayDetailData {

    /**
     * 切片日期
     */
    private String statTime;

    /**
     * 产品
     */
    private String productType;

    /**
     * 产品
     */
    private String product;

    /**
     * 规划产品
     */
    private String quotaPlanProductName;

    /**
     * 预约单号
     */
    private String quotaId;

    /**
     * 需求campus
     */
    private String quotaCampusName;

    /**
     * 当前匹配campus
     */
    private String campus;

    /**
     * 需求Region
     */
    private String quotaRegionName;

    /**
     * 当前匹配Module
     */
    private String module;

    /**
     * 行业
     */
    private String xyIndustry;

    /**
     * 客户名称
     */
    private String xyCustomerName;

    /**
     * 是否假机位
     */
    private String isFakePosition;

    /**
     * 可用区类型
     */
    private String zoneCategory;

    /**
     * 机型类型
     */
    private String instanceCategory;

    private String quotaDeviceClass;

    /**
     * 境内外
     */
    private String customhouseTitle;

    /**
     * 国家
     */
    private String countryName;

    /**
     * 地域
     */
    private String regionName;

    /**
     * 区域
     */
    private String areaName;

    /**
     * 可用区
     */
    private String zoneName;

    /**
     * 机型族
     */
    private String instanceGroup;

    /**
     * 实例类型
     */
    private String instanceType;

    private String volumeType;

    /**
     * 是否有风险
     */
    private Integer IsGap;

    /**
     * 交付月份
     */
    private String slaMonth;


    private Integer supplyCore;

    private BigDecimal amount;

    /**
     * 台数
     */
    private Integer cpuNum;

    private BigDecimal instanceNum;

    private String risk;

    public static SupplyOnTheWayDetailData merge(List<SupplyOnTheWayDetailData> list,List<String> dims) {
        SupplyOnTheWayDetailData ret = new SupplyOnTheWayDetailData();
        if(ListUtils.isEmpty(list)){
            return ret;
        }
        SupplyOnTheWayDetailData first = list.get(0);
        ret.setStatTime(first.getStatTime());
        for(String dim:dims) {
            try {
                Field field = SupplyOnTheWayDetailData.class.getDeclaredField(dim);
                field.setAccessible(true);
                field.set(ret,field.get(first));
            } catch (NoSuchFieldException  | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        ret.setCpuNum(list.stream().map(o -> o.getCpuNum()).filter(o -> Objects.nonNull(o)).reduce(0, Integer::sum));
        ret.setSupplyCore(list.stream().map(o -> o.getSupplyCore()).filter(o -> Objects.nonNull(o)).reduce(0, Integer::sum));
        return ret;
    }

}
