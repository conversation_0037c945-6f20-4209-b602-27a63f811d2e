package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/27 16:54
 */
@Getter
@AllArgsConstructor
public enum ZoneCategoryEnum {

    WITHDRAW("WIT<PERSON><PERSON><PERSON>","已收敛可用区"),
    SPECIAL("SPECIAL","特殊专区"),
    PRINCIPAL("PRINCIPAL","主力可用区"),
    SECONDARY("SECONDARY","辅助可用区"),
    WITHDRAWING("WITHDRAWING","待收敛可用区"),
    UNDEFINED("undefined","未分类"),
    ;

    private final String type;

    private final String typeName;


    public static ZoneCategoryEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (ZoneCategoryEnum value : values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }

    public static ZoneCategoryEnum getByTypeName(String typeName) {
        if (typeName == null) {
            return null;
        }
        for (ZoneCategoryEnum value : values()) {
            if (Objects.equals(value.getTypeName(), typeName)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        ZoneCategoryEnum e = getByType(type);
        return e == null ? "" : e.getTypeName();
    }

    public static String getTypeByTypeName(String typeName) {
        ZoneCategoryEnum e = getByTypeName(typeName);
        return e == null ? "" : e.getType();
    }
}
