package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DemandTrendReq extends DemandCommonReq {

    @NotBlank(message = "开始版本不能为空")
    private String startStatTime;

    @NotBlank(message = "结束版本不能为空")
    private String endStatTime;

    private String startYearMonth;

    private String endYearMonth;

    public List<String> getScaleStatTime(){
        List<String> statTime = ListUtils.newArrayList();
        for (int i = 1; i <= 4; i++) {
            //过去4个月的规模
            statTime.add(LocalDate.now().minusMonths(i).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        for (int i = 0; i <= 3; i++) {
            //过去同期
            statTime.add(LocalDate.now().minusYears(1).plusMonths(i).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        return statTime;
    }



}
