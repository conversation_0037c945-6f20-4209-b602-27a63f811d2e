package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.Data;
import report.utils.anno.WhereReport;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/26 12:00
 */
@Data
public class SupplyDemandHedgingReq {

    private String productCategory = "CVM";

    private List<String> product;

    private String startStatTime;

    private String endStatTime;

    private String statTime;

    @NotBlank(message = "开始年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "开始年月格式不正确")
    private String startYearMonth;

    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束年月格式不正确")
    @NotBlank(message = "结束年月不能为空")
    private String endYearMonth;

    @WhereReport
    private List<String> zoneCategory;

    @WhereReport
    private List<String> instanceCategory;

    @WhereReport
    private List<String> instanceGroup;

    @WhereReport
    private List<String> instanceType;

    @WhereReport
    private List<String> volumeType;

    private List<String> isInner;//内外部：0-外部 1-内部

    @WhereReport
    private List<String> customhouseTitle;

    @WhereReport
    private List<String> countryName;

    @WhereReport
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;

    @WhereReport
    private List<String> lineType;

    @WhereReport
    private List<String> materialType;

    @WhereReport
    private List<String> invDetailType;

    private String demandCaliber = "人工校准";

    private String orderRange;

    private boolean excludeFinishDemand = true;

    /**
     * zoneCategory,instanceCategory,customhouseTitle,countryName,areaName,regionName,zoneName,instanceGroup,instanceType
     */
    private List<String> dims;

    private boolean overView = false;

    private String unit = "逻辑核";

    private List<String> projectType; //项目类型

    private List<String> dbStorageType;//数据库存储类型

}
