package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model;

import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.model.PlanDetailVO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

/**
 * cvm库存
 */
@Table("report_plan_detail")
@Data
public class CvmInventoryVO extends PlanDetailVO {

    @Column(value = "year_month", computed = "concat(year(stat_time), '-', month(stat_time))")
    private String yearMonth;

    /**
     * 母机机型<br/>Column: [device_name]
     */
    @Column(value = "device_type")
    private String deviceName;

    /**
     * 可用区名<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 地域名<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 地区名<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 好差呆类型，其中线下库存后处理
     */
    @Column(value = "materialType", computed = "(CASE WHEN indicator_code='d1' THEN '好料'\n" +
            "         WHEN indicator_code='d2' THEN '差料'\n" +
            "         WHEN indicator_code='d3' THEN '呆料'\n" +
            "         ELSE '' END)")
    private String materialType;

    /**
     * 线上核心数
     */
    @Column(value = "onlineCores", computed =
            "SUM((CASE WHEN indicator_code IN ('d1','d2','d3') THEN cores ELSE 0 END))")
    private BigDecimal onlineCores;

    /**
     * 线下核心数
     */
    @Column(value = "offlineCores",
            computed = "SUM((CASE WHEN indicator_code IN ('e4','e8') THEN cores ELSE 0 END))")
    private BigDecimal offlineCores;

    /**
     * 物理机单台核心数
     */
    @Column(value = "logicCpuCore", computed = "MAX(logic_cpu_core)")
    private BigDecimal logicCpuCore;

    /**
     * 转换为OperationViewDetailDO
     */
    public ReportOperationViewDetailDO toOperationViewDO(boolean isForNewestInv) {
        ReportOperationViewDetailDO detailDO = new ReportOperationViewDetailDO();
        detailDO.setProductType(ProductTypeEnum.CVM.getCode());
        detailDO.setMaterialType(materialType);
        detailDO.setCustomhouseTitle(customhouseTitle);
        detailDO.setAreaName(areaName);
        detailDO.setRegionName(regionName);
        detailDO.setZoneName(zoneName);
        detailDO.setDeviceType(deviceName);
        if (isForNewestInv) {
            BigDecimal onlineCores = this.onlineCores == null ? BigDecimal.ZERO : this.onlineCores;
            BigDecimal offlineCores = this.offlineCores == null ? BigDecimal.ZERO : this.offlineCores;
            detailDO.setInvNewestTotal(onlineCores.add(offlineCores));
            detailDO.setInvNewestOnline(onlineCores);
            detailDO.setInvNewestOffline(offlineCores);
        }
        detailDO.setCpuLogicCore(logicCpuCore == null ? 0 : logicCpuCore.intValue());
        return detailDO;
    }

    public String getGroupK() {
        return String.join("-",
                yearMonth, deviceName, materialType, customhouseTitle, areaName, regionName, zoneName);
    }

    /**
     * 除yearMonth外的基础属性，这里获取这个k是为了去已经生成的DetailMap中找到对应的值
     */
    public String getBaseGroupK() {
        return String.join("-",
                ProductTypeEnum.CVM.getCode(), materialType, customhouseTitle, areaName, regionName, zoneName,
                deviceName);
    }

    /**
     * 复制一条具有基本属性的VO
     */
    public CvmInventoryVO copyOne() {
        CvmInventoryVO cvmInventoryVO = new CvmInventoryVO();
        cvmInventoryVO.setYearMonth(this.yearMonth);
        cvmInventoryVO.setDeviceName(this.deviceName);
        cvmInventoryVO.setZoneName(this.zoneName);
        cvmInventoryVO.setRegionName(this.regionName);
        cvmInventoryVO.setAreaName(this.areaName);
        cvmInventoryVO.setCustomhouseTitle(this.customhouseTitle);
        cvmInventoryVO.setMaterialType(this.materialType);
        cvmInventoryVO.setLogicCpuCore(this.logicCpuCore);
        return cvmInventoryVO;
    }
}
