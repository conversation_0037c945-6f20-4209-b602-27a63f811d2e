package cloud.demand.lab.modules.operation_view.operation_view_old.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("device_sla")
public class DeviceSlaDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "startTime")
    private Date startTime;

    @Column(value = "endTime")
    private Date endTime;

    @Column(value = "deviceType")
    private String deviceType;

    @Column(value = "mainlandSla")
    private Integer mainlandSla;

    @Column(value = "overseasSla")
    private Integer overseasSla;

    @Column(value = "updateTime")
    private Date updateTime;

}
