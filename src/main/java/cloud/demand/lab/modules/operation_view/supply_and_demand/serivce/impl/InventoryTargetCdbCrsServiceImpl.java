package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.CdbCrsInventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryTargetService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:26
 */
@Service
public class InventoryTargetCdbCrsServiceImpl implements InventoryTargetService {

    private final List<String> supportDims = ListUtils.newArrayList("customhouseTitle", "countryName", "areaName", "regionName", "zoneName");

    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Resource
    private FillerService fillerService;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public Map<String, InventoryTargetVO> getInventoryTarget(InventoryTargetReq req) {

        List<CdbCrsInventoryTargetVO> data = getInventoryTarget(req.getProduct(), req.getStatTime(), req.getUint());

        Map<String, Boolean> setFlagMap = new HashMap<>();
        boolean dimsContainsZoneName = ListUtils.contains(req.getDims(), o -> StringUtils.equals(o, "zoneName"));
        for (CdbCrsInventoryTargetVO item : data) {
            String key = StringUtils.joinWith("@", item.getStatTime(),item.getProduct(), item.getRegionName());
            if (!dimsContainsZoneName && StringUtils.equals(item.getProduct(), "CDB") && StringUtils.equals(item.getRegionName(), "上海") && !setFlagMap.getOrDefault(key, Boolean.FALSE)) {
                //CDB：拼多多专区：上海地域，DB库存阈值为200TB。这里上海可用区只设置一次，所以用setPinDuoDuo标记是否已经设置过
                setFlagMap.put(key, Boolean.TRUE);
                item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(200), new BigDecimal(1024)));
                continue;
            }
            if (StringUtils.equals(item.getCustomhouseTitle(), "境内") && !StringUtils.equals(item.getRegionName(), "上海") &&  StringUtils.equals(item.getZoneCategory(), "主力园区")) {
                //国内主力园区，库存阈值为30TB
                item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(30), new BigDecimal(1024)));
                continue;
            }
            if (StringUtils.equals(item.getCustomhouseTitle(), "境内") && !StringUtils.equals(item.getRegionName(), "上海") && !StringUtils.equals(item.getZoneCategory(), "主力园区")) {
                if (StringUtils.equals(item.getProduct(), "CDB")) {
                    //CDB:国内非主力园区，库存阈值为6TB
                    item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(6), new BigDecimal(1024)));
                }
                if (StringUtils.equals(item.getProduct(), "CRS")) {
                    //CRS:国内非主力园区，库存阈值为6TB
                    item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(5), new BigDecimal(1024)));
                }
                continue;
            }

            if (!dimsContainsZoneName && StringUtils.equals(item.getCustomhouseTitle(), "境外") && !setFlagMap.getOrDefault(key, Boolean.FALSE)) {
                if (StringUtils.equals(item.getProduct(), "CDB")) {
                    //境外，库存阈值为6TB
                    item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(6), new BigDecimal(1024)));
                }
                if (StringUtils.equals(item.getProduct(), "CRS")) {
                    //境外，库存阈值为5TB
                    item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(5), new BigDecimal(1024)));
                }
                setFlagMap.put(key, Boolean.TRUE);
            }
        }

        List<String> dims = new ArrayList<>(req.getDims());
        dims.removeIf(item -> !supportDims.contains(item));
        dims.add("statTime");
        List<String> sumFieldList = ListUtils.newArrayList("inventoryTarget", "stockThreshold");
        Map<String, InventoryTargetVO> retMap = new HashMap<>();
        ListUtils.groupBy(data, item -> GroupUtils.getDimsGroupKey(item, dims))
                .forEach((key, value) -> {
                    CdbCrsInventoryTargetVO vo = GroupUtils.mergeList(value, dims, sumFieldList);
                    String groupKey = vo.getInventoryTargetGroupKey();
                    InventoryTargetVO inventoryTargetVO = new InventoryTargetVO(groupKey, vo.getInventoryTarget(), vo.getStockThreshold());
                    retMap.put(groupKey, inventoryTargetVO);
                });

        return retMap;
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.DB.getName();
    }

    @Override
    public Boolean matchFlag(InventoryTargetReq req) {
        List<String> dims = req.getDims();
        if (ListUtils.contains(dims, item -> !ListUtils.contains(supportDims, o -> StringUtils.equals(item, o)))) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private List<CdbCrsInventoryTargetVO> getInventoryTarget(List<String> productList, List<String> statTime, String unit) {
        //获取数据库内存容量(目前只考虑一个规划产品一个机型
        Map<String, BigDecimal> deviceMap = demandDBHelper.getAll(BasDeviceMemoryDO.class)
                .stream().collect(Collectors.toMap(BasDeviceMemoryDO::getPlanProductName, BasDeviceMemoryDO::getGbSaleMemory, (k1, k2) -> k1));

        if (ListUtils.isEmpty(productList)) {
            productList = ListUtils.newArrayList("CDB", "CRS");
        }
        List<CdbCrsInventoryTargetVO> ret = ListUtils.newArrayList();
        for (String product : productList) {
            if (StringUtils.equals(product, "CDB")) {
                List<CdbCrsInventoryTargetVO> tempList = getCdbInventoryTarget(statTime);
                tempList.forEach(item -> item.setProduct(product));
                if (StringUtils.equals(unit, UnitEnum.NUM.getName())) {
                    memoryToNum(tempList, deviceMap.getOrDefault(product, BigDecimal.ZERO));
                }
                ret.addAll(tempList);
            }
            if (StringUtils.equals(product, "CRS")) {
                List<CdbCrsInventoryTargetVO> tempList = getCrsInventoryTarget(statTime);
                tempList.forEach(item -> item.setProduct(product));
                if (StringUtils.equals(unit, UnitEnum.NUM.getName())) {
                    memoryToNum(tempList, deviceMap.getOrDefault(product, BigDecimal.ZERO));
                }
                ret.addAll(tempList);
            }
        }
        fillerService.fill(ret);
        return ret;
    }

    private List<CdbCrsInventoryTargetVO> getCdbInventoryTarget(List<String> statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_cdb_inventory_target.sql");
        return ckstdcrpDBHelper.getRaw(CdbCrsInventoryTargetVO.class, sql, statTime);
    }

    private List<CdbCrsInventoryTargetVO> getCrsInventoryTarget(List<String> statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_crs_inventory_target.sql");
        return ckstdcrpDBHelper.getRaw(CdbCrsInventoryTargetVO.class, sql, statTime, statTime);
    }

    private void memoryToNum(List<CdbCrsInventoryTargetVO> list, BigDecimal singleMemory) {
        //如果是台数，将内存转换为台
        for (CdbCrsInventoryTargetVO item : list) {
            if (Objects.isNull(singleMemory) || BigDecimal.ZERO.compareTo(singleMemory) == 0) {
                item.setInventoryTarget(BigDecimal.ZERO);
            } else {
                item.setInventoryTarget(SoeCommonUtils.divide(item.getInventoryTarget(), singleMemory));
            }

        }
    }
}
