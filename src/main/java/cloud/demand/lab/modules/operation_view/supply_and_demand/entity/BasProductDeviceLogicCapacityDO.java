package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("bas_product_device_logic_capacity")
public class BasProductDeviceLogicCapacityDO {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 规划产品：COS,CBS<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 地域类型：国内，海外，全部<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 逻辑容量（PB）<br/>Column: [logic_capacity] */
    @Column(value = "logic_capacity")
    private BigDecimal logicCapacity;

    /** 盘类型<br/>Column: [disk_type] */
    @Column(value = "disk_type")
    private String diskType;

    /** 盘大小（T）<br/>Column: [disk_size] */
    @Column(value = "disk_size")
    private BigDecimal diskSize;

    /** 盘数量<br/>Column: [disk_number] */
    @Column(value = "disk_number")
    private BigDecimal diskNumber;

    /** 创建人<br/>Column: [create_user] */
    @Column(value = "create_user")
    private String createUser;

    /** 更新时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDateTime createTime;

    /** 更新人<br/>Column: [update_user] */
    @Column(value = "update_user")
    private String updateUser;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private LocalDateTime updateTime;

}
