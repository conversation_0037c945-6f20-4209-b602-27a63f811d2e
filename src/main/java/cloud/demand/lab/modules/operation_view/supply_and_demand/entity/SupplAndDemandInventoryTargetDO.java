package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * [产品供需看板]目标库存
 */
@Data
@ToString
@Table("suppl_and_demand_inventory_target")
public class SupplAndDemandInventoryTargetDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 目标库存<br/>Column: [target_inventory] */
    @Column(value = "target_inventory")
    private Integer inventoryTarget;

    /** 备注<br/>Column: [remark] */
    @Column(value = "remark")
    private String remark;

}