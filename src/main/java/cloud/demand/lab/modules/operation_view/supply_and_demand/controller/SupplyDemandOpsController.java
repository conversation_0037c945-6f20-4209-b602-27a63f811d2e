package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.StatTimeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandGenService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 11:44
 */
@Slf4j
@JsonrpcController("/ops")
public class SupplyDemandOpsController {

    @Resource
    private SupplyAndDemandGenService supplyAndDemandGenService;

    @Synchronized(waitLockMillisecond = 100)
    @RequestMapping
    public void genSupplyOnTheWayData(@JsonrpcParam StatTimeReq req) {
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.genSupplyOnTheWayData(req.getStatTime());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.genSupplyOnTheWayData(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                startDate = startDate.plusDays(1);
            }
        }
    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void syncDemandData(@JsonrpcParam StatTimeReq req) {
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.CVM.getName(),
                ProductCategoryEnum.CBS.getName(),
                ProductCategoryEnum.DB.getName(),
                ProductCategoryEnum.GPU.getName());
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.syncDemandData(LocalDate.parse(req.getStatTime()), productCategory);
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.syncDemandData(startDate,productCategory);
                startDate = startDate.plusDays(1);
            }
        }

    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void syncCvmDemandData(@JsonrpcParam StatTimeReq req) {
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.CVM.getName());
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.syncCvmDemandData(LocalDate.parse(req.getStatTime()), req.isForceSync());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.syncCvmDemandData(startDate,req.isForceSync());
                startDate = startDate.plusDays(1);
            }
        }

    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void syncGpuDemandData(@JsonrpcParam StatTimeReq req) {
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.GPU.getName());
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.syncGpuDemandData(LocalDate.parse(req.getStatTime()), req.isForceSync());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.syncGpuDemandData(startDate,req.isForceSync());
                startDate = startDate.plusDays(1);
            }
        }

    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void syncRandomZoneName(@JsonrpcParam StatTimeReq req) {
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.syncRandomZoneName(LocalDate.parse(req.getStatTime()));
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.syncRandomZoneName(startDate);
                startDate = startDate.plusDays(1);
            }
        }

    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void syncPplJoinOrderNewestData(@JsonrpcParam StatTimeReq req) {
        supplyAndDemandGenService.syncPplJoinOrderNewestData(req.getStatTime(),true);
    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void genInventoryData(@JsonrpcParam StatTimeReq req) {
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.syncInventoryData(req.getStatTime());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.syncInventoryData(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                startDate = startDate.plusDays(1);
            }
        }

    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void genInventoryTargetData(@JsonrpcParam StatTimeReq req) {
        supplyAndDemandGenService.genInventoryTargetData(req.getStatTime());
    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void genDbSaleScaleData(@JsonrpcParam StatTimeReq req) {
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.genDbSaleScaleData(req.getStatTime());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.genDbSaleScaleData(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                startDate = startDate.plusDays(1);
            }
        }
    }

    @Synchronized(waitLockMillisecond = 1800)
    @RequestMapping
    public void repairData(@JsonrpcParam StatTimeReq req) {
        if (StringUtils.isNotEmpty(req.getStatTime())) {
            supplyAndDemandGenService.repairData(req.getStatTime());
        }
        if (StringUtils.isNotEmpty(req.getStartStatTime()) && StringUtils.isNotEmpty(req.getEndStatTime())) {
            LocalDate startDate = LocalDate.parse(req.getStartStatTime());
            LocalDate endDate = LocalDate.parse(req.getEndStatTime());
            while (!startDate.isAfter(endDate)) {
                supplyAndDemandGenService.repairData(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                startDate = startDate.plusDays(1);
            }
        }
    }
}
