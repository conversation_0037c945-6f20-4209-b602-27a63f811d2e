package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import cloud.demand.lab.modules.operation_view.supply_and_demand.parse.VolumeTypeParse;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import report.utils.anno.WhereReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DayDbScaleReq {

    @WhereReport
    private List<String> statTime;

    @WhereReport
    private List<String> zoneCategory;

    @WhereReport
    private List<String> customhouseTitle;

    @WhereReport
    private List<String> countryName;

    @WhereReport(sql = " area_name in (?) ")
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;

    private String unit;



    private List<String> dims;

    private List<String> originalStatTime;

    public void addLastMonthStatTime() {
        List<String> lastMonthStatTime = ListUtils.newArrayList();
        for (String statTime : this.getStatTime()) {
            String lastMonth = LocalDate.parse(statTime).minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE);
            lastMonthStatTime.add(lastMonth);
        }
        this.setOriginalStatTime(this.getStatTime());
        this.setStatTime(ListUtils.union(this.getStatTime(), lastMonthStatTime));
    }

    public static DayDbScaleReq transform(DemandCommonReq req) {
        DayDbScaleReq targetReq = new DayDbScaleReq();
        BeanUtils.copyProperties(req, targetReq);
        targetReq.setStatTime(req.getScaleStatTime());
        return targetReq;
    }

}
