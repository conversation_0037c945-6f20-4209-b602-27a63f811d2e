package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataAdjustDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandTrendReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.PplOrderDemandTrendData;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 16:29
 */
public interface DemandService {

    List<DemandReportDetailVO> queryReport(DemandReportReq req);

    List<PplOrderDemandTrendData> queryTrend(DemandTrendReq req);

    List<String> queryParams(SupplyDemandParamTypeReq req);

    List<DemandReportDetailVO> queryDemand(DemandReq req);


    void saveAdjustDemand(List<ProductDemandDataAdjustDO> dataList, String statTime, String productCategory);

}
