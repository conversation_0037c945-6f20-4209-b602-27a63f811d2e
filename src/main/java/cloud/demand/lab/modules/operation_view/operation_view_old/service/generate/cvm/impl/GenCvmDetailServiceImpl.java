package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.impl;

import cloud.demand.lab.common.config.DynamicProperties;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.GenCvmDetailService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model.ChCvmSaleDTO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model.CvmInventoryVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class GenCvmDetailServiceImpl implements GenCvmDetailService {

    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper newckdemandDBHelper;
    @Resource
    private OperationViewCommonService commonService;

    @Override
    public void genCvmDetail(String statTime) {

    }

    /**
     * 库存部分
     *
     * @param statTime
     */
    private Map<String, ReportOperationViewDetailDO> genInventoryDataForCvm(String statTime) {
        //   1、获取当日的线上、线下、总库存数据
        Map<String, ReportOperationViewDetailDO> newestInvMap = getNewestInvMap(statTime);
        //  2、获取近一年时间范围内的总库存数据
        Map<String, ReportOperationViewDetailDO> last12MonthAllInvMap = getLast12MonthAllInvMap(statTime, newestInvMap);
        return last12MonthAllInvMap;
    }

    /**
     * 售卖部分
     */
    private Map<String, ReportOperationViewDetailDO> genSaleDataForCvm(String statTime,
            Map<String, ReportOperationViewDetailDO> inventoryMap) {
        //  1、在库存的基础上填充by月的售卖数据
        Map<String, ReportOperationViewDetailDO> monthData = fillMonthSaleData(statTime, inventoryMap);
        //  2、在1的基础上填充by周的售卖/需求数据
        Map<String, ReportOperationViewDetailDO> weekData = fillWeekSaleData(statTime, monthData);
        return weekData;
    }


    /**
     * 填充By月销量
     *
     * @param statTime
     * @param inventoryMap
     * @return
     */
    private Map<String, ReportOperationViewDetailDO> fillMonthSaleData(String statTime,
            Map<String, ReportOperationViewDetailDO> inventoryMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);
        //  过去12个月每个月的天数，计算日均值
        //    Map<String, Integer> monthNumOfDays = commonService.getMonthNumOfDays(statTime, 12); // 这个不需要

        //  获取售卖底数
        String sql = ORMUtils.getSql("/sql/operation_view/cvm/sale_detail_by_month.sql");
        List<ChCvmSaleDTO> all = newckdemandDBHelper.getRaw(ChCvmSaleDTO.class, sql, timeInterval._1, timeInterval._2);
        ListUtils.forEach(all, o -> o.setDeviceType(dictService.getDeviceTypeByDeviceFamily(o.getGinsFamily())));

        Map<String, List<ChCvmSaleDTO>> saleMap = ListUtils.groupBy(all, o -> o.getGroupK());
        for (Map.Entry<String, List<ChCvmSaleDTO>> entry : saleMap.entrySet()) {
            String key = entry.getKey();
            ReportOperationViewDetailDO detailDO = inventoryMap.get(key);
            if (detailDO == null) {
                ReportOperationViewDetailDO saleNewDO = entry.getValue().get(0).toOperationViewDO();
                commonService.fillOtherFields(saleNewDO, statTime);
                detailDO = saleNewDO;
            }

            for (ChCvmSaleDTO dto : entry.getValue()) {
                String yearMonth = dto.getYear() + "-" + dto.getMonth();
                Integer i = mReduceNMap.get(yearMonth);
                commonService.setHistorySaleByI(i, dto, detailDO);
            }
            inventoryMap.put(key, detailDO);
        }
        return inventoryMap;
    }


    /**
     * 填充By周销量
     */
    private Map<String, ReportOperationViewDetailDO> fillWeekSaleData(String statTime,
            Map<String, ReportOperationViewDetailDO> monthData) {
        //  获取过去13周的起止时间(end为上周周末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByWeek(statTime, 13);

        String start = timeInterval._1;
        Date curStart = DateUtils.parse(start);
        Date curEnd = DateUtils.addTime(curStart, Calendar.DATE, 6);

        //  获取售卖底数sql
        String sql = ORMUtils.getSql("/sql/operation_view/cvm/sale_detail_by_week.sql");

        for (int i = 13; i >= 1; i--) {
            List<ChCvmSaleDTO> all = newckdemandDBHelper.getRaw(ChCvmSaleDTO.class,
                    sql, DateUtils.formatDate(curStart), DateUtils.formatDate(curEnd));
            ListUtils.forEach(all, o -> o.setDeviceType(dictService.getDeviceTypeByDeviceFamily(o.getGinsFamily())));

            Map<String, List<ChCvmSaleDTO>> saleMap = ListUtils.groupBy(all, o -> o.getGroupK());

            for (Map.Entry<String, List<ChCvmSaleDTO>> entry : saleMap.entrySet()) {
                String key = entry.getKey();
                ReportOperationViewDetailDO detailDO = monthData.get(key);
                if (detailDO == null) {
                    ReportOperationViewDetailDO saleNewDO = entry.getValue().get(0).toOperationViewDO();
                    commonService.fillOtherFields(saleNewDO, statTime);
                    detailDO = saleNewDO;
                }

                //  合并list为一个DTO对象
                ChCvmSaleDTO dto = ChCvmSaleDTO.mergeListSaleDTO(entry.getValue());
                commonService.setHistoryDemandByI(i, dto, detailDO);
                monthData.put(key, detailDO);
            }
            curStart = DateUtils.addTime(curStart, Calendar.DATE, 7);
            curEnd = DateUtils.addTime(curEnd, Calendar.DATE, 7);
        }
        return monthData;
    }


    /**
     * 生成过去12个月的历史库存Map
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthAllInvMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);
        //  过去12个月每个月的天数，计算日均值
        Map<String, Integer> monthNumOfDays = commonService.getMonthNumOfDays(statTime, 12);

        // 1、获得线上和线下库存的条目
        List<CvmInventoryVO> all = rrpDBHelper.getAll(CvmInventoryVO.class,
                "WHERE stat_time between ? and ? AND product_type='CVM' AND compute_type='CPU'\n" +
                        "AND indicator_code IN ('d1','d2','d3','e4','e8')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                timeInterval._1, timeInterval._2);
        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
        // 1.2 合并重复的项
        Map<String, CvmInventoryVO> resultMap = removeRepeatData(all);

        Map<String, List<CvmInventoryVO>> map = ListUtils.groupBy(resultMap.values(), o -> o.getBaseGroupK());

        for (Map.Entry<String, List<CvmInventoryVO>> entry : map.entrySet()) {
            String key = entry.getKey();
            ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
            if (detailDO == null) {
                ReportOperationViewDetailDO history = entry.getValue().get(0).toOperationViewDO(false);
                commonService.fillOtherFields(history, statTime);
                detailDO = history;
            }
            for (CvmInventoryVO each : entry.getValue()) {
                String yearMonth = each.getYearMonth();
                Integer n = mReduceNMap.get(yearMonth);
                //  避免除0异常
                Integer num = monthNumOfDays.get(yearMonth) == null ? 1 : monthNumOfDays.get(yearMonth);
                each.setOnlineCores(AmountUtils.divideScale6(each.getOnlineCores(), BigDecimal.valueOf(num)));
                each.setOfflineCores(AmountUtils.divideScale6(each.getOfflineCores(), BigDecimal.valueOf(num)));
                commonService.setHistoryInvByI(n, each, detailDO, ProductTypeEnum.CVM.getCode());
            }
            newestInvMap.put(key, detailDO);
        }
        return newestInvMap;
    }

    /**
     * 相同维度合并
     */
    public Map<String, CvmInventoryVO> removeRepeatData(List<CvmInventoryVO> all){
        Map<String, List<CvmInventoryVO>> map = ListUtils.groupBy(all, o -> o.getGroupK());
        Map<String, CvmInventoryVO> resultMap = new HashMap<>();

        for (Map.Entry<String, List<CvmInventoryVO>> entry : map.entrySet()) {
            List<CvmInventoryVO> value = entry.getValue();
            if (entry.getValue().size() > 1){
                CvmInventoryVO copyOne = value.get(0).copyOne();
                copyOne.setOnlineCores(NumberUtils.sum(value, o -> o.getOnlineCores()));
                copyOne.setOfflineCores(NumberUtils.sum(value, o -> o.getOfflineCores()));
                resultMap.put(entry.getKey(), copyOne);
            }else {
                resultMap.put(entry.getKey(), value.get(0));
            }
        }
        return resultMap;
    }


    /**
     * 生成当月最新库存数据Map
     */
    private Map<String, ReportOperationViewDetailDO> getNewestInvMap(String statTime) {
        // 1. 第一步：获得线上和线下库存的条目
        List<CvmInventoryVO> all = rrpDBHelper.getAll(CvmInventoryVO.class,
                "WHERE stat_time=? AND product_type='CVM' AND compute_type='CPU'\n" +
                        "AND indicator_code IN ('d1','d2','d3','e4','e8')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", statTime);

        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

        // 1.2 合并重复的项
        Map<String, CvmInventoryVO> resultMap = removeRepeatData(all);

        // 1.3 to DO
        List<ReportOperationViewDetailDO> transform = ListUtils.transform(resultMap.values(), o -> o.toOperationViewDO(true));

        // 1.4 补全信息
        ListUtils.forEach(transform, o -> commonService.fillOtherFields(o, statTime));

        return ListUtils.toMap(transform, o -> o.getKey(), o -> o);
    }


}
