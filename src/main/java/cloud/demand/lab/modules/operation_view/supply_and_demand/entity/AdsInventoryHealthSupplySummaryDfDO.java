package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;
import report.utils.model.field.zone.ICountryName;
import report.utils.model.field.zone.IRegionName;

@Data
@ToString
@Table("ads_inventory_health_supply_summary_df")
public class AdsInventoryHealthSupplySummaryDfDO implements CountryNameFiller, ZoneCategoryFiller, InstanceCategoryFiller,InstanceGroupFiller, ZoneInfoFiller  {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 区域名称<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域编码<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区类型<br/>Column: [zone_category] */
    @Column(value = "zone_category")
    private String zoneCategory;

    /** 可用区编码<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_category] */
    @Column(value = "instance_category")
    private String instanceCategory;

    /** 实例族<br/>Column: [instance_group] */
    @Column(value = "instance_group")
    private String instanceGroup;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "volume_type")
    private String volumeType;

    /** 供应类型：库存、采购<br/>Column: [supply_type] */
    @Column(value = "supply_type")
    private String supplyType;

    /** 产品类型：CVM、GPU、裸金属<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** <br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 库存类型：线上库存库存、线下搬迁、线下流转<br/>Column: [line_type] */
    @Column(value = "line_type")
    private String lineType;

    /** 物料类型：好料、呆料、差料<br/>Column: [material_type] */
    @Column(value = "material_type")
    private String materialType;

    /** 库存细类：用户预扣、小核库存、大核库存等详细分类<br/>Column: [inv_detail_type] */
    @Column(value = "inv_detail_type")
    private String invDetailType;

    /** Q单id<br/>Column: [quota_id] */
    @Column(value = "quota_id")
    private String quotaId;

    /** 核心数<br/>Column: [cores] */
    @Column(value = "cores")
    private BigDecimal cores;

    /** 总设备数量<br/>Column: [total_num] */
    @Column(value = "total_num")
    private BigDecimal totalNum;

    /** 对应设备类型的逻辑核心数<br/>Column: [logic_cores] */
    @Column(value = "logic_cores")
    private BigDecimal logicCores;

    /** 对应设备类型的售卖核心数<br/>Column: [sale_cores] */
    @Column(value = "sale_cores")
    private BigDecimal saleCores;

    @Column(value = "disk_storage")
    private BigDecimal diskStorage;

    @Column(value = "memory")
    private BigDecimal memory;

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
    }

    @Override
    public String provideCustomhouseTitle() {
        return this.getCustomhouseTitle();
    }

    @Override
    public void fillInstanceCategory(String instanceCategory) {
        this.instanceCategory = instanceCategory;
    }

    @Override
    public String provideZoneName() {
        return this.zoneName;
    }

    @Override
    public void fillZone(String zone) {
        this.zone = zone;
    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.customhouseTitle = customhouseTitle;
    }

    @Override
    public void fillRegionName(String regionName) {
        this.regionName = regionName;
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }
}