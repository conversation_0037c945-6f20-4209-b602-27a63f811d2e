package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;


import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyReportDetailReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyOnTheWayReportService;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import java.util.List;

@Slf4j
@JsonrpcController("/supply-demand/supply/report")
public class SupplyReportController {

    @Resource
    private SupplyOnTheWayReportService supplyOnTheWayReportService;

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;


    @RequestMapping
    public Object querySupplyDetailReport(@JsonrpcParam SupplyReportDetailReq req) {

        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if(StringUtils.isEmpty(req.getStartDate()) || StringUtils.isEmpty(req.getEndDate())){
            throw new WrongWebParameterException("开始时间或结束时间不能为空");
        }
        supplyAndDemandDictService.addUnclassified(req);
        req.setHedgingParams();
        return supplyOnTheWayReportService.querySupplyOnTheWayDetailReport(req);
    }

    @RequestMapping
    public Object querySupplyTrendReport(@JsonrpcParam SupplyReportDetailReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if(StringUtils.isEmpty(req.getStartDate()) || StringUtils.isEmpty(req.getEndDate())){
            throw new WrongWebParameterException("开始时间或结束时间不能为空");
        }
        req.setHedgingParams();
        supplyAndDemandDictService.addUnclassified(req);
        return supplyOnTheWayReportService.querySupplyOnTheWayTrendReport(req);
    }

    @RequestMapping
    public DownloadBean exportSupplyDetailExcel(@JsonrpcParam SupplyReportDetailReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        req.setHedgingParams();
        supplyAndDemandDictService.addUnclassified(req);
        return supplyOnTheWayReportService.exportSupplyDetailExcel(req);
    }

    @RequestMapping
    public Object queryAllSupplyCampus() {
        return supplyOnTheWayReportService.queryAllSupplyCampus();
    }

    @RequestMapping
    public Object queryProduct() {
        return supplyOnTheWayReportService.queryProduct();
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam SupplyDemandParamTypeReq req){
        return supplyOnTheWayReportService.queryParams(req);
    }


}
