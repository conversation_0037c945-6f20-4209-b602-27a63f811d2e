package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CRSHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.TDSQLHealthActualData;
import java.util.List;

public interface CDBHealthActualService {

    List<CDBHealthActualData> queryCDBHealthActualReport(CDBHealthActualReq req);

    List<TxyRegionInfoDTO> queryCDBZoneNameInfo();

    List<CRSHealthActualData> queryCRSHealthActualReport(CDBHealthActualReq req);

   List<TDSQLHealthActualData> queryTDSQLHealthActualReport(CDBHealthActualReq req);


}
