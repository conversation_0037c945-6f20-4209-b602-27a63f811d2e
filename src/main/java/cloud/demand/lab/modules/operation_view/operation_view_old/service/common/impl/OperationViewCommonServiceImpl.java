package cloud.demand.lab.modules.operation_view.operation_view_old.service.common.impl;

import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.model.PlanDetailVO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model.ChCvmSaleDTO;
import com.pugwoo.wooutils.lang.DateUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OperationViewCommonServiceImpl implements OperationViewCommonService {

    @Resource
    private DictService dictService;

    public void fillOtherFields(ReportOperationViewDetailDO o, String statTime){
        o.setDeviceFamily(dictService.getDeviceFamilyByDeviceType(o.getDeviceType()));
        o.setStatTime(DateUtils.toLocalDate(DateUtils.parse(statTime)));
        //  CPU规格直接从销售表里取了，不再通过逻辑核心数推断
        Integer cpuLogicCore = AmountUtils.
                divideScale6(dictService.getPlanDeviceLogicCore(o.getDeviceType()), 100).intValue();
        o.setCpuLogicCore(cpuLogicCore);
    }

    public Tuple2<String, String> getTimeIntervalByMonth(String statTime, int n){
        Date date = DateUtils.parse(statTime);
        Integer year = DateUtils.getYear(date);
        Integer month = DateUtils.getMonth(date);
        //  当月第一天
        String firstDay = year + "-" + (month < 10 ? "0" + month : month.toString()) + "-01";
        String start = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(firstDay), Calendar.MONTH, -n));
        String end = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(firstDay), Calendar.DATE, -1));
        return new Tuple2<>(start, end);
    }


    public Tuple2<String, String> getTimeIntervalByWeek(String statTime, int n){
        Date curWeekMonday = getMonday(statTime);
        String start = DateUtils.formatDate(DateUtils.addTime(curWeekMonday, Calendar.WEEK_OF_YEAR, -n));
        String end = DateUtils.formatDate(DateUtils.addTime(curWeekMonday, Calendar.DATE, -1));
        return new Tuple2<>(start, end);
    }

    /**
     * 获取本周周一日期
     */
    public static Date getMonday(String statTime){
        Calendar instance = Calendar.getInstance();
        instance.setTime(DateUtils.parse(statTime));
        int dayOfWeek = instance.get(Calendar.DAY_OF_WEEK);
        if (1 == dayOfWeek){
            instance.add(Calendar.DAY_OF_MONTH, -1);
        }
        int day = instance.get(Calendar.DAY_OF_WEEK);
        instance.setFirstDayOfWeek(Calendar.MONDAY);
        int firstDayOfWeek = instance.getFirstDayOfWeek();
        instance.add(Calendar.DATE, firstDayOfWeek - day);
        return instance.getTime();
    }

    public Map<String, Integer> getMReduceNMonthMap(String statTime){
        Map<String, Integer> resultMap = new HashMap<>();
        Date date = DateUtils.parse(statTime);
        Integer year = DateUtils.getYear(date);
        Integer month = DateUtils.getMonth(date);
        //  当月第一天
        String firstDay = year + "-" + (month < 10 ? "0" + month : month.toString()) + "-01";
        String curFirstDay = firstDay;
        for (int i = 1; i <= 13; i++) {
            Date curDate = DateUtils.addTime(DateUtils.parse(curFirstDay), Calendar.DATE, -1);
            Integer curYear = DateUtils.getYear(curDate);
            Integer curMonth = DateUtils.getMonth(curDate);
            resultMap.put(curYear + "-" + curMonth, i);
            curFirstDay = curYear + "-" + (curMonth < 10 ? "0" + curMonth : curMonth.toString()) + "-01";
        }
        return resultMap;
    }

    public void setHistoryInvByI(int i, PlanDetailVO vo,
            ReportOperationViewDetailDO one, String productType){
        BigDecimal onlineNum;
        BigDecimal offlineNum;

        if (Objects.equals(productType, "CVM") || Objects.equals(productType, "裸金属")){
            onlineNum = vo.getOnlineCores() == null ? BigDecimal.ZERO : vo.getOnlineCores();
            offlineNum = vo.getOfflineCores() == null ? BigDecimal.ZERO : vo.getOfflineCores();
        }else {
            onlineNum = vo.getOnlineLogicNum() == null ? BigDecimal.ZERO : vo.getOnlineLogicNum();
            offlineNum = vo.getOfflineLogicNum() == null ? BigDecimal.ZERO : vo.getOfflineLogicNum();
        }

        BigDecimal totalNum = onlineNum.add(offlineNum);

        switch (i){
            case 1:
                one.setInvM1(totalNum);
                break;
            case 2:
                one.setInvM2(totalNum);
                break;
            case 3:
                one.setInvM3(totalNum);
                break;
            case 4:
                one.setInvM4(totalNum);
                break;
            case 5:
                one.setInvM5(totalNum);
                break;
            case 6:
                one.setInvM6(totalNum);
                break;
            case 7:
                one.setInvM7(totalNum);
                break;
            case 8:
                one.setInvM8(totalNum);
                break;
            case 9:
                one.setInvM9(totalNum);
                break;
            case 10:
                one.setInvM10(totalNum);
                break;
            case 11:
                one.setInvM11(totalNum);
                break;
            case 12:
                one.setInvM12(totalNum);
                break;
        }
    }


    public void setHistorySaleByI(int i, PlanDetailVO vo,
            ReportOperationViewDetailDO one, String productType){
        BigDecimal saleNum;

        if (Objects.equals(productType, "CVM") || Objects.equals(productType, "裸金属")){
            saleNum = vo.getSaleCores() == null ? BigDecimal.ZERO : vo.getSaleCores();
        }else {
            saleNum = vo.getSaleLogicNum() == null ? BigDecimal.ZERO : vo.getSaleLogicNum();
        }

        switch (i){
            case 1:
                one.setSaleM1(saleNum);
                break;
            case 2:
                one.setSaleM2(saleNum);
                break;
            case 3:
                one.setSaleM3(saleNum);
                break;
            case 4:
                one.setSaleM4(saleNum);
                break;
            case 5:
                one.setSaleM5(saleNum);
                break;
            case 6:
                one.setSaleM6(saleNum);
                break;
            case 7:
                one.setSaleM7(saleNum);
                break;
            case 8:
                one.setSaleM8(saleNum);
                break;
            case 9:
                one.setSaleM9(saleNum);
                break;
            case 10:
                one.setSaleM10(saleNum);
                break;
            case 11:
                one.setSaleM11(saleNum);
                break;
            case 12:
                one.setSaleM12(saleNum);
                break;
        }
    }

    public void setHistorySaleByI(int i, ChCvmSaleDTO dto, ReportOperationViewDetailDO one){
        BigDecimal totalCores = dto.getSaleCores() == null ? BigDecimal.ZERO : dto.getSaleCores();
        switch (i){
            case 1:
                one.setSaleM1(totalCores.add(one.getSaleM1() == null ? BigDecimal.ZERO : one.getSaleM1()));
                break;
            case 2:
                one.setSaleM2(totalCores.add(one.getSaleM2() == null ? BigDecimal.ZERO : one.getSaleM2()));
                break;
            case 3:
                one.setSaleM3(totalCores.add(one.getSaleM3() == null ? BigDecimal.ZERO : one.getSaleM3()));
                break;
            case 4:
                one.setSaleM4(totalCores.add(one.getSaleM4() == null ? BigDecimal.ZERO : one.getSaleM4()));
                break;
            case 5:
                one.setSaleM5(totalCores.add(one.getSaleM5() == null ? BigDecimal.ZERO : one.getSaleM5()));
                break;
            case 6:
                one.setSaleM6(totalCores.add(one.getSaleM6() == null ? BigDecimal.ZERO : one.getSaleM6()));
                break;
            case 7:
                one.setSaleM7(totalCores.add(one.getSaleM7() == null ? BigDecimal.ZERO : one.getSaleM7()));
                break;
            case 8:
                one.setSaleM8(totalCores.add(one.getSaleM8() == null ? BigDecimal.ZERO : one.getSaleM8()));
                break;
            case 9:
                one.setSaleM9(totalCores.add(one.getSaleM9() == null ? BigDecimal.ZERO : one.getSaleM9()));
                break;
            case 10:
                one.setSaleM10(totalCores.add(one.getSaleM10() == null ? BigDecimal.ZERO : one.getSaleM10()));
                break;
            case 11:
                one.setSaleM11(totalCores.add(one.getSaleM11() == null ? BigDecimal.ZERO : one.getSaleM11()));
                break;
            case 12:
                one.setSaleM12(totalCores.add(one.getSaleM12() == null ? BigDecimal.ZERO : one.getSaleM12()));
                break;
        }
    }

    public void setHistoryDemandByI(int i, ChCvmSaleDTO dto, ReportOperationViewDetailDO one){
        BigDecimal totalCores = dto.getSaleCores() == null ? BigDecimal.ZERO : dto.getSaleCores();
        switch (i){
            case 1:
                one.setDemandW1(totalCores.add(one.getDemandW1() == null ? BigDecimal.ZERO : one.getDemandW1()));
                break;
            case 2:
                one.setDemandW2(totalCores.add(one.getDemandW2() == null ? BigDecimal.ZERO : one.getDemandW2()));
                break;
            case 3:
                one.setDemandW3(totalCores.add(one.getDemandW3() == null ? BigDecimal.ZERO : one.getDemandW3()));
                break;
            case 4:
                one.setDemandW4(totalCores.add(one.getDemandW4() == null ? BigDecimal.ZERO : one.getDemandW4()));
                break;
            case 5:
                one.setDemandW5(totalCores.add(one.getDemandW5() == null ? BigDecimal.ZERO : one.getDemandW5()));
                break;
            case 6:
                one.setDemandW6(totalCores.add(one.getDemandW6() == null ? BigDecimal.ZERO : one.getDemandW6()));
                break;
            case 7:
                one.setDemandW7(totalCores.add(one.getDemandW7() == null ? BigDecimal.ZERO : one.getDemandW7()));
                break;
            case 8:
                one.setDemandW8(totalCores.add(one.getDemandW8() == null ? BigDecimal.ZERO : one.getDemandW8()));
                break;
            case 9:
                one.setDemandW9(totalCores.add(one.getDemandW9() == null ? BigDecimal.ZERO : one.getDemandW9()));
                break;
            case 10:
                one.setDemandW10(totalCores.add(one.getDemandW10() == null ? BigDecimal.ZERO : one.getDemandW10()));
                break;
            case 11:
                one.setDemandW11(totalCores.add(one.getDemandW11() == null ? BigDecimal.ZERO : one.getDemandW11()));
                break;
            case 12:
                one.setDemandW12(totalCores.add(one.getDemandW12() == null ? BigDecimal.ZERO : one.getDemandW12()));
                break;
            case 13:
                one.setDemandW13(totalCores.add(one.getDemandW13() == null ? BigDecimal.ZERO : one.getDemandW13()));
                break;

        }
    }


    public void setHistoryDemandByI(int i, PlanDetailVO dto,
            ReportOperationViewDetailDO one, String productType){
        BigDecimal saleNum;

        if (Objects.equals(productType, "CVM") || Objects.equals(productType, "裸金属")){
            saleNum = dto.getSaleCores() == null ? BigDecimal.ZERO : dto.getSaleCores();
        }else {
            saleNum = dto.getSaleLogicNum() == null ? BigDecimal.ZERO : dto.getSaleLogicNum();
        }
        switch (i){
            case 1:
                one.setDemandW1(saleNum.add(one.getDemandW1() == null ? BigDecimal.ZERO : one.getDemandW1()));
                break;
            case 2:
                one.setDemandW2(saleNum.add(one.getDemandW2() == null ? BigDecimal.ZERO : one.getDemandW2()));
                break;
            case 3:
                one.setDemandW3(saleNum.add(one.getDemandW3() == null ? BigDecimal.ZERO : one.getDemandW3()));
                break;
            case 4:
                one.setDemandW4(saleNum.add(one.getDemandW4() == null ? BigDecimal.ZERO : one.getDemandW4()));
                break;
            case 5:
                one.setDemandW5(saleNum.add(one.getDemandW5() == null ? BigDecimal.ZERO : one.getDemandW5()));
                break;
            case 6:
                one.setDemandW6(saleNum.add(one.getDemandW6() == null ? BigDecimal.ZERO : one.getDemandW6()));
                break;
            case 7:
                one.setDemandW7(saleNum.add(one.getDemandW7() == null ? BigDecimal.ZERO : one.getDemandW7()));
                break;
            case 8:
                one.setDemandW8(saleNum.add(one.getDemandW8() == null ? BigDecimal.ZERO : one.getDemandW8()));
                break;
            case 9:
                one.setDemandW9(saleNum.add(one.getDemandW9() == null ? BigDecimal.ZERO : one.getDemandW9()));
                break;
            case 10:
                one.setDemandW10(saleNum.add(one.getDemandW10() == null ? BigDecimal.ZERO : one.getDemandW10()));
                break;
            case 11:
                one.setDemandW11(saleNum.add(one.getDemandW11() == null ? BigDecimal.ZERO : one.getDemandW11()));
                break;
            case 12:
                one.setDemandW12(saleNum.add(one.getDemandW12() == null ? BigDecimal.ZERO : one.getDemandW12()));
                break;
            case 13:
                one.setDemandW13(saleNum.add(one.getDemandW13() == null ? BigDecimal.ZERO : one.getDemandW13()));
                break;

        }
    }

    public Map<String, Integer> getMonthNumOfDays(String statTime, int n){
        Map<String, Integer> resultMap = new HashMap<>();
        Date date = DateUtils.parse(statTime);
        Integer year = DateUtils.getYear(date);
        Integer month = DateUtils.getMonth(date);
        //  当月第一天
        String firstDay = year + "-" + (month < 10 ? "0" + month : month.toString()) + "-01";
        String curFirstDay = firstDay;
        for (int i = 0; i < n; i++) {
            Date curDate = DateUtils.addTime(DateUtils.parse(curFirstDay), Calendar.DATE, -1);
            Integer curYear = DateUtils.getYear(curDate);
            Integer curMonth = DateUtils.getMonth(curDate);
            curFirstDay = curYear + "-" + (curMonth < 10 ? "0" + curMonth : curMonth.toString()) + "-01";
            resultMap.put(curYear + "-" + curMonth, DateUtils.diffDays(DateUtils.parse(curFirstDay), curDate) + 1);
        }
        return resultMap;
    }


}
