package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.IInventoryTargetGroupKey;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.pugwoo.dbhelper.annotation.Table;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

@Data
@ToString
@Table("suppl_and_demand_inventory_target_df")
public class SupplAndDemandInventoryTargetDfDO implements IInventoryTargetGroupKey {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** id<br/>Column: [id] */
    @Column(value = "id")
    private Long id;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 库存目标<br/>Column: [inventory_target] */
    @Column(value = "inventory_target")
    private Integer inventoryTarget;

    /** 备注<br/>Column: [remark] */
    @Column(value = "remark")
    private String remark;

    public static SupplAndDemandInventoryTargetDfDO transform(SupplAndDemandInventoryTargetDO inventoryTargetDO,LocalDate statTime){
        SupplAndDemandInventoryTargetDfDO ret = new SupplAndDemandInventoryTargetDfDO();
        BeanUtils.copyProperties(inventoryTargetDO,ret);
        ret.setStatTime(statTime);
        return ret;
    }

    @Override
    public String getInventoryTargetGroupKey() {
        return StringUtils.joinWith("@", this.getStatTime().format(DateTimeFormatter.ISO_LOCAL_DATE), this.getInstanceType(), this.getRegionName());
    }
}