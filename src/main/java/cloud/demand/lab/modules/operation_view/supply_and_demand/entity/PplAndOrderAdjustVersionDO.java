package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.common.entity.BaseUserDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_and_order_adjust_version")
public class PplAndOrderAdjustVersionDO extends BaseUserDO {

    /** 版本日期<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 产品大类<br/>Column: [product_category] */
    @Column(value = "product_category")
    private String productCategory;

    /** 是否定稿 0-未定稿 1-已定稿<br/>Column: [definitive] */
    @Column(value = "definitive")
    private Boolean definitive;

}
