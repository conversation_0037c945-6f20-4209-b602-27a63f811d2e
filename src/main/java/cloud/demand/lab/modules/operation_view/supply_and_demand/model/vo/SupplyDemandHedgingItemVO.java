package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:39
 */
@Data
public class SupplyDemandHedgingItemVO implements CountryNameFiller, InstanceGroupFiller {

    private String statTime;

    private String productCategory;

    private String yearMonth;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private String volumeType;

    private String projectType;

    private String type;//demand、supply、targetInventory、actualSafeInventory、actualInventory、withholdInventory

    private BigDecimal amount;

    private SafeInventoryCalcVO safeInventoryCalc;

    public static SupplyDemandHedgingItemVO transform(InventoryDisassembleResp.Item item) {
        SupplyDemandHedgingItemVO ret = new SupplyDemandHedgingItemVO();
        BeanUtils.copyProperties(item, ret);
        ret.setType("actualSafeInventory");
        ret.setAmount(BigDecimal.valueOf(item.getActualSafeInventory()));

        SafeInventoryCalcVO safeInventoryCalc = new SafeInventoryCalcVO(
                BigDecimal.valueOf(item.getActualInventory()),BigDecimal.valueOf(item.getActualTurnoverInventory()), BigDecimal.valueOf(item.getSafeInventory()));
        ret.setSafeInventoryCalc(safeInventoryCalc);

        if (StringUtils.isBlank(item.getCustomhouseTitle())) {
            ret.setCustomhouseTitle(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getAreaName())) {
            ret.setAreaName(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getRegionName())) {
            ret.setRegionName(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getZoneName())) {
            ret.setZoneName(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getZoneCategory())) {
            ret.setZoneCategory(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getInstanceCategory())) {
            ret.setInstanceCategory(Constant.EMPTY_VALUE_STR);
        }
        if (StringUtils.isBlank(item.getInstanceType())) {
            ret.setInstanceType(Constant.EMPTY_VALUE_STR);
        }
        return ret;
    }

    public static SupplyDemandHedgingItemVO transform(AdsInventoryHealthSupplySummaryDfVO item, String type) {
        SupplyDemandHedgingItemVO ret = new SupplyDemandHedgingItemVO();
        BeanUtils.copyProperties(item, ret);
        //ret.setStatTime(LocalDate.parse(item.getStatTime()).plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        ret.setType(type);
        if (StringUtils.equals("withholdInventory", type)) {
            ret.setAmount(item.getWithholdInventoryCore());
        } else {
            ret.setAmount(item.getBeginInventory());
        }

        return ret;
    }

    public static SupplyDemandHedgingItemVO transform(DemandReportDetailVO item) {
        SupplyDemandHedgingItemVO ret = new SupplyDemandHedgingItemVO();
        BeanUtils.copyProperties(item, ret);
        if(StringUtils.equals(item.getDataType(),"规模")){
            ret.setType("scale");
        }else {
            ret.setType("demand");
        }
        ret.setAmount(item.getTotalAmount());
        return ret;
    }

    public static SupplyDemandHedgingItemVO transform(SupplyOnTheWayDetailData item) {
        SupplyDemandHedgingItemVO ret = new SupplyDemandHedgingItemVO();
        BeanUtils.copyProperties(item, ret);
        ret.setType("supply");
        ret.setYearMonth(item.getSlaMonth());
        ret.setAmount(BigDecimal.valueOf(item.getSupplyCore()));

        return ret;
    }

    public static List<SupplyDemandHedgingItemVO> mergeList(List<SupplyDemandHedgingItemVO> itemList, List<String> mergeDims) {
        List<String> sumFields = ListUtils.newArrayList("amount");

        List<SupplyDemandHedgingItemVO> retLit = ListUtils.newArrayList();
        ListUtils.groupBy(itemList, item -> GroupUtils.getDimsGroupKey(item, mergeDims))
                .forEach((k, vList) -> {
                    SupplyDemandHedgingItemVO temp = GroupUtils.mergeList(vList, mergeDims, sumFields);
                    //计算安全库存
                    BigDecimal actualInventory = BigDecimal.ZERO;
                    BigDecimal actualTurnoverInventory = BigDecimal.ZERO;
                    BigDecimal safeInventory = BigDecimal.ZERO;
                    for(SupplyDemandHedgingItemVO item : vList){
                        if(Objects.nonNull(item.getSafeInventoryCalc())){
                            actualInventory = SoeCommonUtils.addWithNull(actualInventory,item.getSafeInventoryCalc().getActualInventory());
                            actualTurnoverInventory = SoeCommonUtils.addWithNull(actualTurnoverInventory,item.getSafeInventoryCalc().getActualTurnoverInventory());
                            safeInventory = SoeCommonUtils.addWithNull(safeInventory,item.getSafeInventoryCalc().getSafeInventory());
                        }
                    }
                    SafeInventoryCalcVO safeInventoryCalcInfo = new SafeInventoryCalcVO(actualInventory,actualTurnoverInventory,safeInventory);
                    temp.setSafeInventoryCalc(safeInventoryCalcInfo);
                    retLit.add(temp);
                });
        return retLit;
    }

    public static String getGroupKey(SupplyDemandHedgingItemVO item) {
        return StringUtils.joinWith("@", item.getStatTime(), item.getZoneCategory(), item.getInstanceCategory(), item.getCustomhouseTitle(),
                item.getCountryName(), item.getAreaName(), item.getRegionName(), item.getZoneName(), item.getInstanceGroup(), item.getInstanceType(), item.getVolumeType(),item.getProjectType());
    }

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.getRegionName();
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
    }

}
