package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.inventory_health.enums.PplDatabaseEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.PplWaveProjectTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/11 15:04
 */
@Data
public class SyncDbDemandDataVO extends SyncCvmDemandDataVO {


    /**
     * 数据库存名称
     */
    @Column(value = "database_name")
    private String databaseName;

    /**
     * 存储类型：通用版、云盘版,默认(空值)
     */
    @Column(value = "db_storage_type")
    private String dbStorageType;

    /**
     * 部署类型：通用型、独享型,默认(空值)
     */
    @Column(value = "db_deploy_type")
    private String dbDeployType;

    /**
     * 架构类型：通用版、云盘版,默认(空值)
     */
    @Column(value = "db_framework_type")
    private String dbFrameworkType;

    @Column(value = "db_memory")
    private BigDecimal dbMemory = BigDecimal.ZERO;

    @Column(value = "disk_storage")
    private BigDecimal diskStorage = BigDecimal.ZERO;

    public static ProductDemandDataDfDO transform(SyncDbDemandDataVO item) {
        ProductDemandDataDfDO ret = new ProductDemandDataDfDO();
        BeanUtils.copyProperties(item, ret);
        ret.setProductCategory(ProductCategoryEnum.DB.getName());
        ret.setDemandCaliber("共识需求");
        ret.setDiskType(Constant.EMPTY_STR);
        ret.setVolumeType(Constant.EMPTY_STR);
        ret.setProduct(PplDatabaseEnum.getByName(item.getDatabaseName()).getAlias());
        ret.setProjectType(PplWaveProjectTypeEnum.getNameByCode(item.getCbsIsSpike()));
        return ret;
    }

    public static List<ProductDemandDataDfDO> buildCrossMonthData(List<ProductDemandDataDfDO> list) {
        List<ProductDemandDataDfDO> ret = ListUtils.newArrayList();
        for (ProductDemandDataDfDO item : list) {
            int beginMonthValue = LocalDate.parse(item.getBeginBuyDate()).getMonthValue();
            int endMonthValue = LocalDate.parse(item.getEndBuyDate()).getMonthValue();
            int lastMonthValue = LocalDate.now().getMonthValue() - 1;
            if (beginMonthValue != endMonthValue && item.getWaitBuyDbMemory().compareTo(BigDecimal.ZERO) > 0 && lastMonthValue == beginMonthValue) {
                ProductDemandDataDfDO retItem = new ProductDemandDataDfDO();
                BeanUtils.copyProperties(item, retItem);
                //月份+1
                String nextYearMonth = LocalDate.parse(item.getYearMonth() + "-01").plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                retItem.setYearMonth(nextYearMonth);
                retItem.setMonthType("跨月");
                ret.add(retItem);
            }
        }
        return ret;
    }
}
