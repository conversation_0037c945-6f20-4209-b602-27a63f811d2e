package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.PplDataBaseFrameworkEnum;
import cloud.demand.lab.modules.operation_view.operation_view.model.ReturnT;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DownloadTemplateReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.ImportAdjustDemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SimpleDemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.VersionReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandTrendReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.PplOrderDemandTrendData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl.DemandExcelStrategy;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/11 18:17
 */
@Slf4j
@JsonrpcController("/supply-demand/demand/report")
public class DemandController {

    @Resource
    private DemandService demandService;

    @Resource
    private DemandExcelStrategy demandExcelStrategy;

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;

    @RequestMapping
    public List<DemandReportDetailVO> queryReport(@JsonrpcParam DemandReportReq req) {

        supplyAndDemandDictService.addUnclassified(req);
        req.setStatTimeList(ListUtils.newArrayList(req.getStartStatTime(), req.getEndStatTime()));
        return demandService.queryReport(req);
    }

    @RequestMapping
    public List<DemandReportDetailVO> queryCbsSimpleReport(@JsonrpcParam SimpleDemandReportReq req) {
        VersionReq versionReq = new VersionReq();
        versionReq.setProductCategory(ProductCategoryEnum.CBS.getName());
        versionReq.setVersionCode(req.getStatTime());
        versionReq.setType("demand");
        String fixStatTime = supplyAndDemandDictService.getDefinitiveVersion(versionReq);

        req.setStatTime(fixStatTime);
        DemandReportReq reportReq = DemandReportReq.transform(req);

        return demandService.queryReport(reportReq);
    }

    @RequestMapping
    public List<PplOrderDemandTrendData> queryTrend(@JsonrpcParam DemandTrendReq req) {
        supplyAndDemandDictService.addUnclassified(req);
        return demandService.queryTrend(req);
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam SupplyDemandParamTypeReq req) {
        return demandService.queryParams(req);
    }

    @RequestMapping
    public Object allPplDataBaseFramework() {
        return PplDataBaseFrameworkEnum.allFrameworks();
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> exportDemandDetail(@JsonrpcParam DemandReportReq req) {
        supplyAndDemandDictService.addUnclassified(req);
        return demandExcelStrategy.exportDemand(req);
    }


    @RequestMapping
    public DownloadBean downloadDemand(@JsonrpcParam DownloadTemplateReq req) {
        return demandExcelStrategy.downloadTemplate(req);
    }

    @RequestMapping
    public ReturnT<List<ErrorMessage>> importAdjustDemand(@RequestParam("file") MultipartFile file,
                                                          @RequestParam("statTime") String statTime,
                                                          @RequestParam("productCategory")  String productCategory) {
        ImportAdjustDemandReq req = new ImportAdjustDemandReq(statTime, productCategory);
        return demandExcelStrategy.importAdjustDemand(file,req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> exportDemand(@JsonrpcParam DemandReportReq req) {
        supplyAndDemandDictService.addUnclassified(req);
        return demandExcelStrategy.exportDemand(req);
    }

}
