package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/27 16:54
 */
@Getter
@AllArgsConstructor
public enum InstanceCategoryEnum {

    EOL("EOL", "EOL机型"),
    ON_SALE("ON_SALE", "在售非主力机型"),
    WHITE_LISTED("WHITE_LISTED", "白名单机型"),

    PRINCIPAL("PRINCIPAL", "主力机型"),
    UNDEFINED("undefined", "未分类"),
    ;

    private final String type;

    private final String typeName;


    public static InstanceCategoryEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (InstanceCategoryEnum value : values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }

    public static InstanceCategoryEnum getByTypeName(String typeName) {
        if (typeName == null) {
            return null;
        }
        for (InstanceCategoryEnum value : values()) {
            if (Objects.equals(value.getTypeName(), typeName)) {
                return value;
            }
        }
        return null;
    }

    public static String getTypeNameByType(String type) {
        InstanceCategoryEnum e = getByType(type);
        return e == null ? "" : e.getTypeName();
    }

    public static String getTypeByTypeName(String typeName) {
        InstanceCategoryEnum e = getByTypeName(typeName);
        return e == null ? "" : e.getType();
    }

}
