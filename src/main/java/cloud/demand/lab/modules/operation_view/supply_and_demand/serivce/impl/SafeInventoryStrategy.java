package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SafeInventoryService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/4/7 16:47
 */
@Service
public class SafeInventoryStrategy implements InitializingBean {

    @Resource
    private List<SafeInventoryService> safeInventoryServices;

    private final Map<String, SafeInventoryService> safeInventoryMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        if (ListUtils.isEmpty(safeInventoryServices)) {
            return;
        }
        // bean初始化完成后，扫描所有的inventoryTargetServices，注册进来
        for (SafeInventoryService service : safeInventoryServices) {
            safeInventoryMap.put(service.getProductCategory(), service);
        }
    }

    public List<SupplyDemandHedgingItemVO> querySafeInventory(SupplyDemandHedgingReq req, String productCategory) {
        SafeInventoryService service = getService(productCategory);
        return service.queryActSafeInventory(req);
    }

    private SafeInventoryService getService(String productCategory) {
        SafeInventoryService service = safeInventoryMap.get(productCategory);
        if (Objects.isNull(service)) {
            throw new BizException("未找到对应的productCategory:" + productCategory);
        }
        return service;
    }

}
