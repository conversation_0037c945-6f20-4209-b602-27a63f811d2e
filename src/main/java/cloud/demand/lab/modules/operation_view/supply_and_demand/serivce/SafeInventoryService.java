package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:05
 */
public interface SafeInventoryService {

    List<SupplyDemandHedgingItemVO> queryActSafeInventory(SupplyDemandHedgingReq req);

    String getProductCategory();
}
