package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/4/12 22:43
 */
@Data
public class DemandReportDetailVO implements ZoneCategoryFiller, ZoneInfoFiller, InstanceCategoryFiller, InstanceGroupFiller, CountryNameFiller {

    private String statTime;

    private String yearMonth;

    private String dataSource;

    private String billNumber;

    private String productCategory;

    private String product;

    private String industryDept;

    private String warZone;

    private String commonCustomerShortName;

    private String customerUin;

    private Integer isInner;

    private String projectName;

    private String demandScene;

    private String demandType;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneCategory;

    private String zoneName;

    private String instanceCategory;

    private String instanceGroup;

    private String instanceType;

    private String beginBuyDate;

    private String endBuyDate;

    private String dataType = "需求";//数据类型：订单、PPL、规模，默认规模

    private String volumeType;

    private String diskType;

    private String dbStorageType;

    private String dbDeployType;

    private String dbFrameworkType;

    private BigDecimal totalAmount;

    //当前月核心数
    private BigDecimal currentMonthAmount;

    //跨月核心数
    private BigDecimal crossMonthAmount;

    private BigDecimal waitBuyAmount;
    private BigDecimal buyAmount;

    private BigDecimal totalNum;

    private boolean lastYear = false;

    private String remark;

    private String errorMsg;

    private String intervene;

    private String projectType; //项目类型

    public static DemandReportDetailVO builder(String unit, ProductDemandDataDfDO current, ProductDemandDataDfDO cross, Function<ProductDemandDataDfDO, BigDecimal> totalAmountGetter,
                                               Function<ProductDemandDataDfDO, BigDecimal> waitBuyAmountGetter, Function<ProductDemandDataDfDO, BigDecimal> buyAmountGetter,
                                               Function<ProductDemandDataDfDO, BigDecimal> numAmountGetter) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        if (Objects.nonNull(current)) {
            BeanUtils.copyProperties(current, ret);
        } else {
            BeanUtils.copyProperties(cross, ret);
        }
        BigDecimal currentAmount = BigDecimal.ZERO;
        BigDecimal buyCurrentAmount = BigDecimal.ZERO;
        BigDecimal waitBuyCurrentAmount = BigDecimal.ZERO;
        BigDecimal currentNum = BigDecimal.ZERO;

        BigDecimal crossAmount = BigDecimal.ZERO;
        BigDecimal buyCrossAmount = BigDecimal.ZERO;
        BigDecimal waitBuyCrossAmount = BigDecimal.ZERO;
        BigDecimal crossNum = BigDecimal.ZERO;

        if (Objects.nonNull(current)) {
            currentAmount = totalAmountGetter.apply(current);
            waitBuyCurrentAmount = waitBuyAmountGetter.apply(current);
            buyCurrentAmount = buyAmountGetter.apply(current);
            currentNum = numAmountGetter.apply(current);
        }

        if (Objects.nonNull(cross)) {
            crossAmount = totalAmountGetter.apply(cross);
            waitBuyCrossAmount = waitBuyAmountGetter.apply(cross);
            buyCrossAmount = buyAmountGetter.apply(cross);
            crossNum = numAmountGetter.apply(cross);
        }
        if (StringUtils.equals(unit, UnitEnum.STORAGE.getName())) {
            //GB --> TB
            crossAmount = SoeCommonUtils.divide(crossAmount, BigDecimal.valueOf(1024));
            //GB --> TB
            currentAmount = SoeCommonUtils.divide(currentAmount, BigDecimal.valueOf(1024));
            //GB --> TB
            waitBuyCrossAmount = SoeCommonUtils.divide(waitBuyCrossAmount, BigDecimal.valueOf(1024));
            //GB --> TB
            waitBuyCurrentAmount = SoeCommonUtils.divide(waitBuyCurrentAmount, BigDecimal.valueOf(1024));
            //GB --> TB
            buyCurrentAmount = SoeCommonUtils.divide(buyCurrentAmount, BigDecimal.valueOf(1024));
            //GB --> TB
            buyCrossAmount = SoeCommonUtils.divide(buyCrossAmount, BigDecimal.valueOf(1024));
        }
        ret.setCrossMonthAmount(crossAmount);
        ret.setCurrentMonthAmount(currentAmount);
        ret.setTotalAmount(SoeCommonUtils.addWithNull(crossAmount, currentAmount));
        ret.setWaitBuyAmount(SoeCommonUtils.addWithNull(waitBuyCrossAmount, waitBuyCurrentAmount));
        ret.setBuyAmount(SoeCommonUtils.addWithNull(buyCrossAmount, buyCurrentAmount));
        ret.setTotalNum(SoeCommonUtils.addWithNull(crossNum, currentNum));
        return ret;
    }

    public static DemandReportDetailVO transform(DayCvmScaleVO item) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        BeanUtils.copyProperties(item, ret);
        ret.setProductCategory(ProductCategoryEnum.CVM.getName());
        ret.setDataType("规模");
        ret.setDataSource("规模");
        if (StringUtils.equals(item.getStatTime().substring(0, 4), LocalDate.now().minusYears(1).getYear() + "")) {
            ret.setLastYear(true);
        }
        if (StringUtils.equals(item.getCustomerUin(), "0")) {
            item.setCustomerUin(Constant.EMPTY_STR);
        }
        return ret;
    }

    public static DemandReportDetailVO transform(DayCbsScaleVO currentMonth, DayCbsScaleVO lastMouth) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        BeanUtils.copyProperties(currentMonth, ret);
        ret.setProductCategory(ProductCategoryEnum.CBS.getName());
        ret.setDataType("规模");
        ret.setDataSource("规模");
        if (StringUtils.equals(currentMonth.getStatTime().substring(0, 4), LocalDate.now().minusYears(1).getYear() + "")) {
            ret.setLastYear(true);
        }
        ret.setYearMonth(currentMonth.getStatTime().substring(0, 7));
        BigDecimal totalAmount = currentMonth.getCurAmount();
        BigDecimal lastAmount = BigDecimal.ZERO;
        if (Objects.nonNull(lastMouth)) {
            lastAmount = lastMouth.getCurAmount();
        }
        totalAmount = SoeCommonUtils.multiply(SoeCommonUtils.sub(totalAmount, lastAmount), BigDecimal.valueOf(1024));
        ret.setTotalAmount(totalAmount);
        return ret;
    }

    public static DemandReportDetailVO transform(DayCbsScaleVO item) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        BeanUtils.copyProperties(item, ret);
        ret.setDataType("规模");
        ret.setDataSource("规模");
        ret.setProductCategory(ProductCategoryEnum.CBS.getName());
        if (StringUtils.equals(item.getStatTime().substring(0, 4), LocalDate.now().minusYears(1).getYear() + "")) {
            ret.setLastYear(true);
        }
        ret.setYearMonth(item.getStatTime().substring(0, 7));
        BigDecimal totalAmount = SoeCommonUtils.multiply(item.getCurAmount(), BigDecimal.valueOf(1024));
        ret.setTotalAmount(totalAmount);
        return ret;
    }

    public static DemandReportDetailVO transform(DayDbScaleVO item) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        BeanUtils.copyProperties(item, ret);
        ret.setDataType("规模");
        ret.setDataSource("规模");
        ret.setProductCategory(ProductCategoryEnum.DB.getName());
        if (StringUtils.equals(item.getStatTime().substring(0, 4), LocalDate.now().minusYears(1).getYear() + "")) {
            ret.setLastYear(true);
        }
        ret.setYearMonth(item.getStatTime().substring(0, 7));
        ret.setTotalAmount(item.getCurAmount());
        return ret;
    }

    public static DemandReportDetailVO transform(DayDbScaleVO currentMonth, DayDbScaleVO lastMouth) {
        DemandReportDetailVO ret = new DemandReportDetailVO();
        BeanUtils.copyProperties(currentMonth, ret);
        ret.setProductCategory(ProductCategoryEnum.DB.getName());
        ret.setDataType("规模");
        ret.setDataSource("规模");
        if (StringUtils.equals(currentMonth.getStatTime().substring(0, 4), LocalDate.now().minusYears(1).getYear() + "")) {
            ret.setLastYear(true);
        }
        ret.setYearMonth(currentMonth.getStatTime().substring(0, 7));
        BigDecimal totalAmount = currentMonth.getCurAmount();
        BigDecimal lastAmount = BigDecimal.ZERO;
        if (Objects.nonNull(lastMouth)) {
            lastAmount = lastMouth.getCurAmount();
        }
        totalAmount = SoeCommonUtils.sub(totalAmount, lastAmount);
        ret.setTotalAmount(totalAmount);
        return ret;
    }

    @Override
    public String provideInstanceType() {
        return this.getInstanceType();
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.setInstanceGroup(instanceGroup);
    }

    @Override
    public String provideCustomhouseTitle() {
        return this.customhouseTitle;
    }

    @Override
    public void fillInstanceCategory(String instanceCategory) {
        this.instanceCategory = instanceCategory;
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public void fillZone(String zone) {

    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.customhouseTitle = customhouseTitle;
    }

    @Override
    public void fillRegionName(String regionName) {
        this.regionName = regionName;
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }
}
