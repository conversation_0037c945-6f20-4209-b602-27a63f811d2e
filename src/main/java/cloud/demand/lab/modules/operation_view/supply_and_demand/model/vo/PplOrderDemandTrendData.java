package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class PplOrderDemandTrendData{

    private String statTime;

    private String yearMonth;

    private BigDecimal totalAmount;

    private int dataType;//1:去年同期  2:过去4个月  3、未来4个

    public static PplOrderDemandTrendData transform(DemandReportDetailVO detail, String statTime, boolean isDemand){
        PplOrderDemandTrendData trendData = new PplOrderDemandTrendData();
        trendData.setStatTime(statTime);
        trendData.setYearMonth(detail.getYearMonth());
        trendData.setTotalAmount(detail.getTotalAmount());

        if(isDemand){
            trendData.setDataType(3);
        }else {
            //过去4个月的规模
            String yearMonth = LocalDate.now().minusMonths(4).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            if(StringUtils.compare(detail.getYearMonth(),yearMonth)  < 0){
                trendData.setDataType(1);
            }else {
                trendData.setDataType(2);
            }
        }
        return trendData;
    }
}
