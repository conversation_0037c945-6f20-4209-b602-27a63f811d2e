package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.FailAdjustPosConfig;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.OriginAdjustPosConfig;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.PlanProductData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyOnTheWayDetailData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyReportDetailReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyReportTrendData;
import java.util.List;

public interface SupplyOnTheWayReportService {

    void adjustPositionByConfig(String version, String productType);

    List<FailAdjustPosConfig> importAdjustPositionConfig(String version, String productType, List<OriginAdjustPosConfig> configs);

    List<OriginAdjustPosConfig> exportAdjustPositionConfig(String version, String productType);

    List<SupplyOnTheWayDetailData> querySupplyOnTheWayDetailReport(SupplyReportDetailReq req);

    List<SupplyReportTrendData> querySupplyOnTheWayTrendReport(SupplyReportDetailReq req);

    DownloadBean exportSupplyDetailExcel(SupplyReportDetailReq req);

    List<String> queryAllSupplyCampus();

    List<PlanProductData> queryProductAndPlanProduct();

    List<String> queryParams(SupplyDemandParamTypeReq req);

    List<ProductVO> queryProduct();

}
