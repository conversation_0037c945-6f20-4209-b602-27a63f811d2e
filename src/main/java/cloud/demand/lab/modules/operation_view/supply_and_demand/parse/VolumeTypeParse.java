package cloud.demand.lab.modules.operation_view.supply_and_demand.parse;


import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.VolumeTypeEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/14 14:19
 */
public class VolumeTypeParse implements IWhereParser {
    @Override
    public void parseSQL(WhereSQL content, WhereBuilder.WhereInfo whereInfo, Object t) {
        Object v = whereInfo.getV();
        List<String> ls = (List<String>) v;
        if (ListUtils.isNotEmpty(ls)) {
            String volumeTypeColumn = whereInfo.getParseParams()[0]; //
            List<String> deviceType = ListUtils.newArrayList();
            for (String item : ls) {
                deviceType.addAll(VolumeTypeEnum.getByName(item).getDeviceType());
            }
            content.andIf(ListUtils.isNotEmpty(deviceType), volumeTypeColumn + " in (?)", deviceType);
        }
    }
}
