package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import java.time.LocalDate;
import java.util.List;

public interface SupplyAndDemandGenService {

    void genSupplyOnTheWayData(String statTime);


    void synchronizePositionConfig(String statTime, String productType);


    void syncDemandData(LocalDate statTime,List<String> productCategoryList);

    void syncCvmDemandData(LocalDate statTime,boolean forceSync);

    void syncGpuDemandData(LocalDate statTime,boolean forceSync);

    void syncRandomZoneName(LocalDate statTime);

    void syncPplJoinOrderNewestData(String statTime,boolean forceSync);

    void syncInventoryData(String statTime);

    void genInventoryTargetData(String statTime);

    void genDbSaleScaleData(String statTime);

    void repairData(String statTime);
}
