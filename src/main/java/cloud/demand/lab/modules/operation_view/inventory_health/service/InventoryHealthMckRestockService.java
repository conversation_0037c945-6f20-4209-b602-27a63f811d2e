package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.*;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import java.util.List;

public interface InventoryHealthMckRestockService {
    QueryMckRestockResp queryMckRestockReport(QueryMckRestockReq req);
    QueryMckRestockInventoryDetailResp queryMckRestockReportInventoryDetail(QueryMckRestockDetailReq req);
    QueryMckRestockSupplyDetailResp queryMckRestockReportSupplyDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType);
    QueryMckRestockForecastDetailResp queryMckRestockReportForecastDetail(QueryMckRestockForecastReq req, List<CvmType> allCvmType);
    Object configureMckRestockManualConfig(ConfigureMckRestockManualConfigReq req);
    void snapshotMckRestockManualConfig(String date);
    QueryManualConfigDetailResp queryManualConfigDetail(QueryManualConfigDetailReq req);

    QueryOperationActionDetailResp queryOperationActionDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType);

    QueryTotalDetailSummaryResp queryTotalDetailSummary(QueryTotalDetailSummaryReq req);
}
