package cloud.demand.lab.modules.operation_view.operation_view_old.model;

import cloud.demand.lab.modules.operation_view.operation_view_old.enums.GroupByEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class OperationViewReq {

    /**产品类型，单选*/
    private String productType;

    /** 日期 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /** 月均消耗算法
     * */
    private String monthAvgAlgorithm;

    /** 线上线下，单选，选项：ONLINE、OFFLINE*/
    private String lineType;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 区域 */
    private List<String> areaName;

    /** region */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /** 机型族 */
    private List<String> deviceFamily;

    /** 设备类型 */
    private List<String> deviceType;

    /** CPU规格 */
    private List<String> cpuCategory;

    /** 好差呆 */
    private List<String> materialType;

    /** 聚合字段
     * productType 必须提供，如果没有提供，后台会自动加在首位
     * @see GroupByEnum
     **/
    private List<String> groupBy;


    public void fillWhere(WhereSQL where) {
        where.and("stat_time=?", DateUtils.toLocalDate(this.getDate()));
        where.and("product_type=?", this.getProductType());
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            where.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(areaName)) {
            where.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            where.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            where.and("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(deviceFamily)) {
            where.and("device_family in (?)", deviceFamily);
        }
        if (ListUtils.isNotEmpty(deviceType)) {
            where.and("device_type in (?)", deviceType);
        }
        if (ListUtils.isNotEmpty(cpuCategory)) {
            where.and("cpu_category in (?)", cpuCategory);
        }
        if (ListUtils.isNotEmpty(materialType)){
            where.and("material_type in (?)", materialType);
        }
    }

}
