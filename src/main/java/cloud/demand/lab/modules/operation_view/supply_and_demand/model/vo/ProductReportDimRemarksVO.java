package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductReportDimRemarksDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * 产品供应看板原因分析
 */
@Data
@ToString
public class ProductReportDimRemarksVO {

    private Long id;

    /** 产品：CVM、CBS等<br/>Column: [product_category] */
    @Column(value = "product_category")
    private String productCategory;

    /** 版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 数据类型:采购-supply、需求-demand<br/>Column: [data_type] */
    @Column(value = "data_type")
    private String dataType;

    /** 维度值集合,用@分割,开头结尾都有@<br/>Column: [dim_key] */
    @Column(value = "dim_key")
    private String dimKey;

    /** 维度值集合，用@分割,开头结尾都有@。如果是总结，维度值不传<br/>Column: [dim_value] */
    @Column(value = "dim_value")
    private String dimValue;

    /** 备注<br/>Column: [remarks] */
    @Column(value = "remarks")
    private String remarks;

    public static ProductReportDimRemarksVO transform(ProductReportDimRemarksDO entity){
        ProductReportDimRemarksVO ret = new ProductReportDimRemarksVO();
        BeanUtils.copyProperties(entity,ret);
        return ret;
    }
}