package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SafeInventoryService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:05
 */
@Service
public class SafeInventoryCbsServiceImpl implements SafeInventoryService {

    @Override
    public List<SupplyDemandHedgingItemVO> queryActSafeInventory(SupplyDemandHedgingReq req) {
        return ListUtils.newArrayList();
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CBS.getName();
    }
}
