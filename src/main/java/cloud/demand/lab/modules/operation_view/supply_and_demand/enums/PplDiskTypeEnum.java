package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import com.google.common.collect.ImmutableSet;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Getter
public enum PplDiskTypeEnum {

    UNKNOWN("UNKNOWN", "未知"),
    //    CLOUD_PREMIUM("CLOUD_PREMIUM", "高性能"),
    CLOUD_SSD("CLOUD_SSD", "SSD"),
    CLOUD_PREMIUM("CLOUD_PREMIUM", "高性能云硬盘"),
    NEW_CLOUD_SSD("CLOUD_SSD", "SSD云硬盘"),
    CLOUD_BSSD("CLOUD_BSSD", "通用型SSD云硬盘"),
    CLOUD_HSSD("CLOUD_HSSD", "增强型SSD云硬盘"),
    CLOUD_TSSD("CLOUD_TSSD", "极速型SSD云硬盘"),

    CLOUD_BIGDATA("CLOUD_BIGDATA", "大数据云盘"),

    CLOUD_HIGHIO("CLOUD_HIGHIO", "高IO型云硬盘"),
    ;

    public static final Set<String> diskNameSet = ImmutableSet.of(CLOUD_PREMIUM.name, CLOUD_SSD.name);
    public static final List<String> systemDiskNameList = Arrays.asList(CLOUD_PREMIUM.getName(),
            CLOUD_SSD.getName(), NEW_CLOUD_SSD.getName(), CLOUD_BSSD.getName(),
            CLOUD_HSSD.getName());

    public static final Set<String> ssdCodeList = ImmutableSet.of(CLOUD_SSD.getCode(),
            NEW_CLOUD_SSD.getCode(), CLOUD_BSSD.getCode(), CLOUD_HSSD.getCode(),
            CLOUD_HSSD.getCode());

    public static final Set<String> premiumCodeList = ImmutableSet.of(CLOUD_PREMIUM.getCode());

    public static final Set<String> ssdANdPremiumCodeList = ImmutableSet.of(CLOUD_SSD.getCode(),
            NEW_CLOUD_SSD.getCode(), CLOUD_BSSD.getCode(), CLOUD_HSSD.getCode(),
            CLOUD_HSSD.getCode(),CLOUD_PREMIUM.getCode());

    public static final List<String> dataDiskNameList
            = Arrays.asList(CLOUD_PREMIUM.getName(), CLOUD_SSD.getName(),
            NEW_CLOUD_SSD.getName(), CLOUD_BSSD.getName(), CLOUD_HSSD.getName(), CLOUD_TSSD.getName(),
            CLOUD_BIGDATA.getName(), CLOUD_HIGHIO.getName());

    private final String code;
    private final String name;

    PplDiskTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplDiskTypeEnum getByName(String name) {
        for (PplDiskTypeEnum e : PplDiskTypeEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static PplDiskTypeEnum getByCode(String code) {
        for (PplDiskTypeEnum e : PplDiskTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getNameByCode(String code) {
        PplDiskTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        PplDiskTypeEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }



}