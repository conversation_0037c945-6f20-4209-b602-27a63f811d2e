package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/13 13:40
 */
@Data
public class DayCbsScaleVO {
    @Column(value = "stat_time")
    private String statTime;

    @Column(value = "year_month")
    private String yearMonth;

    @Column(value = "any_customhouse_title")
    private String customhouseTitle;

    @Column(value = "any_country_name")
    private String countryName;

    @Column(value = "any_area_name")
    private String areaName;

    @Column(value = "any_region_name")
    private String regionName;

    @Column(value = "any_zone_category")
    private String zoneCategory;

    @Column(value = "any_zone_name")
    private String zoneName;

    @Column(value = "any_volume_type")
    private String volumeType;

    private BigDecimal totalAmount;

    @Column(value = "cur_amount")
    private BigDecimal curAmount;
}
