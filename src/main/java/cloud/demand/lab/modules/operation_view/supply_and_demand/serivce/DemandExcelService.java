package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.operation_view.model.ReturnT;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DownloadTemplateReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.ImportAdjustDemandReq;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.SneakyThrows;
import org.nutz.lang.Strings;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/24 15:05
 */
public interface DemandExcelService {
    DownloadBean downloadTemplate(DownloadTemplateReq req);

    String getProductCategory();

    ResponseEntity<InputStreamResource> exportDemand(DemandReportReq req);

    ReturnT<List<ErrorMessage>> importAdjustDemand(MultipartFile file, ImportAdjustDemandReq req);

    default List<ProductDemandDataDfDO> checkReq(ImportAdjustDemandReq req){
        if (Strings.isBlank(req.getVersionCode())) {
            throw new BizException("需求校验版本号不能为空");
        }
        List<ProductDemandDataDfDO> sourceList = SpringUtil.getBean("ckcldStdCrpDBHelper", DBHelper.class).getAll(ProductDemandDataDfDO.class, " where stat_time = ? and demand_caliber = '共识需求' ", req.getVersionCode());
        if (ListUtils.isEmpty(sourceList)) {
            throw new BizException("需求版本号不存在");
        }
        if (SpringUtil.getBean("supplyAndDemandDictServiceImpl", SupplyAndDemandDictService.class).checkDefinitive(req.getVersionCode(), "demand", getProductCategory())) {
            throw new BizException("需求版本号已定为最终版本，不能再进行干预");
        }
        return sourceList;
    }
}
