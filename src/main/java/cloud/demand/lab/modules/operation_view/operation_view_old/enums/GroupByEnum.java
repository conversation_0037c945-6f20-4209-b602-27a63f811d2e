package cloud.demand.lab.modules.operation_view.operation_view_old.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum GroupByEnum {

    customhouseTitle("customhouseTitle", "境内外", "customhouse_title"),

    areaName("areaName", "区域", "area_name"),

    regionName("regionName", "地域", "region_name"),

    zoneName("zoneName", "可用区", "zone_name"),

    deviceType("deviceType", "设备类型", "device_type"),

    deviceFamily("deviceFamily", "机型族", "device_family"),

    cpuCategory("cpuCategory", "CPU规格", "cpu_category"),

    materialType("materialType", "好差呆", "material_type"),

    lineType("lineType", "线上线下", "")

    ;

    final private String code;
    final private String name;
    final private String column;

    GroupByEnum(String code, String name, String column) {
        this.code = code;
        this.name = name;
        this.column = column;
    }

    public static List<String> getGroupByColumn(List<String> groupBys) {
        List<String> columns = new ArrayList<>();
        ListUtils.forEach(groupBys, o -> {
            GroupByEnum group = GroupByEnum.getByCode(o);
            if (group != null && StringTools.isNotBlank(group.getColumn())) { // 线下线下目前没有作为底表的列存在，故由程序后处理
                columns.add(group.getColumn());
            }
        });
        return columns;
    }

    public static GroupByEnum getByCode(String code) {
        for (GroupByEnum e : GroupByEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        GroupByEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static void main(String[] args) {
        System.out.println(BillingScaleGroupByEnum.values());
    }

}
