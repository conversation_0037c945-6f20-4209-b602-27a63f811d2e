package cloud.demand.lab.modules.operation_view.operation_view_old.model;

import com.pugwoo.dbhelper.annotation.Column;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class QueryFieldsRsp {

    private List<String> data;

    private List<RegionalFieldDTO> regionalData;

    public QueryFieldsRsp() {
    }

    public QueryFieldsRsp(List<String> data) {
        this.data = data;
    }

    @Data
    public static class RegionalFieldDTO {
        @Column("customhouseTitle")
        private String customhouseTitle;

        @Column("areaName")
        private String areaName;

        @Column("regionName")
        private String regionName;

        @Column("zoneName")
        private String zoneName;
    }
}
