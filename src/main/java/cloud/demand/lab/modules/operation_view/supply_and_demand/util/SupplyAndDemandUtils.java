package cloud.demand.lab.modules.operation_view.supply_and_demand.util;

import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/27 13:10
 */
public class SupplyAndDemandUtils {

    public  static <T>  String getDimsGroupKey(T entity, List<String> dims) {
        StringBuffer bf = new StringBuffer();
        Field[] fields = entity.getClass().getDeclaredFields();
        for (String dim : dims) {
            for (Field field : fields) {
                if (StringUtils.equals(field.getName(), dim)) {
                    try {
                        field.setAccessible(true);
                        bf.append("@").append(field.get(entity));
                    } catch (IllegalAccessException e) {
                        throw new BizException("不合法的维度");
                    }
                    break;
                }
            }
        }
        return bf.toString();
    }
}
