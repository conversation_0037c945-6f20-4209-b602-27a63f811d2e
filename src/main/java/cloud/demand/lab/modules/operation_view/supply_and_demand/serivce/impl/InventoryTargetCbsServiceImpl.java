package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.CbsSafeStockByZoneVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryTargetService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:26
 */
@Service
public class InventoryTargetCbsServiceImpl implements InventoryTargetService {

    private final List<String> supportDims = ListUtils.newArrayList("volumeType", "customhouseTitle", "countryName", "areaName", "regionName", "zoneName", "zoneCategory");

    @Resource
    private DBHelper plancbsDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private FillerService fillerService;

    @Override
    public Map<String, InventoryTargetVO> getInventoryTarget(InventoryTargetReq req) {

        List<CbsSafeStockByZoneVO> data = getData(req.getStatTime());

        fillerService.fill(data);
        data.forEach(item -> {
            if (StringUtils.equals(item.getCustomhouseTitle(), "境内") && StringUtils.equals(item.getZoneCategory(), "主力园区")) {
                //国内主力园区，CBS库存阈值为1PB
                item.setStockThreshold(SoeCommonUtils.multiply(BigDecimal.ONE, new BigDecimal(1024)));
            } else {
                //国内非主力园区，CBS库存阈值为0.5PB
                item.setStockThreshold(SoeCommonUtils.multiply(new BigDecimal(0.5), new BigDecimal(1024)));
            }
        });

        data = data.stream().filter(item -> ListUtils.isEmpty(req.getCustomhouseTitle()) || ListUtils.contains(req.getCustomhouseTitle(), o -> StringUtils.equals(o, item.getCustomhouseTitle())))
                .filter(item -> ListUtils.isEmpty(req.getCountryName()) || ListUtils.contains(req.getCountryName(), o -> StringUtils.equals(o, item.getCountryName())))
                .filter(item -> ListUtils.isEmpty(req.getAreaName()) || ListUtils.contains(req.getAreaName(), o -> StringUtils.equals(o, item.getAreaName())))
                .filter(item -> ListUtils.isEmpty(req.getRegionName()) || ListUtils.contains(req.getRegionName(), o -> StringUtils.equals(o, item.getRegionName())))
                .filter(item -> ListUtils.isEmpty(req.getZoneName()) || ListUtils.contains(req.getZoneName(), o -> StringUtils.equals(o, item.getZoneName())))
                .filter(item -> ListUtils.isEmpty(req.getZoneCategory()) || ListUtils.contains(req.getZoneCategory(), o -> StringUtils.equals(o, item.getZoneCategory())))
                .filter(item -> ListUtils.isEmpty(req.getVolumeType()) || ListUtils.contains(req.getVolumeType(), o -> StringUtils.equals(o, item.getVolumeType())))
                .collect(Collectors.toList());

        List<String> dims = new ArrayList<>(req.getDims());
        dims.removeIf(item -> !supportDims.contains(item));
        dims.add("addDate");
        List<String> sumFieldList = ListUtils.newArrayList("safeStock", "stockThreshold");
        Map<String, InventoryTargetVO> retMap = new HashMap<>();
        ListUtils.groupBy(data, item -> GroupUtils.getDimsGroupKey(item, dims))
                .forEach((key, value) -> {
                    CbsSafeStockByZoneVO cbsSafeStockByZoneVO = GroupUtils.mergeList(value, dims, sumFieldList);
                    String groupKey = cbsSafeStockByZoneVO.getInventoryTargetGroupKey();
                    InventoryTargetVO inventoryTargetVO = new InventoryTargetVO(groupKey, cbsSafeStockByZoneVO.getSafeStock(), cbsSafeStockByZoneVO.getStockThreshold());
                    retMap.put(groupKey, inventoryTargetVO);
                });
        return retMap;
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CBS.getName();
    }

    @Override
    public Boolean matchFlag(InventoryTargetReq req) {
        List<String> dims = req.getDims();
        if (ListUtils.contains(dims, item -> !ListUtils.contains(supportDims, o -> StringUtils.equals(item, o)))) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private List<CbsSafeStockByZoneVO> getData(List<String> statTime) {
        List<CbsSafeStockByZoneVO> ret = getFromSysMan(statTime);

        List<String> groupKeys = ret.stream().map(CbsSafeStockByZoneVO::getInventoryTargetGroupKey).collect(Collectors.toList());

        List<CbsSafeStockByZoneVO> planData = getFromPlan(statTime);
        planData.removeIf(item -> ListUtils.contains(groupKeys, o -> StringUtils.equals(o, item.getInventoryTargetGroupKey())));

        ret.addAll(planData);
        return ret;
    }

    private List<CbsSafeStockByZoneVO> getFromPlan(List<String> statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_cbs_inventory_target_plan.sql");
        return plancbsDBHelper.getRaw(CbsSafeStockByZoneVO.class, sql, statTime);
    }

    private List<CbsSafeStockByZoneVO> getFromSysMan(List<String> statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_cbs_inventory_target_sysman.sql");
        return demandDBHelper.getRaw(CbsSafeStockByZoneVO.class, sql, statTime, statTime);
    }
}
