package cloud.demand.lab.modules.operation_view.operation_view_old.utils;

import cloud.demand.lab.common.utils.AmountUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 相关的工具
 */
public class OperationViewTools {

    public static double normsinv(double p) {
        // 服务水平是 0 时，服务系数也是 0
        if (p == 0.0) {
            return 0;
        }

        double LOW = 0.02425;
        double HIGH = 0.97575;

        double a[] = { -3.969683028665376e+01, 2.209460984245205e+02,
                -2.759285104469687e+02, 1.383577518672690e+02,
                -3.066479806614716e+01, 2.506628277459239e+00 };

        double b[] = { -5.447609879822406e+01, 1.615858368580409e+02,
                -1.556989798598866e+02, 6.680131188771972e+01,
                -1.328068155288572e+01 };

        double c[] = { -7.784894002430293e-03, -3.223964580411365e-01,
                -2.400758277161838e+00, -2.549732539343734e+00,
                4.374664141464968e+00, 2.938163982698783e+00 };

        double d[] = { 7.784695709041462e-03, 3.224671290700398e-01,
                2.445134137142996e+00, 3.754408661907416e+00 };

        double q, r;

        if (p < LOW) {
            q = Math.sqrt(-2 * Math.log(p));
            return (((((c[0] * q + c[1]) * q + c[2]) * q + c[3]) * q + c[4])
                    * q + c[5])
                    / ((((d[0] * q + d[1]) * q + d[2]) * q + d[3]) * q + 1);
        } else if (p > HIGH) {
            q = Math.sqrt(-2 * Math.log(1 - p));
            return -(((((c[0] * q + c[1]) * q + c[2]) * q + c[3]) * q + c[4])
                    * q + c[5])
                    / ((((d[0] * q + d[1]) * q + d[2]) * q + d[3]) * q + 1);
        } else {
            q = p - 0.5;
            r = q * q;
            return (((((a[0] * r + a[1]) * r + a[2]) * r + a[3]) * r + a[4])
                    * r + a[5])
                    * q
                    / (((((b[0] * r + b[1]) * r + b[2]) * r + b[3]) * r + b[4])
                    * r + 1);
        }
    }

    /**
     * 计算标准差
     */
    public static BigDecimal calculateSD(List<BigDecimal> data) {
        if (ListUtils.isEmpty(data) || data.size() == 1) {
            return BigDecimal.ZERO;
        }
        data.forEach(d -> d = (d == null ? BigDecimal.ZERO : d));

        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal standardDeviation = BigDecimal.ZERO;

        for(BigDecimal num : data) {
            sum = sum.add(num);
        }
        BigDecimal mean = AmountUtils.divideScale6(sum, BigDecimal.valueOf(data.size()));

        for(BigDecimal num : data) {
            standardDeviation = standardDeviation.add(BigDecimal.valueOf(Math.pow(num.subtract(mean).doubleValue(), 2)));
        }
        return BigDecimal.valueOf(Math.sqrt(standardDeviation.doubleValue()/data.size()));
    }

    /**
     * 计算给定的两个值的标准差
     */
    public static BigDecimal calculateSD(List<BigDecimal> data1, List<BigDecimal> data2) {
        if (ListUtils.isEmpty(data1) || data1.size() == 1) {
            return BigDecimal.ZERO;
        }

//        BigDecimal sum = BigDecimal.ZERO;
        //BigDecimal standardDeviation = BigDecimal.ZERO;

//        for(BigDecimal num : data) {
//            sum = sum.add(num);
//        }
//        BigDecimal mean = AmountUtils.divideScale6(sum, BigDecimal.valueOf(data.size()));
        List<BigDecimal> data = new ArrayList<>();
        for(int i = 0; i < data1.size(); i++) {
            BigDecimal d1 = data1.get(i);
            BigDecimal d2 = data2.get(i);
            data.add(d1.subtract(d2));
        }
        return calculateSD(data);
    }

    /**
     * 加法运算
     */
    public static BigDecimal add(BigDecimal... num){
        if (num == null){
            return BigDecimal.ZERO;
        }
        BigDecimal ret = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : num) {
            ret = ret.add(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
        }
        return ret;
    }

}
