package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingResp;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/12 17:47
 */
@Data
public class ExportInventoryRespVO {

    private List<List<String>> headList;

    private List<List<Object>> dataList;

    public static ExportInventoryRespVO build(String productCategory, List<SupplyDemandHedgingResp> data, List<String> yearMonth) {
        ExportInventoryRespVO vo = new ExportInventoryRespVO();
        vo.setHeadList(buildHeadList(productCategory, yearMonth));
        vo.setDataList(buildDataList(productCategory, data));
        return vo;
    }

    private static List<List<String>> buildHeadList(String productCategory, List<String> yearMonth) {
        List<List<String>> headList = ListUtils.newArrayList();
        headList.add(ListUtils.newArrayList("版本"));
        headList.add(ListUtils.newArrayList("可用区类型"));
        if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
            headList.add(ListUtils.newArrayList("机型类型"));
        }
        headList.add(ListUtils.newArrayList("境内外"));
        headList.add(ListUtils.newArrayList("国家"));
        headList.add(ListUtils.newArrayList("区域"));
        headList.add(ListUtils.newArrayList("地域"));
        headList.add(ListUtils.newArrayList("可用区"));
        if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
            headList.add(ListUtils.newArrayList("机型族"));
            headList.add(ListUtils.newArrayList("实例类型"));
        } else if (StringUtils.equals(productCategory, ProductCategoryEnum.CBS.getName())) {
            headList.add(ListUtils.newArrayList("云盘类型"));
            headList.add(ListUtils.newArrayList("项目类型"));
        }
        headList.add(ListUtils.newArrayList("期初库存\n总计库存"));
        if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
            headList.add(ListUtils.newArrayList("期初库存\n用户预扣"));
            headList.add(ListUtils.newArrayList("期初库存\n安全库存"));
        }
        for (String item : yearMonth) {
            headList.add(ListUtils.newArrayList("需求\n" + item));
        }
        for (String item : yearMonth) {
            headList.add(ListUtils.newArrayList("采购在途\n" + item));
        }
        for (String item : yearMonth) {
            headList.add(ListUtils.newArrayList("期末库存\n" + item + "月底"));
        }
        headList.add(ListUtils.newArrayList("库存目标"));

        return headList;
    }

    private static List<List<Object>> buildDataList(String productCategory, List<SupplyDemandHedgingResp> data) {
        List<List<Object>> rows = ListUtils.newArrayList();
        for (SupplyDemandHedgingResp item : data) {
            List<Object> row = ListUtils.newArrayList();
            row.add(item.getStatTime());
            row.add(item.getZoneCategory());
            if(StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
                row.add(item.getInstanceCategory());
            }
            row.add(item.getCustomhouseTitle());
            row.add(item.getCountryName());
            row.add(item.getAreaName());
            row.add(item.getRegionName());
            row.add(item.getZoneName());
            if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
                row.add(item.getInstanceGroup());
                row.add(item.getInstanceType());
            } else if (StringUtils.equals(productCategory, ProductCategoryEnum.CBS.getName())) {
                row.add(item.getVolumeType());
                row.add(item.getProjectType());
            }
            row.add(item.getBeginInventory());
            if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
                row.add(item.getWithholdInventory());
                row.add(item.getSafetyInventory());
            }

            for (SupplyDemandHedgingResp.Item respItem : item.getDemand()) {
                row.add(respItem.getTotalAmount());
            }
            for (SupplyDemandHedgingResp.Item respItem : item.getSupply()) {
                row.add(respItem.getTotalAmount());
            }
            for (SupplyDemandHedgingResp.Item respItem : item.getEndInventory()) {
                row.add(respItem.getTotalAmount());
            }
            row.add(item.getTargetInventory());
            rows.add(row);
        }
        return rows;
    }
}
