package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductReportDimRemarksVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/16 14:40
 */
public interface ProductReportDimRemarksService {

    /**
     * 查询备注
     *
     * @param req 请求参数
     * @return 返回的备注
     */
    List<ProductReportDimRemarksVO> queryRemarks(QueryRemarksReq req);

    /**
     * 创建备注
     *
     * @param req 请求参数
     */
    int createRemarks(CreateRemarksReq req);

    /**
     * 更新备注
     *
     * @param req 请求参数
     */
    int updateRemarks(UpdateRemarksReq req);

    /**
     * 通过id主键组删除备注
     *
     * @param ids 待删除id组
     */
    int deleteRemarks(List<Long> ids);
}
