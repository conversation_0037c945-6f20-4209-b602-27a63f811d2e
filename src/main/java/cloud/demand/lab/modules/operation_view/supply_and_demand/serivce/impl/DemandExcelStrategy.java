package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.operation_view.model.ReturnT;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DownloadTemplateReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.ImportAdjustDemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetContext;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandExcelService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryTargetService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/4/24 18:37
 */
@Service
public class DemandExcelStrategy implements InitializingBean {
    @Resource
    private List<DemandExcelService> demandExcelServices;

    private final Map<String, DemandExcelService> demandExcelMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        if (ListUtils.isEmpty(demandExcelServices)) {
            return;
        }
        // bean初始化完成后，扫描所有的demandExcelServices，注册进来
        for (DemandExcelService service : demandExcelServices) {
            demandExcelMap.put(service.getProductCategory(), service);
        }
    }

    public DownloadBean downloadTemplate(DownloadTemplateReq req) {
        DemandExcelService service = getService(req.getProductCategory());
        return service.downloadTemplate(req);
    }

    public ResponseEntity<InputStreamResource> exportDemand(DemandReportReq req) {
        DemandExcelService service = getService(req.getProductCategory());
        return service.exportDemand(req);
    }

    public ReturnT<List<ErrorMessage>> importAdjustDemand(MultipartFile file, ImportAdjustDemandReq req) {
        DemandExcelService service = getService(req.getProductCategory());
        return service.importAdjustDemand(file, req);
    }

    private DemandExcelService getService(String productCategory) {
        DemandExcelService service = demandExcelMap.get(productCategory);
        if (Objects.isNull(service)) {
            throw new BizException("未找到对应的productCategory:" + productCategory);
        }
        return service;
    }
}
