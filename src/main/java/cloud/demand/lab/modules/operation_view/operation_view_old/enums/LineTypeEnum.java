package cloud.demand.lab.modules.operation_view.operation_view_old.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * 线上库存和线下库存枚举
 */
@Getter
public enum LineTypeEnum {

    ONLINE("ONLINE", "线上库存"),

    OFFLINE("OFFLINE", "线下库存"),

    ;

    final private String code;
    final private String name;

    LineTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LineTypeEnum getByCode(String code) {
        for (LineTypeEnum e : LineTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        LineTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
