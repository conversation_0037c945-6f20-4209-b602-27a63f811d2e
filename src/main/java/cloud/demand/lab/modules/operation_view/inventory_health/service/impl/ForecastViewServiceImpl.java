package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.CommonConditionDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.CommonQueryConditionDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.DeviceApplyForForecastDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ForecastByHolidayWeekDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ForecastWeeklyDemandDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.InventoryHealthPplForecastDetailVO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.PurchaseFutureDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.SafetyInventoryDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.TurnoverInventoryDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.future.InventoryHealthActualInvResp;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthFutureForecastDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPplForecastDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthPurchaseFutureDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.ForecastViewService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.OutsideHealthService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.ActualInventoryListReq;
import cloud.demand.lab.modules.operation_view.operation_view.service.OperationViewService2;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.utils.OperationViewTools;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;

@Service
@Slf4j
public class ForecastViewServiceImpl implements ForecastViewService {

    @Resource
    DictService dictService;
    @Resource
    OperationViewService operationViewService;
    @Resource
    TaskLogService taskLogService;
    @Resource
    OperationViewService2 operationViewService2;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper shuttleDBHelper;
    @Resource
    OutsideHealthService outsideHealthService;


    /**
     * 预测准确率MAPE
     */
    private static final Supplier<BigDecimal> SAFETY_INVENTORY_MAPE =
            DynamicProperty.create("safetyInventory.MAPE", "0.5",
                    BigDecimal::new);

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "genForecastHolidayWeekData")
    public void genForecastHolidayWeekData() {
        Date yesterday = DateUtils.addTime(new Date(), Calendar.DATE, -1);
        // 1、若存在，则全量覆盖
        long count = demandDBHelper.getCount(InventoryHealthPplForecastDetailDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (count > 0) {
            demandDBHelper.delete(InventoryHealthPplForecastDetailDO.class,
                    "where stat_time = ?", DateUtils.formatDate(yesterday));
        }

        // 2、查询预测数据-中长尾预测数据也从item里拿
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/gen_forecast_data.sql");

        List<ForecastByHolidayWeekDTO> all = demandDBHelper.getRaw(ForecastByHolidayWeekDTO.class, sql);

        //  获取腾讯云全量地域属性
        List<TxyRegionInfoDTO> allTxyRegionInfo = dictService.getAllTxyRegionInfo();
        Map<String, TxyRegionInfoDTO> zoneMap = ListUtils.toMap(allTxyRegionInfo, o -> o.getZoneName(),
                Function.identity());
        Map<String, TxyRegionInfoDTO> regionMap = ListUtils.toMap(allTxyRegionInfo, o -> o.getRegionName(),
                Function.identity());

        ListUtils.forEach(all, o -> {
            //  补齐节假周属性
            ResPlanHolidayWeekDO yearWeekDO = dictService.getHolidayWeekInfoByDate(o.getBeginBuyDate());
            o.setHolidayYear(yearWeekDO != null ? yearWeekDO.getYear() : null);
            o.setHolidayWeek(yearWeekDO != null ? yearWeekDO.getWeek() : null);
            o.setHolidayMonth(yearWeekDO != null ? yearWeekDO.getMonth() : null);
            //  补齐地域属性
            TxyRegionInfoDTO zoneInfo = zoneMap.get(o.getZoneName());
            if (zoneInfo != null) {
                o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                o.setAreaName(zoneInfo.getAreaName());
            }
            // 如果靠可用区匹配不到，则用regionName匹配
            if (zoneInfo == null) {
                TxyRegionInfoDTO regionInfo = regionMap.get(o.getRegionName());
                if (regionInfo != null) {
                    o.setCustomhouseTitle(regionInfo.getCustomhouseTitle());
                    o.setAreaName(regionInfo.getAreaName());
                }
            }
        });

        //   再聚合 + Collect
        List<ForecastByHolidayWeekDTO> result = Lang.list();
        Map<String, List<ForecastByHolidayWeekDTO>> groupMap = ListUtils.groupBy(all,
                o -> ForecastByHolidayWeekDTO.getGroupK(o));
        for (Map.Entry<String, List<ForecastByHolidayWeekDTO>> entry : groupMap.entrySet()) {
            ForecastByHolidayWeekDTO anyone = ForecastByHolidayWeekDTO.copy(entry.getValue().get(0));
            //  退回取负，新增、弹性取正，这里的sum计算后为净增值
            anyone.setTotalCore(NumberUtils.sum(entry.getValue(), o -> o.getTotalCore()).intValue());
            //  新增
            anyone.setTotalCoreNew(NumberUtils.sum(
                    ListUtils.filter(entry.getValue(), o -> o.getTotalCore() > 0), o -> o.getTotalCore()).intValue());
            //  退回
            anyone.setTotalCoreReturn(NumberUtils.sum(
                    ListUtils.filter(entry.getValue(), o -> o.getTotalCore() < 0), o -> o.getTotalCore()).intValue());
            result.add(anyone);
        }

        //  4、插入DB
        List<InventoryHealthPplForecastDetailDO> transform = ListUtils.transform(result,
                o -> ForecastByHolidayWeekDTO.transform(o));
        ListUtils.forEach(transform, o -> o.setStatTime(DateUtils.toLocalDate(yesterday)));
        demandDBHelper.insertBatchWithoutReturnId(transform);

        //  5、事后检查，没数据告警
        long finishCount = demandDBHelper.getCount(InventoryHealthPplForecastDetailDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (finishCount == 0) {
            String msg = "库存健康-13周预测节假周数据未成功生成，请查看";
            taskLogService.genRunLog("genForecastHolidayWeekData", "genForecastHolidayWeekData", msg);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "genPurchaseFutureData")
    public void genPurchaseFutureData() {
        Date yesterday = DateUtils.addTime(new Date(), Calendar.DATE, -1);
        // 1、若存在，则全量覆盖
        long count = demandDBHelper.getCount(InventoryHealthPurchaseFutureDetailDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (count > 0) {
            demandDBHelper.delete(InventoryHealthPurchaseFutureDetailDO.class,
                    "where stat_time = ?", DateUtils.formatDate(yesterday));
        }

        //  2、获取统计范围内的全量数据
        WhereSQL condition = new WhereSQL();
        condition.and("(order_type in (1, 100) or (order_type=0 and status in (0,1,3,4,5,6)))");
        //  单据状态只要【部分到货】和【等待到货】
        condition.and("status IN (8000002, 8000005)");

        //  20230426:放开CVM的限制
        //  condition.and("product = '腾讯云CVM'");
        List<DeviceApplyForForecastDTO> source =
                shuttleDBHelper.getAll(DeviceApplyForForecastDTO.class, condition.getSQL(), condition.getParams());

        //  3、transform & insert
        List<InventoryHealthPurchaseFutureDetailDO> details = buildPurchaseFutureDetail(source);
        ListUtils.forEach(details, o -> o.setStatTime(DateUtils.toLocalDate(yesterday)));

        demandDBHelper.insertBatchWithoutReturnId(details);

        //  4、事后检查，没数据告警
        long finishCount = demandDBHelper.getCount(InventoryHealthPurchaseFutureDetailDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (finishCount == 0) {
            String msg = "库存健康-未来采购明细数据未成功生成，请查看";
            taskLogService.genRunLog("genPurchaseFutureData", "genPurchaseFutureData", msg);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "genFutureForecastData")
    public void genFutureForecastData() {

        //  1、清理已有数据
        Date yesterday = DateUtils.addTime(new Date(), Calendar.DATE, -1);
        long count = demandDBHelper.getCount(InventoryHealthFutureForecastDetailDO.class,
                "where stat_time = ?", DateUtils.formatDate(yesterday));
        if (count > 0) {
            demandDBHelper.delete(InventoryHealthFutureForecastDetailDO.class,
                    "where stat_time = ?", DateUtils.formatDate(yesterday));
        }

        //  2、获取时间范围
        //  获取0~13周的时间区间，框定数据筛选范围
        List<HolidayWeekDTO> futureHolidayWeeks = getFutureHolidayWeekInfo(13);

        //  添加当周
        HolidayWeekDTO holidayWeekDTO = new HolidayWeekDTO();
        ResPlanHolidayWeekDO dto = dictService.getHolidayWeekInfoByDate(DateUtils.formatDate(new Date()));
        holidayWeekDTO.setYear(dto.getYear());
        holidayWeekDTO.setWeek(dto.getWeek());
        holidayWeekDTO.setWeekNFromNow(0);
        holidayWeekDTO.setDate(dto.getStart());
        futureHolidayWeeks.add(holidayWeekDTO);
        List<Date> dates = ListUtils.transform(futureHolidayWeeks, o -> DateUtils.parse(o.getDate()));
        Map<String, HolidayWeekDTO> weekMap =
                ListUtils.toMap(futureHolidayWeeks, o -> o.getYear() + "-" + o.getWeek(), Function.identity());

        //  3、数据捞取-groupby公共字段，供下一步合并
        //  实际库存数据-当日作为W0的实际库存
        List<DwsActualInventoryDfDO> actualInventoryDTOS =
                queryActualInventory(yesterday, new CommonQueryConditionDTO(), null, null, null, null);
        Map<String, List<DwsActualInventoryDfDO>> acutalInventoryMap = ListUtils.groupBy(actualInventoryDTOS, o ->
                String.join("@", o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        //  中长尾需求预测数据
        List<ForecastWeeklyDemandDTO> demandForecastDTOS = queryForecastHolidayWeekData(dates,
                new CommonQueryConditionDTO());
        Map<String, List<ForecastWeeklyDemandDTO>> demandForecastMap = ListUtils.groupBy(demandForecastDTOS, o ->
                String.join("@", o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        //  未来采购数据
        List<PurchaseFutureDTO> purchaseFutureDTOS = queryPurchaseDataData(dates, new CommonQueryConditionDTO());
        Map<String, List<PurchaseFutureDTO>> purchaseFutureMap = ListUtils.groupBy(purchaseFutureDTOS, o ->
                String.join("@", o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        //  安全库存数据
        List<SafetyInventoryDTO> safetyInventoryDTOS = queryFutureSafetyInventory(dates, new CommonQueryConditionDTO());
        Map<String, List<SafetyInventoryDTO>> safetyInventoryMap = ListUtils.groupBy(safetyInventoryDTOS, o ->
                String.join("@", o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        //  周转库存数据
        List<TurnoverInventoryDTO> turnoverInventoryDTOS = queryTurnoverInventory(dates, new CommonQueryConditionDTO());
        Map<String, List<TurnoverInventoryDTO>> turnoverInventoryMap = ListUtils.groupBy(turnoverInventoryDTOS, o ->
                String.join("@", o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        // 4、数据整合,相同维度下的数据只有一条
        List<InventoryHealthFutureForecastDetailDO> result = Lang.list();
        Set<String> allKey = Lang.set();
        allKey.addAll(acutalInventoryMap.keySet());
        allKey.addAll(demandForecastMap.keySet());
        allKey.addAll(purchaseFutureMap.keySet());
        allKey.addAll(safetyInventoryMap.keySet());
        allKey.addAll(turnoverInventoryMap.keySet());

        for (String key : allKey) {
            for (Date date : dates) {
                //  获取
                ResPlanHolidayWeekDO holidayWeek = dictService.getHolidayWeekInfoByDate(DateUtils.formatDate(date));
                Integer curYear = holidayWeek.getYear();
                Integer curMonth = holidayWeek.getMonth();
                Integer curWeek = holidayWeek.getWeek();

                //  全部放一起
                List<CommonConditionDTO> dtos = Lang.list();
                List<DwsActualInventoryDfDO> actualInventory = ListUtils.filter(acutalInventoryMap.get(key),
                        o -> Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getStatTime()).getYear()) &&
                                Objects.equals(curWeek, dictService.getHolidayWeekInfoByDate(o.getStatTime()).getWeek()));

                List<SafetyInventoryDTO> safetyInventory = ListUtils.filter(safetyInventoryMap.get(key),
                        o -> Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getDate()).getYear()) &&
                                Objects.equals(curWeek, dictService.getHolidayWeekInfoByDate(o.getDate()).getWeek()));

                List<PurchaseFutureDTO> purchaseFuture = ListUtils.filter(purchaseFutureMap.get(key),
                        o -> Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getDate()).getYear()) &&
                                Objects.equals(curWeek, dictService.getHolidayWeekInfoByDate(o.getDate()).getWeek()));

                List<ForecastWeeklyDemandDTO> demandForecast = ListUtils.filter(demandForecastMap.get(key),
                        o -> Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getDate()).getYear()) &&
                                Objects.equals(curWeek, dictService.getHolidayWeekInfoByDate(o.getDate()).getWeek()));

                List<TurnoverInventoryDTO> turnoverInventory = ListUtils.filter(turnoverInventoryMap.get(key),
                        o -> Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getDate()).getYear()) &&
                                Objects.equals(curWeek, dictService.getHolidayWeekInfoByDate(o.getDate()).getWeek()));

                dtos.addAll(ListUtils.isNotEmpty(actualInventory) ? actualInventory : Lang.list());
                dtos.addAll(ListUtils.isNotEmpty(safetyInventory) ? safetyInventory : Lang.list());
                dtos.addAll(ListUtils.isNotEmpty(purchaseFuture) ? purchaseFuture : Lang.list());
                dtos.addAll(ListUtils.isNotEmpty(demandForecast) ? demandForecast : Lang.list());
                dtos.addAll(ListUtils.isNotEmpty(turnoverInventory) ? turnoverInventory : Lang.list());

                CommonConditionDTO anyone = new CommonConditionDTO();

                List<CommonConditionDTO> curDateDTO =
                        ListUtils.filter(dtos, o ->
                                Objects.equals(curYear, dictService.getHolidayWeekInfoByDate(o.getDate()).getYear()) &&
                                        Objects.equals(curWeek,
                                                dictService.getHolidayWeekInfoByDate(o.getDate()).getWeek()));

                if (ListUtils.isEmpty(curDateDTO)) {
                    String[] split = key.split("@");
                    if (split.length < 5) {
                        String msg = JSON.toJson(split);
                        taskLogService.genRunLog("genFutureForecastData", "genFutureForecastData",
                                "split key 数据有问题:" + msg);
                        continue;
                    }
                    anyone = new CommonConditionDTO();
                    anyone.setCustomhouseTitle(split[0]);
                    anyone.setAreaName(split[1]);
                    anyone.setRegionName(split[2]);
                    anyone.setZoneName(split[3]);
                    anyone.setInstanceType(split[4]);
                    anyone.setDate(DateUtils.formatDate(date));
                } else {
                    anyone = curDateDTO.get(0);
                }
                InventoryHealthFutureForecastDetailDO build = buildOne(anyone, yesterday);
                result.add(build);
                //  实际库存
                build.setActualInventory(NumberUtils.sum(actualInventory, o -> o.getActualInv()).intValue());

                //  安全库存
                build.setSafetyInventory(NumberUtils.sum(safetyInventory, o -> o.getSafetyInventoryCore()).intValue());

                //  未来采购
                build.setFuturePurchase(NumberUtils.sum(purchaseFuture, o -> o.getCoreNum()).intValue());

                //  需求预测-新增
                build.setDemandForecastNew(NumberUtils.sum(demandForecast, o -> o.getNewCores()).intValue());
                //  需求预测-退回
                build.setDemandForcastReturn(NumberUtils.sum(demandForecast, o -> o.getReturnCores()).intValue());
                //  需求预测-净增
                build.setDemandForecastTotal(NumberUtils.sum(demandForecast, o -> o.getCores()).intValue());
                //  周转库存
                build.setTurnoverInventory(NumberUtils.sum(turnoverInventory, o -> o.getCores()).intValue());

                //  节假周属性
                build.setHolidayYear(curYear);
                build.setHolidayMonth(curMonth);
                build.setHolidayWeek(curWeek);
                build.setWeekIndex(weekMap.get(curYear + "-" + curWeek).getWeekNFromNow());

                build.setStatTime(DateUtils.toLocalDate(yesterday));
            }
        }

        Map<String, List<InventoryHealthFutureForecastDetailDO>> resultMap = ListUtils.groupBy(result,
                o -> String.join("@",
                        o.getCustomhouseTitle(), o.getAreaName(), o.getRegionName(), o.getZoneName(),
                        o.getInstanceType()));
        for (Map.Entry<String, List<InventoryHealthFutureForecastDetailDO>> entry : resultMap.entrySet()) {
            List<InventoryHealthFutureForecastDetailDO> value = entry.getValue();
            ListUtils.sortAscNullFirst(value, o -> o.getWeekIndex());
            for (InventoryHealthFutureForecastDetailDO each : value) {
                Integer weekIndex = each.getWeekIndex();
                //
                if (weekIndex == null || weekIndex == 0) {
                    continue;
                }
                List<InventoryHealthFutureForecastDetailDO> lastWeekList = ListUtils.filter(value,
                        o -> Objects.equals(o.getWeekIndex(), weekIndex - 1));
                if (ListUtils.isNotEmpty(lastWeekList)) {
                    InventoryHealthFutureForecastDetailDO lastWeekDO = lastWeekList.get(0);
                    //  W(n)模拟库存 = W(n-1)实际库存 - W(n-1)新增需求预测 + W(n-1)退回需求预测 + W(n-1)未来采购
                    Integer simulateInventory = AmountUtils.add(AmountUtils.negate(lastWeekDO.getDemandForecastNew()),
                            AmountUtils.negate(lastWeekDO.getDemandForcastReturn()), lastWeekDO.getFuturePurchase());
                    //  除 n = 0 外，W(n)实际库存 = W(n)模拟库存，滚动
                    each.setActualInventory(simulateInventory);
                    each.setSimulateInventory(simulateInventory);
                }
            }
        }
        demandDBHelper.insertBatchWithoutReturnId(result);
    }


    private InventoryHealthFutureForecastDetailDO buildOne(CommonConditionDTO anyone, Date yesterday) {
        InventoryHealthFutureForecastDetailDO build = new InventoryHealthFutureForecastDetailDO();
        build.setStatTime(DateUtils.toLocalDate(yesterday));
        build.setCustomhouseTitle(
                StringTools.isBlank(anyone.getCustomhouseTitle()) ? "未分类" : anyone.getCustomhouseTitle());
        build.setAreaName(StringTools.isBlank(anyone.getAreaName()) ? "未分类" : anyone.getAreaName());
        build.setRegionName(StringTools.isBlank(anyone.getRegionName()) ? "未分类" : anyone.getRegionName());
        build.setZoneName(StringTools.isBlank(anyone.getZoneName()) ? "未分类" : anyone.getZoneName());
        if (StringTools.isBlank(anyone.getInstanceType()) || Objects.equals("null", anyone.getInstanceType()) ||
                Objects.equals("未知实例类型", anyone.getInstanceType())) {
            build.setInstanceType("未分类");
        } else {
            build.setInstanceType(anyone.getInstanceType());
        }

        return build;
    }

    /**
     * 转换并生成未来采购数据
     */
    private List<InventoryHealthPurchaseFutureDetailDO> buildPurchaseFutureDetail(
            List<DeviceApplyForForecastDTO> source) {
        Map<String, String> deviceType2InstanceTypeMap = dictService.getCsigDeviceTypeToInstanceTypeMap();
        Map<String, Integer> logicCpuCoreMap = dictService.getDeviceLogicCpuCore();

        List<InventoryHealthPurchaseFutureDetailDO> result = Lang.list();
        if (ListUtils.isEmpty(source)) {
            return result;
        }

        //  后置处理，填充属性
        ListUtils.forEach(source, o -> {
            //  若module不为空，则用module关联，否则用campus关联
            if (StringTools.isNotBlank(o.getModuleName())) {
                StaticZoneDO zoneInfo = dictService.getStaticZoneInfoByModule(o.getModuleName());
                if (zoneInfo != null) {
                    o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                    o.setAreaName(zoneInfo.getAreaName());
                    o.setRegionName(zoneInfo.getRegionName());
                    o.setZoneName(zoneInfo.getZoneName());
                }
            } else {
                if (StringTools.isNotBlank(o.getCampus())) {
                    StaticZoneDO zoneInfo = dictService.getStaticZoneInfoByCampus(o.getCampus());
                    if (zoneInfo != null) {
                        o.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                        o.setAreaName(zoneInfo.getAreaName());
                        o.setRegionName(zoneInfo.getRegionName());
                        o.setZoneName(zoneInfo.getZoneName());
                    }
                }
            }

            //  填充计算类型
            o.setComputeType(dictService.getComputeType(o.getDeviceType()));

            Boolean isZysy = outsideHealthService.isZysyModule(o.getProduct(), o.getBusiness1(),
                    o.getBusiness2(), o.getBusiness3());
            if (isZysy != null) {
                o.setProjType(isZysy ? "自研上云" : "非自研上云");
            }
            //  通过设备类型获取实例类型
            o.setInstanceType(deviceType2InstanceTypeMap.get(o.getDeviceType()));

            //  未到货量 = 星云子单数 - 已到货量
            o.setNoUsedNum(AmountUtils.subtract(o.getTotalNum(), o.getUsedNum()));
            //  未提货量 = 已到货量 - 已提货量
            o.setNoDeliverNum(AmountUtils.subtract(o.getUsedNum(), o.getDeliverNum()));
            //  未来采购量 = 未提货量 + 未到货量
            o.setFutureNum(AmountUtils.add(o.getNoDeliverNum(), o.getNoUsedNum()));

            //  未来采购量不为0，填充承诺交付信息
            Integer futureNum = o.getFutureNum();
            if (futureNum > 0) {
                Map<String, Integer> sortedMap = new LinkedHashMap<>();
                //  承诺交付日期,示例数据: {"2021-12-28": 2, "2021-12-22": 15, "2021-12-23": 127, "2021-12-30": 1}
                String omdPromiseInfo = o.getOmdPromiseInfo();
                if (StringTools.isNotBlank(omdPromiseInfo)) {
                    ObjectMapper mapper = new ObjectMapper();
                    HashMap<String, Object> readMap;
                    try {
                        readMap = mapper.readValue(omdPromiseInfo, HashMap.class);
                        List<String> keySet = ListUtils.toList(readMap.keySet());
                        //  承诺交付日期逆序排列，None排第一个，未来采购的量一次减去map的v至0
                        if (keySet.contains("None")) {
                            keySet = ListUtils.filter(keySet, p -> !Objects.equals("None", p));
                            sortedMap.put("None", NumberUtils.parseInt(readMap.get("None")));
                        }
                        if (ListUtils.isNotEmpty(keySet)) {
                            Collections.sort(keySet, Comparator.comparing(String::toString, Comparator.reverseOrder()));
                        }
                        for (String eachK : keySet) {
                            sortedMap.put(eachK, NumberUtils.parseInt(readMap.get(eachK)));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    Map<String, Integer> map = new HashMap<>();
                    if (sortedMap.size() > 0) {
                        for (Map.Entry<String, Integer> entry : sortedMap.entrySet()) {
                            map.put(entry.getKey(), entry.getValue() <= futureNum ? entry.getValue() : futureNum);
                            futureNum = AmountUtils.subtract(futureNum, entry.getValue());
                            if (futureNum <= 0) {
                                break;
                            }
                        }
                    }
                    o.setPromiseDeliveryInfo(map);
                }
            } else {
                o.setPromiseDeliveryInfo(new HashMap<>());
            }
        });

        //  剔除自研上云模块
        source = ListUtils.filter(source, o -> !Objects.equals(o.getProjType(), "自研上云"));

        //  生成数据
        for (DeviceApplyForForecastDTO dto : source) {
            for (Map.Entry<String, Integer> entry : dto.getPromiseDeliveryInfo().entrySet()) {
                InventoryHealthPurchaseFutureDetailDO one = new InventoryHealthPurchaseFutureDetailDO();
                one.setCustomhouseTitle(dto.getCustomhouseTitle());
                one.setAreaName(dto.getAreaName());
                one.setRegionName(dto.getRegionName());
                one.setZoneName(dto.getZoneName());
                one.setInstanceType(dto.getInstanceType());
                one.setDeviceType(dto.getDeviceType());
                one.setSubId(dto.getSubId());
                one.setIndustryDept(dto.getIndustryDept());
                one.setPromiseDeliveryDate(entry.getKey());
                one.setComputeType(dto.getComputeType());
                one.setPlanProductName(dto.getPlanProductName());
                if (!Objects.equals(one.getPromiseDeliveryDate(), "None")) {
                    ResPlanHolidayWeekDO holidayInfo = dictService.getHolidayWeekInfoByDate(entry.getKey());
                    if (holidayInfo != null) {
                        one.setPromiseDeliveryHolidayYear(holidayInfo.getYear());
                        one.setPromiseDeliveryHolidayMonth(holidayInfo.getMonth());
                        one.setPromiseDeliveryHolidayWeek(holidayInfo.getWeek());
                    }
                }
                one.setNum(entry.getValue());
                one.setCoreNum(AmountUtils.multiply(logicCpuCoreMap.get(one.getDeviceType()), one.getNum()));
                result.add(one);
            }
        }
        return result;
    }

    @Override
    public List<DwsActualInventoryDfDO> queryActualInventory(Date date, CommonQueryConditionDTO condition,
            List<String> lineType, List<String> materialType,
            List<String> zoneCategory, List<String> instanceTypeCategory) {
        OperationViewService2Impl operationViewService2Impl = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL commonCondition = condition.genCondition();
        ActualInventoryListReq params = new ActualInventoryListReq();
        params.setStatTime(DateUtils.formatDate(date));
        params.setLineType(lineType);
        params.setMaterialType(materialType);
        params.setZoneCategory(zoneCategory);
        params.setInstanceTypeCategory(instanceTypeCategory);
        List<DwsActualInventoryDfDO> actualInventoryDfDOS = operationViewService2Impl.getActualInventoryList(params, commonCondition);
        return actualInventoryDfDOS;
    }

    @Override
    public List<SafetyInventoryDTO> querySafetyInventory(Date date, CommonQueryConditionDTO condition,
            List<String> zoneCategory, List<String> instanceTypeCategory) {
        return null;
    }


    @Override
    public List<SafetyInventoryDTO> queryFutureSafetyInventory(List<Date> dates, CommonQueryConditionDTO condition) {
        // 1. 找到对应的节假周map
        List<String> dateStrs = ListUtils.transform(dates, DateUtils::formatDate);
        Map<String, ResPlanHolidayWeekDO> weekMap = dictService.getHolidayWeekInfoByDates(dateStrs);
        Map<String, ResPlanHolidayWeekDO> yearWeekToDOMap = ListUtils.toMap(
                weekMap.values(), o -> o.getYear() + "-" + o.getWeek(), o -> o);
        Map<String, String> yearWeekToDateStr = ListUtils.toMap(weekMap.entrySet(),
                o -> o.getValue().getYear() + "-" + o.getValue().getWeek(), o -> o.getKey());

        // 2. 批量找到这些周的预测数据，并计算出安全库存
        WhereSQL whereSQL = new WhereSQL();
        List<Object[]> yearWeekParam = new ArrayList();
        for (ResPlanHolidayWeekDO week : weekMap.values()) {
            yearWeekParam.add(new Object[]{week.getYear(), week.getWeek()});
        }
        whereSQL.and("stat_time = ?", DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        whereSQL.and("(holiday_year, holiday_week) in (?)", yearWeekParam);
        whereSQL.and("source = 'FORECAST'");
        if (condition != null) {
            whereSQL.and(condition.genCondition());
        }
        whereSQL.addGroupBy("holiday_year", "holiday_week", "region_name", "zone_name", "instance_type");

        List<InventoryHealthPplForecastDetailVO> all = demandDBHelper.getAll(InventoryHealthPplForecastDetailVO.class,
                whereSQL.getSQL(), whereSQL.getParams());

        calInventoryHealthPplForecastDetailVO(all, DateUtils.formatDate(DateUtils.today())); // 计算安全库存
        ListUtils.forEach(all, o -> {
            ResPlanHolidayWeekDO d = yearWeekToDOMap.get(o.getHolidayYear() + "-" + o.getHolidayWeek());
            if (d != null) {
                o.setForecastDemandStartDate(d.getStart());
                o.setForecastDemandEndDate(d.getEnd());
            }
        });

        // 3. 组装数据
        return ListUtils.transform(all, o -> {
            SafetyInventoryDTO dto = new SafetyInventoryDTO();
            String dateStr = yearWeekToDateStr.get(o.getHolidayYear() + "-" + o.getHolidayWeek());
            dto.setDate(DateUtils.formatDate(DateUtils.parse(dateStr)));

            dto.setInstanceType(o.getInstanceType());

            dto.setZoneName(o.getZoneName());
            dto.setRegionName(o.getRegionName());
            dto.setAreaName(o.getAreaName());
            dto.setCustomhouseTitle(o.getCustomhouseTitle());

            dto.setSafetyInventoryCore(o.getSafetyInventoryCore());
            dto.setPrePaidSafetyInventoryCore(o.getSafetyInventoryCore());
            dto.setForecastDemandCore(o.getPrePaidDemand());

            dto.setServiceLevel(o.getServiceLevel());
            dto.setServiceLevelFactor(o.getServiceLevelFactor());
            dto.setForecastDemandAccuracyRate(o.getForecastDemandAccuracyRate());
            dto.setForecastDemandStartDate(o.getForecastDemandStartDate());
            dto.setForecastDemandEndDate(o.getForecastDemandEndDate());
            dto.setSafetyInventoryCoreExpression(o.getExpression());
            dto.setSla(o.getSla());

            return dto;
        });
    }

    /**
     * 计算安全库存相关的信息
     */
    private void calInventoryHealthPplForecastDetailVO(List<InventoryHealthPplForecastDetailVO> all, String date) {
//        double serviceNum = OperationViewTools.normsinv(SAFETY_INVENTORY_SERVICE_LEVEL.get().doubleValue());
        for (InventoryHealthPplForecastDetailVO vo : all) {
            vo.setSla(operationViewService.getSLAByInstanceType(vo.getInstanceType(),
                    !"境外".equals(vo.getCustomhouseTitle()))); // custome_house_title为null算为境内

            int sla = vo.getSla(); // 服务水平
            double forecast = vo.getSumTotalCore();

            // 预测量
            if (forecast < 0) { // 预测增量小于0，按0处理
                forecast = 0;
                vo.setSumTotalOuterCore(0);
                vo.setSumTotalInnerCore(0);
            }

            OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
            BigDecimal serviceLevel = operationViewService2.getTargetServiceLevel(date, vo.getZoneName(), vo.getInstanceType(), "包年包月");
            double serviceNum = OperationViewTools.normsinv(serviceLevel.doubleValue());
            // 安全库存
            double safetyInventory =
                    serviceNum * forecast * (1 - SAFETY_INVENTORY_MAPE.get().doubleValue()) * Math.sqrt(sla / 7.0);
            vo.setSafetyInventoryCore((int) Math.round(safetyInventory));
            vo.setSla(sla);
            vo.setPrePaidDemand((int) Math.round(forecast));

            vo.setServiceLevel(serviceLevel);
            vo.setServiceLevelFactor(BigDecimal.valueOf(serviceNum));
            vo.setForecastDemandAccuracyRate(BigDecimal.valueOf(SAFETY_INVENTORY_MAPE.get().doubleValue()));

            vo.setExpression("z(" + serviceNum + ") * "
                    + forecast + " * (1-" + SAFETY_INVENTORY_MAPE.get() + ") * sqrt(" + sla + "/7)");
        }
    }

    /**
     * 园区类型、机型类型筛选
     */
    public List<BufferAverageCoreDTO> filterByCategory(List<BufferAverageCoreDTO> dtos, List<String> zoneCategory,
            List<String> instanceTypeCategory, String date){

        InventoryHealthConfigService inventoryHealthConfigService = SpringUtil.getBean(InventoryHealthConfigServiceImpl.class);
        Map<String, List<String>> zoneTypeMap = inventoryHealthConfigService.getZoneConfigMap(date);
        Set<String> allZoneNames = new HashSet<>();

        if (ListUtils.isNotEmpty(zoneCategory)) {
            allZoneNames = zoneCategory.stream().flatMap(type -> zoneTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(
                    Collectors.toSet());
        }

        Map<String, List<String>> instanceTypeMap = inventoryHealthConfigService.getInstanceTypeConfigMap(date);
        Set<String> allInstanceTypes = new HashSet<>();

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            allInstanceTypes = instanceTypeCategory.stream().flatMap(type -> instanceTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(Collectors.toSet());
        }

        Set<String> finalAllZoneNames = allZoneNames;
        Set<String> finalAllInstanceTypes = allInstanceTypes;
        return ListUtils.filter(dtos, o -> {
            boolean zoneCategoryFlag = false;
            boolean instanceTypeCategoryFlag = false;
            if (ListUtils.isNotEmpty(zoneCategory)){
                if (finalAllZoneNames.contains(o.getZoneName())) {
                    zoneCategoryFlag = true;
                }
            } else {
                zoneCategoryFlag = true;
            }

            if (ListUtils.isNotEmpty(instanceTypeCategory)){
                if (finalAllInstanceTypes.contains(o.getInstanceType())) {
                    instanceTypeCategoryFlag = true;
                }
            } else {
                instanceTypeCategoryFlag = true;
            }

            return zoneCategoryFlag && instanceTypeCategoryFlag;
        });
    }

    @Override
    @Deprecated
    public InventoryHealthActualResp queryInventoryHealthActual(InventoryHealthActualReq req) {
        // 老接口已经废弃，返回空对象
        return new InventoryHealthActualResp();
    }

    /**
     * 组合机型的逻辑
     */
    public List<InventoryHealthActualResp.Item> combineItem(List<Set<String>> combineList,
            List<InventoryHealthActualResp.Item> source, List<String> instanceTypeCategory, String date) {
        List<InventoryHealthActualResp.Item> result = Lang.list();
        if (ListUtils.isEmpty(combineList)) {
            return source;
        }
        if (ListUtils.isEmpty(source)) {
            return result;
        }
        //  1、获取组合机型数据
        //  获取全部配置在组合机型中的机型列表
        Set<String> instanceTypes = Lang.set();
        combineList.forEach(instanceTypes::addAll);

        //  获取非组合机型
        List<InventoryHealthActualResp.Item> notCombine = ListUtils.filter(source,
                o -> !instanceTypes.contains(o.getInstanceType()));
        //  获取组合机型
        List<InventoryHealthActualResp.Item> combine = ListUtils.subtract(source, notCombine);

        //  2、组合过程
        for (Set<String> each : combineList) {
            //  过滤出当前组合机型,合并同一可用区下的数据
            List<InventoryHealthActualResp.Item> details = ListUtils.filter(combine,
                    o -> each.contains(o.getInstanceType()));
            Map<String, List<InventoryHealthActualResp.Item>> zoneMap = ListUtils.groupBy(details,
                    o -> o.getZoneName());
            Set<Map.Entry<String, List<InventoryHealthActualResp.Item>>> entries = zoneMap.entrySet();
            for (Map.Entry<String, List<InventoryHealthActualResp.Item>> entry : entries) {
                List<InventoryHealthActualResp.Item> value = entry.getValue();
                InventoryHealthActualResp.Item item = new InventoryHealthActualResp.Item();
                item.setInstanceType(Strings.join(each, '/'));
                item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
                item.setAreaName(value.get(0).getAreaName());
                item.setRegionName(value.get(0).getRegionName());
                item.setZoneName(value.get(0).getZoneName());
                item.setActualInventoryCore(NumberUtils.sum(value, o -> o.getActualInventoryCore()).intValue());
                item.setSafetyInventoryCore(NumberUtils.sum(value, o -> o.getSafetyInventoryCore()).intValue());
                item.setPrePaidSafetyInventoryCore(
                        NumberUtils.sum(value, o -> o.getPrePaidSafetyInventoryCore()).intValue());
                item.setBufferSafetyInventoryCore(
                        NumberUtils.sum(value, o -> o.getBufferSafetyInventoryCore()));
                item.setHealthRatio(
                        AmountUtils.divideScale6(item.getActualInventoryCore(), item.getSafetyInventoryCore()));
                item.setForecastDemandCore(NumberUtils.sum(value, o -> o.getForecastDemandCore()).intValue());
                item.setForecastDemandCoreOuter(NumberUtils.sum(value, o -> o.getForecastDemandCoreOuter()).intValue());
                item.setForecastDemandCoreInner(NumberUtils.sum(value, o -> o.getForecastDemandCoreInner()).intValue());
                item.setForecastDemandStartDate(value.get(0).getForecastDemandStartDate());
                item.setForecastDemandEndDate(value.get(0).getForecastDemandEndDate());
                item.setBufferAverageCore(NumberUtils.sum(value, o -> o.getBufferAverageCore()).intValue());
                item.setBufferAverageStartDate(value.get(0).getBufferAverageStartDate());
                item.setBufferAverageEndDate(value.get(0).getBufferAverageEndDate());
                item.setDetails(value);
                item.setReservedCores(NumberUtils.sum(value, o -> o.getReservedCores()).intValue());
                result.add(item);
            }
        }
        result = filterCombineData(result, instanceTypeCategory, date);
        //  添加所有非组合机型的数据
        result.addAll(notCombine);
        return result;
    }

    /**
     * 出现在组合机型表中的机型，按照主力-在售非主力-其他的优先级进行顺位匹配
     * 如S5（主力机型）/M5（在售非主力机型），在选中组合机型的选项后，只会在主力机型的筛选下出现
     */
    private List<InventoryHealthActualResp.Item> filterCombineData(List<InventoryHealthActualResp.Item> source,
            List<String> instanceTypeCategory, String date){
        InventoryHealthConfigService inventoryHealthConfigService = SpringUtil.getBean(InventoryHealthConfigServiceImpl.class);

        Map<String, List<String>> instanceTypeMap = inventoryHealthConfigService.getInstanceTypeConfigMap(date);
        Set<String> allInstanceTypes = new HashSet<>();

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            allInstanceTypes = instanceTypeCategory.stream().flatMap(type -> instanceTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(Collectors.toSet());
        }

        Set<String> finalAllInstanceTypes = allInstanceTypes;
        List<InventoryHealthActualResp.Item> filter = ListUtils.filter(source, o -> {
            if (o.getInstanceType().contains("/") && ListUtils.isNotEmpty(instanceTypeCategory)) { // 过滤出组合机型
                String[] split = o.getInstanceType().split("/");
                // TODO 这里不满足 "如S5（主力机型）/M5（在售非主力机型），在选中组合机型的选项后，只会在主力机型的筛选下出现" 新逻辑跟原来有冲突
                return Arrays.stream(split).anyMatch(finalAllInstanceTypes::contains);
            }
            return true;
        });
        return filter;
    }

    /**
     * 对单机型进行筛选
     */
    private List<InventoryHealthActualResp.Item> filterSingleData(List<InventoryHealthActualResp.Item> source,
            List<String> instanceTypeCategory, String date){
        InventoryHealthConfigService inventoryHealthConfigService = SpringUtil.getBean(InventoryHealthConfigServiceImpl.class);
        Map<String, List<String>> instanceTypeMap = inventoryHealthConfigService.getInstanceTypeConfigMap(date);
        Set<String> allInstanceTypes = new HashSet<>();

        if (ListUtils.isNotEmpty(instanceTypeCategory)) {
            allInstanceTypes = instanceTypeCategory.stream().flatMap(type -> instanceTypeMap.getOrDefault(type, ListUtils.newList()).stream()).collect(Collectors.toSet());
        }

        Set<String> finalAllInstanceTypes = allInstanceTypes;
        List<InventoryHealthActualResp.Item> filter = ListUtils.filter(source, o -> {
            if (!o.getInstanceType().contains("/")  && ListUtils.isNotEmpty(instanceTypeCategory)) { // 过滤出非组合机型
                return finalAllInstanceTypes.contains(o.getInstanceType());
            }
            return true;
        });
        return filter;
    }

    @Override
    public List<PurchaseFutureDTO> queryPurchaseDataData(List<Date> dates, CommonQueryConditionDTO dto) {
        //  1、查询DB
        WhereSQL condition = dto.genCondition();
        Map<String, ResPlanHolidayWeekDO> weekMap =
                dictService.getHolidayWeekInfoByDates(ListUtils.transform(dates, DateUtils::formatDate));
        Map<String, String> weekStartMap =
                ListUtils.toMap(weekMap.values(), o -> o.getYear() + "-" + o.getWeek(), o -> o.getStart());

        List<Object[]> yearWeekParam = Lang.list();
        for (ResPlanHolidayWeekDO week : weekMap.values()) {
            yearWeekParam.add(new Object[]{week.getYear(), week.getWeek()});
        }
        WhereSQL timeCond = new WhereSQL();
        timeCond.or("(promise_delivery_holiday_year, promise_delivery_holiday_week) in (?)", yearWeekParam);
        timeCond.or("promise_delivery_date = 'None'");
        condition.and(timeCond);
        condition.and("stat_time = ?", DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        condition.and("plan_product_name = '腾讯云CVM' and compute_type = 'CPU'");

        List<InventoryHealthPurchaseFutureDetailDO> all =
                demandDBHelper.getAll(InventoryHealthPurchaseFutureDetailDO.class, condition.getSQL(),
                        condition.getParams());

        Collections.sort(dates);

        //  2、聚合+生成返回对象list
        Map<String, List<InventoryHealthPurchaseFutureDetailDO>> map =
                ListUtils.groupBy(all, o -> String.join("@",
                        o.getPromiseDeliveryHolidayYear() == null ? "None"
                                : o.getPromiseDeliveryHolidayYear().toString(),
                        o.getPromiseDeliveryHolidayWeek() == null ? "None"
                                : o.getPromiseDeliveryHolidayWeek().toString(),
                        o.getCustomhouseTitle(), o.getAreaName(), o.getRegionName(), o.getZoneName(),
                        o.getInstanceType()));

        List<PurchaseFutureDTO> result = Lang.list();
        for (Map.Entry<String, List<InventoryHealthPurchaseFutureDetailDO>> entry : map.entrySet()) {
            List<InventoryHealthPurchaseFutureDetailDO> value = entry.getValue();
            InventoryHealthPurchaseFutureDetailDO anyone = value.get(0);
            PurchaseFutureDTO build = new PurchaseFutureDTO();
            if (Objects.equals(anyone.getPromiseDeliveryDate(), "None")) {
                build.setDate(DateUtils.formatDate(dates.get(dates.size() - 1)));
            } else {
                build.setDate(weekStartMap.get(
                        anyone.getPromiseDeliveryHolidayYear() + "-" + anyone.getPromiseDeliveryHolidayWeek()));
            }
            //  取当周第一天作为date
            build.setCustomhouseTitle(anyone.getCustomhouseTitle());
            build.setAreaName(anyone.getAreaName());
            build.setRegionName(anyone.getRegionName());
            build.setZoneName(anyone.getZoneName());
            build.setInstanceType(anyone.getInstanceType());
            build.setCoreNum(NumberUtils.sum(value, o -> o.getCoreNum()).intValue());
            result.add(build);
        }

        return result;
    }

    @Override
    public List<ForecastWeeklyDemandDTO> queryForecastHolidayWeekData(List<Date> dates, CommonQueryConditionDTO dto) {
        //  1、查询DB
        WhereSQL condition = dto.genCondition();
        Map<String, ResPlanHolidayWeekDO> weekMap =
                dictService.getHolidayWeekInfoByDates(ListUtils.transform(dates, DateUtils::formatDate));
        Map<String, String> weekStartMap = ListUtils.toMap(weekMap.values(), o -> o.getYear() + "-" + o.getWeek(),
                o -> o.getStart());

        List<Object[]> yearWeekParam = Lang.list();
        for (ResPlanHolidayWeekDO week : weekMap.values()) {
            yearWeekParam.add(new Object[]{week.getYear(), week.getWeek()});
        }
        condition.and("stat_time = ?", DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        condition.and("(holiday_year, holiday_week) in (?)", yearWeekParam);
        List<InventoryHealthPplForecastDetailDO> all =
                demandDBHelper.getAll(InventoryHealthPplForecastDetailDO.class, condition.getSQL(),
                        condition.getParams());

        //  2、聚合+生成返回对象list
        Map<String, List<InventoryHealthPplForecastDetailDO>> map =
                ListUtils.groupBy(all, o -> String.join("@",
                        o.getHolidayYear().toString(), o.getHolidayWeek().toString(), o.getCustomhouseTitle(),
                        o.getAreaName(), o.getRegionName(), o.getZoneName(), o.getInstanceType()));

        List<ForecastWeeklyDemandDTO> result = Lang.list();
        for (Map.Entry<String, List<InventoryHealthPplForecastDetailDO>> entry : map.entrySet()) {
            List<InventoryHealthPplForecastDetailDO> value = entry.getValue();
            InventoryHealthPplForecastDetailDO anyone = value.get(0);
            ForecastWeeklyDemandDTO build = new ForecastWeeklyDemandDTO();
            //  取当周第一天作为date
            build.setDate(weekStartMap.get(anyone.getHolidayYear() + "-" + anyone.getHolidayWeek()));
            build.setCustomhouseTitle(anyone.getCustomhouseTitle());
            build.setAreaName(anyone.getAreaName());
            build.setRegionName(anyone.getRegionName());
            build.setZoneName(anyone.getZoneName());
            build.setInstanceType(anyone.getInstanceType());
            //  新增、退回、净增一起生成
            build.setCores(NumberUtils.sum(value, o -> o.getTotalCore()).intValue());
            build.setNewCores(NumberUtils.sum(value, o -> o.getTotalCoreNew()).intValue());
            build.setReturnCores(NumberUtils.sum(value, o -> o.getTotalCoreReturn()).intValue());
            result.add(build);
        }

        return result;
    }

    @Override
    public List<TurnoverInventoryDTO> queryTurnoverInventory(List<Date> dates, CommonQueryConditionDTO conditionDTO) {
        List<TurnoverInventoryDTO> result = Lang.list();
        // 1. 找到对应的节假周map
        List<String> dateStrs = ListUtils.transform(dates, DateUtils::formatDate);
        Map<String, ResPlanHolidayWeekDO> weekMap = dictService.getHolidayWeekInfoByDates(dateStrs);
        Map<String, ResPlanHolidayWeekDO> yearWeekToDOMap = ListUtils.toMap(
                weekMap.values(), o -> o.getYear() + "-" + o.getWeek(), o -> o);
        Map<String, String> yearWeekToDateStr = ListUtils.toMap(weekMap.entrySet(),
                o -> o.getValue().getYear() + "-" + o.getValue().getWeek(), o -> o.getKey());

        // 2. 批量找到这些周的预测数据，并计算出安全库存
        WhereSQL condition = conditionDTO.genCondition();
        List<Object[]> yearWeekParam = new ArrayList();
        for (ResPlanHolidayWeekDO week : weekMap.values()) {
            yearWeekParam.add(new Object[]{week.getYear(), week.getWeek()});
        }
        condition.and("stat_time = ?", DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        condition.and("(holiday_year, holiday_week) in (?)", yearWeekParam);
        condition.and("source = 'FORECAST'");
        condition.addGroupBy("holiday_year", "holiday_week", "region_name", "zone_name", "instance_type");

        List<InventoryHealthPplForecastDetailVO> all = demandDBHelper.getAll(InventoryHealthPplForecastDetailVO.class,
                condition.getSQL(), condition.getParams());

        if (ListUtils.isEmpty(all)) {
            return result;
        }

        for (InventoryHealthPplForecastDetailVO vo : all) {
            vo.setSla(operationViewService.getSLAByInstanceType(vo.getInstanceType(),
                    !"境外".equals(vo.getCustomhouseTitle()))); // custome_house_title为null算为境内

            int sla = vo.getSla(); // 服务水平
            double forecast = vo.getSumTotalCore(); // 预测量
            if (forecast < 0) {
                forecast = 0; // 预测增量小于0，按0处理
            }

            TurnoverInventoryDTO dto = new TurnoverInventoryDTO();
            dto.setDate(yearWeekToDateStr.get(vo.getHolidayYear() + "-" + vo.getHolidayWeek()));
            dto.setCustomhouseTitle(vo.getCustomhouseTitle());
            dto.setAreaName(vo.getAreaName());
            dto.setRegionName(vo.getRegionName());
            dto.setZoneName(vo.getZoneName());
            dto.setInstanceType(vo.getInstanceType());
            dto.setCores(AmountUtils.multiply(new BigDecimal(sla / 7.0), new BigDecimal(forecast)).intValue());
            result.add(dto);
        }
        return result;
    }

    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Override
    public InventoryHealthActualInvResp queryActualInventory2(CommonQueryConditionDTO conditionDTO,
            List<String> lineType, List<String> matrialType,
            List<String> zoneCategory, List<String> instanceTypeCategory) {
        Date yesterday = DateUtils.addTime(new Date(), Calendar.DATE, -1);

        List<DwsActualInventoryDfDO> dtos =
                queryActualInventory(yesterday, conditionDTO, lineType, matrialType, zoneCategory, instanceTypeCategory);
        List<InventoryHealthActualInvResp.ActualInvDTO> result = Lang.list();
        // 按照下面的维度进行分组
        Map<String, List<DwsActualInventoryDfDO>> actualInvMap =
                ListUtils.groupBy(dtos, o -> String.join("@", o.getZoneName(), o.getInstanceType()));
        //  遍历分组后的数据
        actualInvMap.entrySet().forEach(entry -> {
            InventoryHealthActualInvResp.ActualInvDTO one = new InventoryHealthActualInvResp.ActualInvDTO();
            DwsActualInventoryDfDO dto = entry.getValue().get(0);
            one.setCustomhouseTitle(dto.getCustomhouseTitle());
            one.setAreaName(dto.getAreaName());
            one.setRegionName(dto.getRegionName());
            one.setZoneName(dto.getZoneName());
            one.setInstanceType(dto.getInstanceType());
            one.setCoreNum(NumberUtils.sum(entry.getValue(), o -> o.getActualInv()).intValue());
            result.add(one);
        });

        InventoryHealthActualInvResp rsp = new InventoryHealthActualInvResp();
        rsp.setData(result);
        return rsp;
    }


    /**
     * 获取未来13周的节假周信息
     *
     * @return HolidayWeekDTO
     *         date:未来第n周-当周周一
     *         year:节假周-年信息
     *         week:节假周-周信息
     */
    public List<HolidayWeekDTO> getFutureHolidayWeekInfo(int n) {
        LocalDate today = LocalDate.now();
        //  获取当周周一
        Date curWeekMonday = DateUtils.parse(DateUtils.formatDate(today.with(DayOfWeek.MONDAY)));
        List<Tuple2<String, Integer>> mondayList = Lang.list();
        for (int i = 1; i <= n; i++) {
            curWeekMonday = DateUtils.addTime(curWeekMonday, Calendar.DATE, 7);
            mondayList.add(Tuple.of(DateUtils.formatDate(curWeekMonday), i));
        }
        List<HolidayWeekDTO> dtos = Lang.list();

        ListUtils.forEach(mondayList, o -> {
            HolidayWeekDTO holidayWeekDTO = new HolidayWeekDTO();
            holidayWeekDTO.setDate(o._1);
            holidayWeekDTO.setWeekNFromNow(o._2);
            ResPlanHolidayWeekDO holidayWeekInfo = dictService.getHolidayWeekInfoByDate(o._1);
            holidayWeekDTO.setYear(holidayWeekInfo.getYear());
            holidayWeekDTO.setWeek(holidayWeekInfo.getWeek());
            dtos.add(holidayWeekDTO);
        });
        return dtos;
    }

    public Map<String, List<DwsCloudServerLevelDO>> getSla(String date) {
        String version = DateUtils.format(DateUtils.parse(date), "yyyyMMdd");
        return ckcldDBHelper.getAll(DwsCloudServerLevelDO.class,
                        "where version =? ", version).stream()
                .collect(Collectors.groupingBy(item -> item.getZoneName() + "@" + item.getInstanceFamily()));
    }

}

