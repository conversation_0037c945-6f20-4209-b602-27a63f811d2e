package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class SimpleDemandReportReq {

    @NotBlank(message = "切片时间不能为空")
    private String statTime;

    @Pattern(regexp = "^[0-9]{4}-[0-9]{2}$",message = "年月格式不正确")
    private String startYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

    @Pattern(regexp = "^[0-9]{4}-[0-9]{2}$",message = "年月格式不正确")
    private String endYearMonth = LocalDate.now().plusMonths(3).format(DateTimeFormatter.ofPattern("yyyy-MM"));

    private String orderRange;

    private boolean excludeFinishDemand = true;

    private List<String> dims;

}
