package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryBasicReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleOutData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleOutReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleTodayResp;
import java.util.List;

public interface InventoryDisassembleService {

    InventoryDisassembleResp queryInventoryDisassembleReport(InventoryDisassembleReq req);

    InventoryDisassembleTodayResp queryInventoryDisassembleToday(InventoryBasicReq req);

    DownloadBean exportInventoryDisassembleTotal(InventoryDisassembleReq req);

    List<InventoryDisassembleOutData> queryInventoryDisassembleOut(InventoryDisassembleOutReq req);



}
