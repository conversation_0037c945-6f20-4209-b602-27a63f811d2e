package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplyDemandParamTypeReq {

    private String statTime;

    @NotBlank(message = "paramType不能为空")
    private String paramType;

    private String productCategory;

    public SupplyDemandParamTypeReq(String paramType) {
        this.paramType = paramType;
    }
}
