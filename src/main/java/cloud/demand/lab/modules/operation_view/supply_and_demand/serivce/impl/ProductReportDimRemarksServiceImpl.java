package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductReportDimRemarksDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductReportDimRemarksVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ProductReportDimRemarksService;
import cloud.demand.lab.modules.operation_view.util.DimJoinUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/16 14:40
 */
@Service
public class ProductReportDimRemarksServiceImpl implements ProductReportDimRemarksService {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<ProductReportDimRemarksVO> queryRemarks(QueryRemarksReq req) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" product_category in (?) ",req.getProductCategory());
        whereSQL.and(" version in (?) ",req.getVersion());
        whereSQL.and(" data_type in (?) ",req.getDataType());

        if(!ListUtils.isEmpty(req.getDimValue())){
            if(req.getDimKey().size() != req.getDimValue().size()){
                throw new BizException("维度key和value数量不一致");
            }
            // 按key排序后分割且前后都要有@
            Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimValue());
            whereSQL.andIf(!dim.isEmpty()," dim_key = ? ", DimJoinUtil.join(dim.keySet()));
            whereSQL.andIf(!dim.isEmpty()," dim_value = ? ", DimJoinUtil.join(dim.values()));
        }else {
            Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimKey());
            whereSQL.andIf(!dim.isEmpty()," dim_key = ? ", DimJoinUtil.join(dim.keySet()));
        }

        whereSQL.addOrderBy("update_time");
        List<ProductReportDimRemarksDO> dataList = demandDBHelper.getAll(ProductReportDimRemarksDO.class,whereSQL.getSQL(),whereSQL.getParams());
        return ListUtils.transform(dataList, ProductReportDimRemarksVO::transform);
    }

    @Override
    public int createRemarks(CreateRemarksReq req) {
        ProductReportDimRemarksDO data = new ProductReportDimRemarksDO();
        data.setProductCategory(req.getProductCategory());
        data.setVersion(req.getVersion());
        data.setDataType(req.getDataType());


        if(!ListUtils.isEmpty(req.getDimValue())){
            if(req.getDimKey().size() != req.getDimValue().size()){
                throw new BizException("维度key和value数量不一致");
            }
            // 按key排序后分割且前后都要有@
            Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimValue());
            data.setDimKey(DimJoinUtil.join(dim.keySet()));
            data.setDimValue(DimJoinUtil.join(dim.values()));
        }else {
            Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimKey());
            data.setDimKey(DimJoinUtil.join(dim.keySet()));
        }

        data.setRemarks(req.getRemarks());
        return demandDBHelper.insert(data);
    }

    @Override
    public int updateRemarks(UpdateRemarksReq req) {
        ProductReportDimRemarksDO one = demandDBHelper.getOne(ProductReportDimRemarksDO.class, " where id = ? ", req.getId());
        if (Objects.isNull(one)) {
            throw new BizException("ID不存在，id：" + req.getId());
        }
        one.setRemarks(req.getRemarks());
        return demandDBHelper.update(one);
    }

    @Override
    public int deleteRemarks(List<Long> ids) {

        if (!CollectionUtils.isEmpty(ids)) {
           return demandDBHelper.delete(ProductReportDimRemarksDO.class, "where id in (?)", ids);
        }
        return 0;
    }
}
