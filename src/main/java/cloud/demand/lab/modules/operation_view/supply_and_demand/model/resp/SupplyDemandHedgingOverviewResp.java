package cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:39
 */
@Data
public class SupplyDemandHedgingOverviewResp {
    private String statTime;

    private String yearMonth;

    private String productCategory;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private String volumeType;

    private BigDecimal beginInventory;

    private Integer safetyInventoryCore;

    private BigDecimal safetyInventory;

    private BigDecimal currentMonthScale;

    private Integer withholdInventoryCore;

    private BigDecimal withholdInventory;

    private BigDecimal targetInventory;

    private BigDecimal targetThreshold;

    private List<Item> itemList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private String yearMonth;

        private BigDecimal demandCore;

        private BigDecimal demandAmount;

        private BigDecimal supplyCore;

        private BigDecimal supplyAmount;

        private BigDecimal endInventoryCore;

        private BigDecimal endInventoryAmount;
    }

    public static SupplyDemandHedgingOverviewResp transform(SupplyDemandHedgingResp item){
        SupplyDemandHedgingOverviewResp ret = new SupplyDemandHedgingOverviewResp();
        BeanUtils.copyProperties(item,ret);
        Map<String, BigDecimal> endInventoryMap = ListUtils.toMap(item.getEndInventory(),SupplyDemandHedgingResp.Item::getYearMonth,SupplyDemandHedgingResp.Item::getTotalAmount);
        Map<String,BigDecimal> supplyMap = ListUtils.toMap(item.getSupply(),SupplyDemandHedgingResp.Item::getYearMonth,SupplyDemandHedgingResp.Item::getTotalAmount);

        List<Item> retItemList = ListUtils.newArrayList();
        for(SupplyDemandHedgingResp.Item demandItem:item.getDemand()){
            Item retItem = new Item();
            retItem.setYearMonth(demandItem.getYearMonth());
            retItem.setDemandCore(demandItem.getTotalAmount());
            retItem.setDemandAmount(demandItem.getTotalAmount());
            retItem.setSupplyCore(supplyMap.get(demandItem.getYearMonth()));
            retItem.setSupplyAmount(supplyMap.get(demandItem.getYearMonth()));
            retItem.setEndInventoryCore(endInventoryMap.get(demandItem.getYearMonth()));
            retItem.setEndInventoryAmount(endInventoryMap.get(demandItem.getYearMonth()));
            retItemList.add(retItem);
        }
        ret.setItemList(retItemList);
        return ret;
    }


}
