package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryHealthOverviewRateConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryHealthOverviewZoneConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview.InventoryOverviewTrendReq;
import java.util.List;

public interface InventoryHealthOverviewService {

    List<InventoryHealthOverviewRateConfigDO> getAllInventoryHealthConfig();

    List<InventoryHealthOverviewZoneConfigDO> getAllInventoryHealthZoneConfig();


    List<InventoryOverviewData> queryInventoryOverviewReport(InventoryOverviewReq req);

    List<InventoryOverviewTrendData> queryInventoryOverviewTrendReport(InventoryOverviewTrendReq req);

    void sendInventoryHealthMail();



}
