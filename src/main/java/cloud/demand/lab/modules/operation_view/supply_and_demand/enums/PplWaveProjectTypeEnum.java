package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.Getter;

@Getter
public enum PplWaveProjectTypeEnum {

    NORMAL_PROJECT(0, "长尾报备"),

    IMPORTANT_PROJECT(1, "头部项目"),
    FORECAST(2, "长尾模型预测");

    final private Integer code;

    final private String name;

    PplWaveProjectTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(int code) {
        for (PplWaveProjectTypeEnum value : PplWaveProjectTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (PplWaveProjectTypeEnum value : PplWaveProjectTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return -1;
    }
 }
