package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.SoeRegionInfo;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_turnover.InventoryTurnoverReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_turnover.InventoryTurnoverReq.DateRange;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_turnover.InventoryTurnoverResp;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthAlgorithm;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryTurnoverService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigSnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.ActualInventoryListReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2.Item;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2.SafetyInventoryResult;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.ManualConfigServiceImpl;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.utils.OperationViewTools;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class InventoryTurnoverServiceImpl implements InventoryTurnoverService {
    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    DBHelper ckcldStdCrpDBHelper;


    @Resource
    OperationViewService2Impl operationViewService2;

    @Resource
    private DictService dictService;
    @Resource
    RedisHelper redisHelper;

    @Resource
    DBHelper demandDBHelper;

    @Resource
    DBHelper rrpDBHelper;

    @Resource
    DBHelper yuntiDBHelper;

    @Resource
    DBHelper ckcubesDBHelper;

    private final ExecutorService threadPool = Executors.newFixedThreadPool(10);
    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 1200, keyScript = "args[0]")
    public InventoryTurnoverResp queryInventoryTurnoverReport(InventoryTurnoverReq req) {
        //1.时间处理
        DateRange dateRange = req.getDateRange();
        String dateType = dateRange.getDateType();
        dateEntity dateEntity;
        switch (dateType) {
            case "year":
                dateEntity = getDateRangeByYear(dateRange);
                break;
            case "halfYear":
                dateEntity = getDateRangeByHalfYear(dateRange);
                break;
            case "quarter":
                dateEntity = getDateRangeByQuarter(dateRange);
                break;
            case "month":
                dateEntity = getDateRangeByMonth(dateRange);
                break;
            default:
                throw new WrongWebParameterException("dateRange的type参数错误");

        }
        //2.系统取数
        //2.1 取安全库存
        Map<String, BigDecimal> safeInventory;
        Future<Map<String, BigDecimal>> safeInventoryFuture = threadPool.submit(() -> getSafeInventory(req, dateEntity));
        //2.2 取实际库存
        Map<String, List<itoItem>> actualInventory;
        Future<Map<String, List<itoItem>>> actualInventoryFuture = threadPool.submit(() -> getActualInventory(req, dateEntity));
        //2.3 取售卖流水
        Map<String, List<itoItem>> sellRevenue;
        Future<Map<String, List<itoItem>>> sellRevenueFuture = threadPool.submit(() -> getSellRevenue(req, dateEntity));
        //2.4 取物理机公司退回量
        Map<String, List<itoItem>> returnCore;
        Future<Map<String, List<itoItem>>> returnCoreFuture = threadPool.submit(() -> getReturnCore(req, dateEntity));
        //2.5 取物理机转出量
        Map<String, List<itoItem>> outCore;
        Future<Map<String, List<itoItem>>> outCoreFuture = threadPool.submit(() -> getOutCore(req, dateEntity));
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start("safeInventoryFuture执行时间");
            safeInventory = safeInventoryFuture.get();
            stopWatch.stop();
            stopWatch.start("actualInventoryFuture执行时间");
            actualInventory = actualInventoryFuture.get();
            stopWatch.stop();
            stopWatch.start("sellRevenueFuture执行时间");
            sellRevenue = sellRevenueFuture.get();
            stopWatch.stop();
            stopWatch.start("returnCoreFuture执行时间");
            returnCore = returnCoreFuture.get();
            stopWatch.stop();
            stopWatch.start("outCoreFuture执行时间");
            outCore = outCoreFuture.get();
            stopWatch.stop();
        } catch (InterruptedException | ExecutionException e) {
            log.info("queryInventoryTurnoverReport的线程池执行失败， 报错为：" + e.getMessage());
            throw new BizException("线程池执行任务失败： " + e.getMessage());
        }
        log.info(stopWatch.prettyPrint());
        //3.整合所有的可用区和实例类型
        Set<String> allZoneNames = new HashSet<>();
        if (ListUtils.isNotEmpty(safeInventory)) {
            allZoneNames.addAll(safeInventory.entrySet().stream().map(o -> {
                String key = o.getKey();
                String[] split = key.split("@");
                return split[0];
            }).collect(Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(actualInventory)) {
            allZoneNames.addAll(actualInventory.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(sellRevenue)) {
            allZoneNames.addAll(sellRevenue.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(returnCore)) {
            allZoneNames.addAll(returnCore.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(outCore)) {
            allZoneNames.addAll(outCore.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(
                    Collectors.toList()));
        }
        Set<String> allInstance = new HashSet<>();
        if (ListUtils.isNotEmpty(safeInventory)) {
            allInstance.addAll(safeInventory.entrySet().stream().map(o -> {
                String key = o.getKey();
                String[] split = key.split("@");
                return split[1];
            }).collect(Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(actualInventory)) {
            allInstance.addAll(actualInventory.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(sellRevenue)) {
            allInstance.addAll(sellRevenue.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(returnCore)) {
            allInstance.addAll(returnCore.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(
                    Collectors.toList()));
        }
        if (ListUtils.isNotEmpty(outCore)) {
            allInstance.addAll(outCore.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(
                    Collectors.toList()));
        }

        //4.聚合数据
        InventoryTurnoverResp resp = new InventoryTurnoverResp();
        List<InventoryTurnoverResp.Item> data = new ArrayList<>();
        //获取可用区类型映射
        Map<String, InventoryHealthMainZoneNameConfigDO> mainZoneConfig = getMainZoneConfig();
        //获取机型类型映射
        Map<String, InventoryHealthMainInstanceTypeConfigDO> mainInstanceConfig = getMainInstanceConfig();
        //获取地区相关信息
        Map<String, SoeRegionInfo> regionInfo = getRegionInfo();

        Map<String, List<String>> date = dateEntity.getFullDate();
        for (String zoneName : allZoneNames) {
            for (String ins : allInstance) {
                for(String range: date.keySet()) {
                    InventoryTurnoverResp.Item item = new InventoryTurnoverResp.Item();
                    item.setZoneName(zoneName);
                    item.setInstanceType(ins);
                    item.setStatisticTime(range);
                    //设置区域地域境内外国家等信息
                    InventoryHealthMainZoneNameConfigDO zoneConfig = mainZoneConfig.get(zoneName);
                    InventoryHealthMainInstanceTypeConfigDO insConfig = mainInstanceConfig.get(
                            ins);
                    SoeRegionInfo soeRegionInfo = regionInfo.get(zoneName);
                    if (zoneConfig != null) {
                        item.setZoneCategory(zoneConfig.getTypeName());
                    }
                    if (insConfig != null) {
                        item.setInstanceCategory(insConfig.getType2Name());
                    }
                    if (soeRegionInfo != null) {
                        item.setCountryName(soeRegionInfo.getCountryName());
                        item.setRegionName(soeRegionInfo.getRegionName());
                        item.setCustomhouseTitle(soeRegionInfo.getCustomhouseTitle());
                    }
                    String key = String.join("@", zoneName, ins, range);
                    BigDecimal safeInv = safeInventory.getOrDefault(key, BigDecimal.ZERO);
                    BigDecimal actualInv = NumberUtils.sum(actualInventory.get(key), o -> o.getCores());
                    BigDecimal sell = NumberUtils.sum(sellRevenue.get(key), o -> o.getCores());
                    BigDecimal reCore = NumberUtils.sum(returnCore.get(key), o -> o.getCores());
                    BigDecimal ouCore = NumberUtils.sum(outCore.get(key), o -> o.getCores());
                    BigDecimal outboundCore = sell.add(reCore).add(ouCore);
                    item.setSellRevenue(sell);
                    item.setReturnCores(reCore);
                    item.setOutCores(ouCore);
                    //查看是否剔除安全库存，默认不剔除
                    if (req.getReduceHealthInventory() == null) {
                        req.setReduceHealthInventory(false);
                    }
                    boolean reduce = req.getReduceHealthInventory();
                    //计算各个指标
                    int dateNum = date.get(range).size();
                    //安全库存
                    item.setSafeInventory(safeInv);
                    //平均出库流水
                    item.setAverageSellRevenue(outboundCore.divide(new BigDecimal(dateNum), 6, RoundingMode.HALF_UP));
                    //平均实际库存
                    BigDecimal averageActual = actualInv.divide(new BigDecimal(dateNum), 6, RoundingMode.HALF_UP);
                    //查看是否需要减去安全库存，如果为true 说明需要减去安全库存
                    if (reduce) {
                        averageActual = averageActual.subtract(safeInv).max(BigDecimal.ZERO);
                    }
                    //设置平均库存
                    item.setAverageInventory(averageActual);
                    //计算ito 平均库存除以平均售卖流水
                    if (item.getAverageSellRevenue().compareTo(BigDecimal.ZERO) != 0) {
                        item.setIto(averageActual.divide(item.getAverageSellRevenue(), 6 , RoundingMode.HALF_UP));
                    }
                    data.add(item);
                }
            }
        }
        resp.setData(data);
        return resp;
    }

    //获取物理机转出量
    private Map<String, List<itoItem>> getOutCore(InventoryTurnoverReq req, dateEntity dateEntity) {
        WhereSQL condition = getReturnAndOutBasicCondition(req);
        condition.and("compute_type = 'CPU'");
        condition.and("product_type = 'CVM'");
        condition.and("transfer_type = '转出'");
        condition.and("stat_time = ?", req.getStatDate());
        condition.addGroupBy("device_type, zone_name ,yearmonth");
        String field = "txy_zone_name as zone_name, device_type, sum(logic_core_num) as cores, DATE_FORMAT(end_time, '%Y-%m') as yearmonth";
        Map<String, List<String>> fullDate = dateEntity.getFullDate();
        DateRange dateRange = req.getDateRange();
        String startRange = dateRange.getStart();
        String endRange = dateRange.getEnd();
        String start = fullDate.get(startRange).get(0);
        List<String> stringList = fullDate.get(endRange);
        String end = stringList.get(stringList.size() - 1);
        condition.and("date(end_time) between ? and ?", start, end);
        String sql = "select " + field + " from report_erp_transfer_detail" + condition.getSQL();
        List<ReturnAndOutDO> all = rrpDBHelper.getRaw(ReturnAndOutDO.class, sql, condition.getParams());
        List<itoItem> result = new ArrayList<>();
        List<CloudDemandCsigDeviceExtendInfoDO> temp = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        Map<String, String> deviceToIns = ListUtils.toMap(temp, o -> o.getDeviceType(), o -> o.getInstanceTypeEng());
        for (ReturnAndOutDO reAndOut : all) {
            itoItem item = new itoItem();
            item.setZoneName(reAndOut.getZoneName());
            item.setCores(reAndOut.getCores());
            item.setInstanceType(deviceToIns.get(reAndOut.getDeviceType()));
            String yearMonth = reAndOut.getYearMonth();
            if (dateRange.getDateType().equals("quarter")) {
                // 解析输入的年月字符串
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                LocalDate date = LocalDate.parse(yearMonth + "-01", formatter);
                // 获取年份
                int year = date.getYear();
                // 获取月份并计算季度
                int month = date.getMonthValue();
                int quarter = (month - 1) / 3 + 1;
                item.setDate(String.format("%dQ%d", year, quarter));
            } else if(dateRange.getDateType().equals("year")) {
                item.setDate(yearMonth.substring(0, 4));
            } else {
                item.setDate(yearMonth);
            }
            result.add(item);
        }
        //剔除zoneName与instanceType为null的字段
        result = result.stream().filter(o -> o.getZoneName() != null && !o.getZoneName().isEmpty() && o.getInstanceType() != null && !o.getInstanceType().isEmpty()).collect(
                Collectors.toList());
        return ListUtils.toMapList(result, o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getDate()), o -> o);
    }

    //获取物理机公司退回量
    private Map<String, List<itoItem>> getReturnCore(InventoryTurnoverReq req, dateEntity dateEntity) {
        WhereSQL condition = getReturnAndOutBasicCondition(req);
        condition.and("compute_type = 'CPU'");
        condition.and("product_type = 'CVM'");
        condition.and("stat_time = ?", req.getStatDate());
        condition.addGroupBy("device_type, zone_name, yearmonth");
        String field = "txy_zone_name as zone_name, device_type, sum(logic_cpu_core) as cores, DATE_FORMAT(finish_time, '%Y-%m') as yearmonth";
        Map<String, List<String>> fullDate = dateEntity.getFullDate();
        DateRange dateRange = req.getDateRange();
        String startRange = dateRange.getStart();
        String endRange = dateRange.getEnd();
        String start = fullDate.get(startRange).get(0);
        List<String> stringList = fullDate.get(endRange);
        String end = stringList.get(stringList.size() - 1);
        condition.and("date(finish_time) between ? and ?", start, end);
        String sql = "select " + field + " from report_erp_return_detail" + condition.getSQL();
        List<ReturnAndOutDO> all = rrpDBHelper.getRaw(ReturnAndOutDO.class, sql, condition.getParams());
        List<itoItem> result = new ArrayList<>();
        List<CloudDemandCsigDeviceExtendInfoDO> temp = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        Map<String, String> deviceToIns = ListUtils.toMap(temp, o -> o.getDeviceType(), o -> o.getInstanceTypeEng());
        for (ReturnAndOutDO reAndOut : all) {
            itoItem item = new itoItem();
            item.setZoneName(reAndOut.getZoneName());
            item.setCores(reAndOut.getCores());
            item.setInstanceType(deviceToIns.get(reAndOut.getDeviceType()));
            String yearMonth = reAndOut.getYearMonth();
            if (dateRange.getDateType().equals("quarter")) {
                // 解析输入的年月字符串
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                LocalDate date = LocalDate.parse(yearMonth + "-01", formatter);
                // 获取年份
                int year = date.getYear();
                // 获取月份并计算季度
                int month = date.getMonthValue();
                int quarter = (month - 1) / 3 + 1;
                item.setDate(String.format("%dQ%d", year, quarter));
            } else if(dateRange.getDateType().equals("year")) {
                item.setDate(yearMonth.substring(0, 4));
            } else {
                item.setDate(yearMonth);
            }
            result.add(item);
        }
        //剔除zoneName与instanceType为null的字段
        result = result.stream().filter(o -> o.getZoneName() != null && !o.getZoneName().isEmpty() && o.getInstanceType() != null && !o.getInstanceType().isEmpty()).collect(
                Collectors.toList());
        return ListUtils.toMapList(result, o-> String.join("@", o.getZoneName(), o.getInstanceType(), o.getDate()), o -> o);

    }

    private synchronized WhereSQL getReturnAndOutBasicCondition(InventoryTurnoverReq req) {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            condition.and("txy_customhouse_title in (?)", req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            condition.and("txy_region_name in (?)", req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            condition.and("txy_area_name in (?)", req.getAreaName());
        }
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            condition.and("txy_zone_name in (?)",req.getZoneName());
        }
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, List<String>> insToDevice = bean.getCsigInstanceTypeToDeviceTypeMap();
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            List<String> deviceType = new ArrayList<>();
            for (String ins : req.getInstanceType()) {
                List<String> types = insToDevice.get(ins);
                if (ListUtils.isNotEmpty(types)) {
                    deviceType.addAll(types);
                }
            }
            condition.and("device_type in (?)", deviceType);
        }
        WhereSQL categoryCondition =operationViewService2.genZoneAndDeviceCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), false, DateUtils.formatDate(
                LocalDate.now().plusDays(-1)), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        return condition;
    }




    private dateEntity getDateRangeByMonth(DateRange dateRange) {
        List<String> months = new ArrayList<>();
        YearMonth start = YearMonth.parse(dateRange.getStart());
        YearMonth end = YearMonth.parse(dateRange.getEnd());
        while(!start.isAfter(end)) {
            months.add(start.toString());
            start = start.plusMonths(1);
        }
        Map<String, List<String>> result = new HashMap<>();
        Map<String, List<String>> lastDate = new HashMap<>();
        for (String month : months) {
            YearMonth parse = YearMonth.parse(month);
            LocalDate startDate = LocalDate.of(parse.getYear(), parse.getMonthValue(), 1);
            LocalDate endDate = LocalDate.of(parse.getYear(), parse.getMonthValue(), 1).plusMonths(1).plusDays(-1);
            LocalDate now = LocalDate.now().plusDays(-1);
            List<String> days = new ArrayList<>();
            while(!startDate.isAfter(endDate) && !startDate.isAfter(now)) {
                days.add(DateUtils.formatDate(startDate));
                startDate = startDate.plusDays(1);
            }
            days.sort(String::compareTo);
            List<String> last = new ArrayList<>();
            last.add(days.get(days.size() - 1));
            lastDate.put(month, last);
            result.put(month, days);
        }
        dateEntity dateEntity = new dateEntity();
        dateEntity.setFullDate(result);
        dateEntity.setLastDate(lastDate);
        return dateEntity;
    }

    private dateEntity getDateRangeByQuarter(DateRange dateRange) {
        List<String> quarters = new ArrayList<>();
        String start = dateRange.getStart();
        String end = dateRange.getEnd();
        String current = start;
        while(!current.equals(end)) {
            quarters.add(current);
            int year = Integer.parseInt(current.substring(0, 4));
            int quarter = Integer.parseInt(current.substring(5, 6));
            if (quarter == 4) {
                year ++;
                quarter = 1;
            }else {
                quarter ++;
            }
            current = year + "Q" + quarter;
        }
        quarters.add(end);
        Map<String, List<String>> result = new HashMap<>();
        Map<String, List<String>> lastDate = new HashMap<>();
        for (String quarter : quarters) {
            int year = Integer.parseInt(quarter.substring(0, 4));
            int q = Integer.parseInt(quarter.substring(5, 6));
            LocalDate startDate;
            LocalDate endDate;
            List<Month> months;
            switch (q) {
                case 1:
                    startDate = LocalDate.of(year, 1, 1);
                    endDate = LocalDate.of(year, 3, 31);
                    months = ListUtils.newArrayList(Month.JANUARY, Month.FEBRUARY, Month.MARCH);
                    break;
                case 2:
                    startDate = LocalDate.of(year, 4, 1);
                    endDate = LocalDate.of(year, 6, 30);
                    months = ListUtils.newArrayList(Month.APRIL, Month.MAY, Month.JUNE);
                    break;
                case 3:
                    startDate = LocalDate.of(year, 7, 1);
                    endDate = LocalDate.of(year, 9, 30);
                    months = ListUtils.newArrayList(Month.JULY, Month.AUGUST, Month.SEPTEMBER);
                    break;
                case 4:
                    startDate = LocalDate.of(year, 10, 1);
                    endDate = LocalDate.of(year, 12, 31);
                    months = ListUtils.newArrayList(Month.OCTOBER, Month.NOVEMBER, Month.DECEMBER);
                    break;
                default:
                    throw new WrongWebParameterException("错误的参数: " + quarter);
            }
            LocalDate now = LocalDate.now().plusDays(-1);
            List<String> days = new ArrayList<>();
            while(!startDate.isAfter(endDate) && !startDate.isAfter(now)) {
                days.add(DateUtils.formatDate(startDate));
                startDate = startDate.plusDays(1);
            }
            Set<String> last = new HashSet<>();
            for (Month month : months) {
                YearMonth yearMonth = YearMonth.of(year, month);
                LocalDate startLocalDate = yearMonth.atDay(1);
                LocalDate localDate = yearMonth.atEndOfMonth();
                if (startLocalDate.isAfter(now)) {
                    break;
                }
                String temp = localDate.isAfter(now) ? now.toString() : localDate.toString();
                last.add(temp);
            }
            days.sort(String::compareTo);
            result.put(quarter, days);
            lastDate.put(quarter, new ArrayList<>(last));
        }
        dateEntity dateEntity = new dateEntity();
        dateEntity.setFullDate(result);
        dateEntity.setLastDate(lastDate);
        return dateEntity;
    }

    private dateEntity getDateRangeByHalfYear(DateRange dateRange) {
        List<String> halfYears = new ArrayList<>();
        String start = dateRange.getStart();
        String end = dateRange.getEnd();
        String current = start;
        while(!current.equals(end)) {
            halfYears.add(current);
            int year = Integer.parseInt(current.substring(0, 4));
            int half = Integer.parseInt(current.substring(5, 6));
            if (half == 2) {
                year ++;
                half = 1;
            }else {
                half ++;
            }
            current = year + "H" + half;
        }
        halfYears.add(end);
        Map<String, List<String>> result = new HashMap<>();
        Map<String, List<String>> lastDate = new HashMap<>();
        for (String halfYear : halfYears) {
            int year = Integer.parseInt(halfYear.substring(0, 4));
            int half = Integer.parseInt(halfYear.substring(5, 6));
            LocalDate startDate;
            LocalDate endDate;
            List<Month> months;
            switch (half) {
                case 1:
                    startDate = LocalDate.of(year, 1, 1);
                    endDate = LocalDate.of(year, 6, 30);
                    months = ListUtils.newArrayList(Month.JANUARY, Month.FEBRUARY, Month.MARCH, Month.APRIL, Month.MAY, Month.JUNE);
                    break;
                case 2:
                    startDate = LocalDate.of(year, 7, 1);
                    endDate = LocalDate.of(year, 12, 31);
                    months = ListUtils.newArrayList(Month.JULY, Month.AUGUST, Month.SEPTEMBER, Month.OCTOBER, Month.NOVEMBER, Month.DECEMBER);
                    break;
                default:
                    throw new WrongWebParameterException("错误的参数: " + halfYear);
            }
            LocalDate now = LocalDate.now().plusDays(-1);
            List<String> days = new ArrayList<>();
            while(!startDate.isAfter(endDate) && !startDate.isAfter(now)) {
                days.add(DateUtils.formatDate(startDate));
                startDate = startDate.plusDays(1);
            }
            Set<String> last = new HashSet<>();
            for (Month month : months) {
                YearMonth yearMonth = YearMonth.of(year, month);
                LocalDate startLocalDate = yearMonth.atDay(1);
                LocalDate localDate = yearMonth.atEndOfMonth();
                if (startLocalDate.isAfter(now)) {
                    break;
                }
                String temp = localDate.isAfter(now) ? now.toString() : localDate.toString();
                last.add(temp);
            }
            days.sort(String::compareTo);
            result.put(halfYear, days);
            lastDate.put(halfYear, new ArrayList<>(last));
        }
        dateEntity dateEntity = new dateEntity();
        dateEntity.setFullDate(result);
        dateEntity.setLastDate(lastDate);
        return dateEntity;
    }

    private dateEntity getDateRangeByYear(DateRange dateRange) {
        List<String> years = new ArrayList<>();
        int startYear = Integer.parseInt(dateRange.getStart());
        int endYear = Integer.parseInt(dateRange.getEnd());
        int current = startYear;
        while(current <= endYear) {
            years.add(String.valueOf(current));
            current ++;
        }
        Map<String, List<String>> lastDate = new HashMap<>();
        Map<String, List<String>> result = new HashMap<>();
        for (String year : years) {
            LocalDate start = LocalDate.of(Integer.parseInt(year), 1, 1);
            LocalDate end = LocalDate.of(Integer.parseInt(year), 12, 31);
            LocalDate now = LocalDate.now().plusDays(-1);
            List<String> days = new ArrayList<>();
            while(!start.isAfter(end) && !start.isAfter(now)) {
                days.add(DateUtils.formatDate(start));
                start = start.plusDays(1);
            }
            Set<String> map = new HashSet<>();
            Month[] months = Month.values();
            for (Month month : months) {
                YearMonth yearMonth = YearMonth.of(Integer.parseInt(year), month);
                LocalDate startDate = yearMonth.atDay(1);
                LocalDate localDate = yearMonth.atEndOfMonth();
                if (startDate.isAfter(now)) {
                    break;
                }
                String last = localDate.isAfter(now) ? now.toString() : localDate.toString();
                map.add(last);
            }
            days.sort(String::compareTo);
            lastDate.put(year, new ArrayList<>(map));
            result.put(year, days);
        }
        dateEntity dateEntity = new dateEntity();
        dateEntity.setFullDate(result);
        dateEntity.setLastDate(lastDate);
        return dateEntity;
    }

    private Map<String, List<itoItem>> getSellRevenue(InventoryTurnoverReq req, dateEntity dateEntity) {
        WhereSQL condition = req.genBasicCondition();
        WhereSQL categoryCondition = operationViewService2.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), false, req.getStatDate(), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        List<itoItem> allItem = new ArrayList<>();
        Map<String, List<String>> lastDate = dateEntity.getLastDate();
        for (Entry<String, List<String>> entry : lastDate.entrySet()) {
            List<String> dates = entry.getValue();
            WhereSQL newCondition = condition.copy();
            newCondition.and("stat_time in (?)", dates);
            String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/sell_revenue_data.sql");
            sql = sql.replace("${BASIC_CONDITION}", newCondition.getSQL());
            List<sellRevenueDO> all = ckcldStdCrpDBHelper.getRaw(sellRevenueDO.class, sql, newCondition.getParams());
            if (ListUtils.isNotEmpty(all)) {
                List<itoItem> collect = all.stream().map(o -> {
                    itoItem actualItem = new itoItem();
                    actualItem.setCores(o.getCores());
                    actualItem.setDate(entry.getKey());
                    actualItem.setInstanceType(o.getInstanceType());
                    actualItem.setZoneName(o.getZoneName());
                    return actualItem;
                }).collect(Collectors.toList());
                allItem.addAll(collect);
            }
        }
        //剔除zoneName与instanceType为null的字段
        allItem = allItem.stream().filter(o -> o.getZoneName() != null && !o.getZoneName().isEmpty() && o.getInstanceType() != null && !o.getInstanceType().isEmpty()).collect(
                Collectors.toList());
        return ListUtils.toMapList(allItem, o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getDate()), o -> o);

    }

    private Map<String, List<itoItem>> getActualInventory(InventoryTurnoverReq req, dateEntity dateEntity) {
        //1.获取实际库存
        List<itoItem> allItem = new ArrayList<>();
        Map<String, List<String>> date = dateEntity.getFullDate();
        for (Entry<String, List<String>> entry : date.entrySet()) {
            List<String> days = entry.getValue();
            WhereSQL condition = req.genBasicCondition();
            condition.and("stat_time between ? and ?", days.get(0), days.get(days.size() - 1));
            if (ListUtils.isNotEmpty(req.getLineType())) {
                condition.and("line_type in (?)", req.getLineType());
            }
            WhereSQL categoryCondition = operationViewService2.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), false, DateUtils.formatDate(LocalDate.now().plusDays(-1)), req.getCustomhouseTitle());
            condition.and(categoryCondition);
            String groupBy = "zone_name, instance_type";
            condition.addGroupBy(groupBy);
            String field = "zone_name, instance_type, sum(actual_inv) cores";
            String sql = "select " + field + " from dws_actual_inventory_df" + condition.getSQL();
            List<ActualInventoryDO> all = ckcldDBHelper.getRaw(ActualInventoryDO.class, sql, condition.getParams());
            if (ListUtils.isNotEmpty(all)) {
                List<itoItem> collect = all.stream().map(o -> {
                    itoItem actualItem = new itoItem();
                    actualItem.setCores(o.getCores());
                    actualItem.setDate(entry.getKey());
                    actualItem.setInstanceType(o.getInstanceType());
                    actualItem.setZoneName(o.getZoneName());
                    return actualItem;
                }).collect(Collectors.toList());
                allItem.addAll(collect);
            }
        }
        //剔除zoneName与instanceType为null的字段
        allItem = allItem.stream().filter(o -> o.getZoneName() != null && !o.getZoneName().isEmpty() && o.getInstanceType() != null && !o.getInstanceType().isEmpty()).collect(
                Collectors.toList());
        //4.按照zoneName与instanceType聚合
        return ListUtils.toMapList(allItem, o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getDate()), o -> o);
    }


    @Data
    public static class ActualInventoryDO {
        @Column("zone_name")
        private String zoneName;
        @Column("instance_type")
        private String instanceType;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class itoItem {
        private String zoneName;
        private String instanceType;
        private BigDecimal cores;
        private String date;
    }


    @Data
    public static class sellRevenueDO {
        @Column("zone_name")
        private String zoneName;
        @Column("instance_type")
        private String instanceType;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class ReturnAndOutDO {
        @Column("zone_name")
        private String zoneName;
        @Column("device_type")
        private String deviceType;
        @Column("yearmonth")
        private String yearMonth;
        @Column("cores")
        private BigDecimal cores;
    }

    @Data
    public static class StaticCvmTypeDO {
        @Column("cvmtype")
        private String cvmType;
        @Column("ginsfamily")
        private String ginsFamily;
    }

    public Map<String, BigDecimal> getSafeInventory(InventoryTurnoverReq req, dateEntity dateEntity) {
        WhereSQL condition = req.genBasicCondition();
        WhereSQL categoryCondition =operationViewService2.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), false, DateUtils.formatDate(LocalDate.now().plusDays(-1)), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        String algorithm = InventoryHealthAlgorithm.getNameFromCode(operationViewAlgorithm);
        Map<String, BigDecimal> result = new HashMap<>();
        Map<String, List<String>> dates = dateEntity.getFullDate();
        List<SafeInvItem>  all = new ArrayList<>();
        for (Entry<String, List<String>> entry : dates.entrySet()) {
            String date = entry.getKey();
            List<String> value = entry.getValue();
            String end = value.get(value.size() - 1);
            WhereSQL safeCondition = condition.copy();
            //1.从dws_safe_inventory_history_monthly_df表中获取包月安全库存
            safeCondition.and("algorithm = ?", algorithm);
            safeCondition.and("product_type = 'CVM'");
            safeCondition.and("stat_time = ?", end);
            safeCondition.and("customer_custom_group = 'ALL'");
            String field = "zone_name, instance_type, monthly_safety_inv";
            String sql = "select " + field + " from dws_safe_inventory_history_monthly_df" + safeCondition.getSQL();
            List<SafeInventoryDO> safeInvs = ckcldDBHelper.getRaw(SafeInventoryDO.class, sql,
                    safeCondition.getParams());
            Map<String, List<SafeInventoryDO>> safeInvsMap = ListUtils.toMapList(safeInvs,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
            //2.从dws_buffer_safe_inventory_df表取弹性配额
            LocalDate temp = LocalDate.parse(end);
            String curWeekMonday = DateUtils.formatDate(temp.with(DayOfWeek.MONDAY));
            WhereSQL bufferCondition = condition.copy();
            bufferCondition.and("stat_time = ?", curWeekMonday);
            bufferCondition.and("product_type = 'CVM'");
            List<DwsBufferSafeInventoryDfDO> bufferInvs = ckcldDBHelper.getAll(DwsBufferSafeInventoryDfDO.class,
                    bufferCondition.getSQL(), bufferCondition.getParams()).stream().map(item -> {
                // 确保弹性备货 > 0
                item.setMckBufferSafetyInv(NumberUtils.max(item.getMckBufferSafetyInv(), BigDecimal.ZERO));
                item.setBufferSafetyInv(NumberUtils.max(item.getBufferSafetyInv(), BigDecimal.ZERO));
                return item;
            }).collect(Collectors.toList());
            Map<String, List<DwsBufferSafeInventoryDfDO>> bufferInvsMap = ListUtils.toMapList(bufferInvs,
                    o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
            //3.获取人工调整
            WhereSQL manualCondition = condition.copy();
            manualCondition.and("stat_time = ?", end);
            List<InventoryHealthManualConfigSnapshotDO> manualList =
                    demandDBHelper.getAll(InventoryHealthManualConfigSnapshotDO.class, manualCondition.getSQL(), manualCondition.getParams());
            Map<String, BigDecimal> manualInvs = ListUtils.toMap(manualList, o ->
                            Strings.join("@", o.getZoneName(), o.getInstanceType()),
                    InventoryHealthManualConfigSnapshotDO::getNum);
            Set<String> zoneAndIns = new HashSet<>();
            if (ListUtils.isNotEmpty(safeInvsMap.keySet())) {
                zoneAndIns.addAll(safeInvsMap.keySet());
            }
            if (ListUtils.isNotEmpty(bufferInvsMap.keySet())) {
                zoneAndIns.addAll(bufferInvsMap.keySet());
            }
            if (ListUtils.isNotEmpty(manualInvs.keySet())) {
                zoneAndIns.addAll(manualInvs.keySet());
            }
            for (String zoneAndIn : zoneAndIns) {
                SafeInvItem safeInvItem = new SafeInvItem();
                String[] split = zoneAndIn.split("@");
                if (split.length < 2) {
                    continue;
                }
                String zone = split[0];
                String ins = split[1];
                safeInvItem.setZoneName(zone);
                safeInvItem.setInstanceType(ins);
                safeInvItem.setDate(date);
                List<SafeInventoryDO> safeInventoryDOS = safeInvsMap.get(zoneAndIn);
                BigDecimal safeInv = NumberUtils.sum(safeInventoryDOS, o -> o.getSafeInv());
                List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = bufferInvsMap.get(zoneAndIn);
                BigDecimal bufferInv = NumberUtils.sum(bufferSafeInventoryDfDOS, o -> o.getFinalBufferSafetyInv());
                BigDecimal manual = manualInvs.get(zoneAndIn) == null? BigDecimal.ZERO: manualInvs.get(zoneAndIn);
                safeInvItem.setSafeInv(NumberUtils.max(safeInv.add(bufferInv).add(manual), BigDecimal.ZERO));
                all.add(safeInvItem);
            }
        }
        Map<String, List<SafeInvItem>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getDate()), o -> o);
        for (Entry<String, List<SafeInvItem>> entry : mapList.entrySet()) {
            result.put(entry.getKey(), NumberUtils.sum(entry.getValue(), o -> o.getSafeInv()));
        }
        return result;
    }

    @Data
    public static class SafeInventoryDO {
        @Column("zone_name")
        private String zoneName;
        @Column("instance_type")
        private String InstanceType;
        @Column("monthly_safety_inv")
        private BigDecimal safeInv;
    }

    public List<SafeInvItem> getSafeInventoryThread(InventoryTurnoverReq req, ActualInventoryListReq params, Entry<String, List<String>> entry, WhereSQL condition) {
        List<String> value = entry.getValue();
        String statDate = value.get(value.size() - 1);
        params.setStatTime(statDate);
        List<DwsActualInventoryDfDO> all = operationViewService2.getActualInventoryList(params, condition);
        LocalDate temp = LocalDate.parse(statDate);
        String curWeekMonday = DateUtils.formatDate(temp.with(DayOfWeek.MONDAY));
        params.setStatTime(curWeekMonday);
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = operationViewService2.getBufferSafeInventoryList(params, condition);
        // 合并实际库存和弹性库存的结果，并汇总到总的结果中
        params.setStatTime(statDate);
        List<OperationViewResp2.Item> mergedItems = operationViewService2.mergeActualBufferInventoryData(params.getStatTime(), all, bufferSafeInventoryDfDOS);
        // 构造实际库存接口返回
        OperationViewResp2 operationViewResp2 = constructResp(mergedItems);
        OperationViewReq2 operationViewReq2 = invReqToOpViewReq(req, params);
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        Function<OperationViewResp2.Item, SafetyInventoryResult> getter;
        switch (operationViewAlgorithm) {
            case "historyWeekPeak":
                operationViewService2.buildHistoryWeekPeak(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekPeak;
                break;
            case "historyWeekDiff":
                operationViewService2.buildHistoryWeekDiff(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekDiff;
                break;
            case "futureWeekPeak":
                operationViewService2.buildFutureWeekPeak(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getFutureWeekPeak;
                break;
            case "historyWeekPeakForecastWN":
                operationViewService2.buildHistoryWeekPeakDemand(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekPeakForecastWN;
                break;
            default:
                throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
        }
        List<OperationViewResp2.Item> data = operationViewResp2.getData();
        fillNullSafetyInvItem(data, statDate, operationViewAlgorithm);
        List<SafeInvItem> result = new ArrayList<>();
        for (OperationViewResp2.Item datum : data) {
            SafetyInventoryResult apply = getter.apply(datum);
            String zoneName = datum.getZoneName();
            String instanceType = datum.getInstanceType();
            if (apply != null && !zoneName.isEmpty() && !instanceType.isEmpty()) {
                SafeInvItem safeInvItem = new SafeInvItem();
                safeInvItem.setDate(entry.getKey());
                safeInvItem.setZoneName(zoneName);
                safeInvItem.setInstanceType(instanceType);
                safeInvItem.setSafeInv(apply.getSafetyInv());
                result.add(safeInvItem);
            }
        }
        return result;
    }

    private void fillNullSafetyInvItem(List<Item> data, String statDate, String operationViewAlgorithm) {
        Map<String, BigDecimal> manualConfigMap =
                SpringUtil.getBean(ManualConfigServiceImpl.class).querySnapshotManual(statDate);
        for (Item datum : data) {
            OperationViewResp2.SafetyInventoryResult result = new OperationViewResp2.SafetyInventoryResult();
            result.setSafetyInv(datum.getBufferSafetyInv());
            switch (operationViewAlgorithm) {
                case "historyWeekPeak":
                    datum.setHistoryWeekPeak(result);
                    break;
                case "historyWeekDiff":
                    datum.setHistoryWeekDiff(result);
                    break;
                case "futureWeekPeak":
                    datum.setFutureWeekPeak(result);
                    break;
                case "historyWeekPeakForecastWN":
                    datum.setHistoryWeekPeakForecastWN(result);
                    break;
                default:
                    throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
            }
            String manualConfigKey = Strings.join("@", datum.getZoneName(), datum.getInstanceType());

            result.setSafeInvManualConfig(manualConfigMap.getOrDefault(manualConfigKey, BigDecimal.ZERO));

            if (result.getSafetyInv() != null) {
                result.setSafetyInv(NumberUtils.max(result.getSafetyInv().add(result.getSafeInvManualConfig()), BigDecimal.ZERO));
            }
        }
    }


    @Data
    public static class SafeInvItem {
        private String zoneName;
        private String instanceType;
        private String date;
        private BigDecimal safeInv;
    }


    private OperationViewReq2 invReqToOpViewReq(InventoryTurnoverReq req, ActualInventoryListReq tempReq) {
        OperationViewReq2 opReq = new OperationViewReq2();
        opReq.setZoneCategory(req.getZoneCategory());
        opReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        opReq.setInstanceType(req.getInstanceType());
        opReq.setCustomhouseTitle(req.getCustomhouseTitle());
        opReq.setAreaName(req.getAreaName());
        opReq.setRegionName(req.getRegionName());
        opReq.setZoneName(req.getZoneName());
        // 默认不组合机型
        opReq.setIsCombine(tempReq.getIsCombine());
        opReq.setMaterialType(req.getMaterialType());
        // 将客户组设置为ALL
        opReq.setCustomerCustomGroup("ALL");
        opReq.setLineType(tempReq.getLineType());
        opReq.setDate(DateUtils.parse(tempReq.getStatTime()));
        opReq.setCategoryDate(tempReq.getCategoryDate());
        return opReq;
    }

    private OperationViewResp2 constructResp(List<Item> mergedItems) {
        OperationViewResp2 opResp = new OperationViewResp2();
        opResp.setData(mergedItems.stream().map(item -> {
            // 安全库存信息此时还没生成，只转换实际存库数据
            OperationViewResp2.Item opItem = new OperationViewResp2.Item();
            opItem.setProductType("CVM"); // 固定 CVM
            opItem.setCustomhouseTitle(item.getCustomhouseTitle());
            opItem.setAreaName(item.getAreaName());
            opItem.setRegionName(item.getRegionName());
            opItem.setZoneName(item.getZoneName());
            opItem.setInstanceType(item.getInstanceType());
            //  总库存
            opItem.setInvTotalNum(BigDecimal.valueOf(item.getInvTotalNum().intValue()));
            //  弹性服务水平/系数
            opItem.setBufferServiceLevel(item.getBufferServiceLevel());
            opItem.setBufferServiceLevelFactor(item.getBufferServiceLevelFactor());
            //  弹性规模日均值
            opItem.setBufferAverageCore(item.getBufferAverageCore());
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            opItem.setBufferSafetyInv(item.getBufferSafetyInv());
            return opItem;
        }).collect(Collectors.toList()));

        return opResp;
    }

    /**
     * 获取可用区对应可用区类型
     * @return
     */
    public Map<String, InventoryHealthMainZoneNameConfigDO> getMainZoneConfig() {
        //只设置时间，获取全量的信息
        WhereSQL cond = new WhereSQL();
        cond.and("date = ?", DateUtils.formatDate(LocalDate.now().plusDays(-1)));
        List<InventoryHealthMainZoneNameConfigDO> all = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class,
                cond.getSQL(), cond.getParams());
        return ListUtils.toMap(all, InventoryHealthMainZoneNameConfigDO::getZoneName, o -> o);
    }

    /**
     * 获取实例类型对应的机型类型
     * @return
     */
    public Map<String, InventoryHealthMainInstanceTypeConfigDO> getMainInstanceConfig() {
        //只设置时间，获取全量的信息
        WhereSQL cond = new WhereSQL();
        cond.and("date = ?", DateUtils.formatDate(LocalDate.now().plusDays(-1)));
        List<InventoryHealthMainInstanceTypeConfigDO> all = demandDBHelper.getAll(
                InventoryHealthMainInstanceTypeConfigDO.class, cond.getSQL(), cond.getParams());
        return ListUtils.toMap(all, InventoryHealthMainInstanceTypeConfigDO::getInstanceType, o -> o);
    }

    /**
     * 获取所有可用区、区域、国家等信息
     * @return
     */
    public Map<String, SoeRegionInfo> getRegionInfo() {
        String sql = "select distinct region_name ,country_name,customhouse_title from bas_soe_region_name_country";
        List<SoeRegionNameCountryDO> region2Country = demandDBHelper.getRaw(SoeRegionNameCountryDO.class, sql);
        Map<String, String> map = ListUtils.toMap(region2Country, SoeRegionNameCountryDO::getRegionName, SoeRegionNameCountryDO::getCountryName);
        List<TxyRegionInfoDTO> allTxyRegionInfo = dictService.getAllTxyRegionInfo();

        List<SoeRegionInfo> ret = new ArrayList<>();

        for (TxyRegionInfoDTO dto : allTxyRegionInfo) {
            SoeRegionInfo soeRegionInfo = new SoeRegionInfo();
            soeRegionInfo.setRegionName(dto.getRegionName());
            soeRegionInfo.setAreaName(dto.getAreaName());
            soeRegionInfo.setZoneName(dto.getZoneName());
            soeRegionInfo.setCustomhouseTitle(dto.getCustomhouseTitle());
            soeRegionInfo.setCountryName(map.getOrDefault(dto.getRegionName(), "(空值)"));
            ret.add(soeRegionInfo);
        }
        return ListUtils.toMap(ret, SoeRegionInfo::getZoneName, o -> o);
    }

    @Data
    public static class dateEntity {
        Map<String, List<String>> fullDate;
        Map<String, List<String>> lastDate;
    }

}
