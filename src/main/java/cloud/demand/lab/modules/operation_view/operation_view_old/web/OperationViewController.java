package cloud.demand.lab.modules.operation_view.operation_view_old.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewReq;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewRsp;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.QueryFieldsRsp;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.string.StringTools;
import java.util.ArrayList;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 运营视图Controller
 */
@JsonrpcController("/operation-view")
public class OperationViewController {

    @Resource
    private DBHelper ckcldDBHelper;



    @RequestMapping
    public QueryFieldsRsp queryAllStatTimes(){
        String raw = "select distinct stat_time from report_operation_view_detail order by stat_time desc";
        return new QueryFieldsRsp(ckcldDBHelper.getRaw(String.class, raw));
    }


    @RequestMapping
    public QueryFieldsRsp queryDeviceType(@JsonrpcParam OperationViewReq req){
        checkQueryReq(req);
        String raw = "select distinct device_type from report_operation_view_detail where product_type = ?";
        return new QueryFieldsRsp(ckcldDBHelper.getRaw(String.class, raw, req.getProductType()));
    }

    @RequestMapping
    public QueryFieldsRsp queryDeviceFamily(@JsonrpcParam OperationViewReq req){
        checkQueryReq(req);
        String raw = "select distinct device_family from report_operation_view_detail where product_type = ?";
        return new QueryFieldsRsp(ckcldDBHelper.getRaw(String.class, raw, req.getProductType()));
    }

    @RequestMapping
    public QueryFieldsRsp queryCpuCategory(@JsonrpcParam OperationViewReq req){
        checkQueryReq(req);

        String raw = "select distinct cpu_category from report_operation_view_detail where product_type = ?";
        return new QueryFieldsRsp(ckcldDBHelper.getRaw(String.class, raw, req.getProductType()));
    }

    private void checkQueryReq(OperationViewReq req){
        if (req == null){
            throw new WrongWebParameterException("参数为空，请确认");
        }
        if (StringTools.isBlank(req.getProductType())) {
            throw new WrongWebParameterException("产品必须选择，例如CVM/GPU/裸金属等");
        }
    }

    @RequestMapping
    public QueryFieldsRsp queryRegionalFields(@JsonrpcParam OperationViewReq req){
        checkQueryReq(req);
        String raw = "select customhouse_title customhouseTitle," +
                " area_name areaName, region_name regionName, zone_name zoneName\n" +
                "from report_operation_view_detail\n" +
                "where product_type = ?\n" +
                "group by customhouseTitle, areaName, regionName, zoneName";
        QueryFieldsRsp queryFieldsRsp = new QueryFieldsRsp();
        queryFieldsRsp.setRegionalData(ckcldDBHelper.getRaw(QueryFieldsRsp.RegionalFieldDTO.class,
                raw, req.getProductType()));
        return queryFieldsRsp;
    }



}
