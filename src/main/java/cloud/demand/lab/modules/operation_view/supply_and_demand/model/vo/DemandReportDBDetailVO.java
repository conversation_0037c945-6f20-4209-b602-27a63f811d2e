package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/12 22:43
 */
@Data
public class DemandReportDBDetailVO extends DemandReportDetailVO {
    private BigDecimal waitBuyMemory = BigDecimal.ZERO;
    private BigDecimal buyMemory = BigDecimal.ZERO;

    private BigDecimal totalMemory = BigDecimal.ZERO;

    private BigDecimal waitBuyStorage = BigDecimal.ZERO;
    private BigDecimal buyStorage = BigDecimal.ZERO;

    private BigDecimal totalStorage = BigDecimal.ZERO;

    public static DemandReportDBDetailVO transform(DemandReportDetailVO vo,String unit) {
        DemandReportDBDetailVO ret = new DemandReportDBDetailVO();
        BeanUtils.copyProperties(vo, ret);
        if(StringUtils.equals(UnitEnum.STORAGE.getName(),unit)){
            ret.setWaitBuyStorage(vo.getWaitBuyAmount());
            ret.setBuyStorage(vo.getBuyAmount());
            ret.setTotalStorage(vo.getTotalAmount());
        }
        if(StringUtils.equals(UnitEnum.MEMORY.getName(),unit)){
            ret.setWaitBuyMemory(vo.getWaitBuyAmount());
            ret.setBuyMemory(vo.getBuyAmount());
            ret.setTotalMemory(vo.getTotalAmount());
        }
        return ret;
    }
}
