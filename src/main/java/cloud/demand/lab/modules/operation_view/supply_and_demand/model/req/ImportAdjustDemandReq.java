package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/4/24 15:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportAdjustDemandReq {
    @NotBlank(message = "版本时间不能为空")
    private String versionCode;

    private String productCategory = "CVM";
}
