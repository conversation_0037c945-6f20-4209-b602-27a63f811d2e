package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class DeleteChangeResultReq {

    @NotEmpty(message = "删除id不能为空")
    private List<Long> ids;

}
