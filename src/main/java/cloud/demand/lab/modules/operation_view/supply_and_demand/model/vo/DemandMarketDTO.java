package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;

@Data
public class DemandMarketDTO {

    @Column("DAY")
    private LocalDate statTime;

    @Column("cloud_business_type")
    private String cloudBusinessType;

    @Column("quota_id")
    private String quotaId;

    @Column("quota_plan_product_name")
    private String quotaPlanProductName;

    @Column("quota_device_class")
    private String quotaDeviceClass;

    @Column("quota_campus_name")
    private String quotaCampusName;

    @Column("quota_region_name")
    private String quotaRegionName;

    @Column("campus")
    private String campus;

    @Column("module")
    private String module;

    @Column("quota_use_time")
    private String quotaUseTime;

    @Column("sla_date_expect")
    private String slaDateExpect;

    @Column("est_arrive_date")
    private String estArriveDate;

    @Column("quota_create_time")
    private String quotaCreateTime;

    @Column("produce_status")
    private String produceStatus;

    @Column("is_delivery")
    private String isDelivery;

    @Column("xy_industry")
    private String xyIndustry;

    @Column("xy_customer_name")
    private String xyCustomerName;

    @Column("pos_pre_start_datetime")
    private String posPreStartDateTime;

    @Column("is_fake_position")
    private String isFakePosition;

    @Column("delivery_status")
    private String deliveryStatus;

    @Column("cpu_logic_core")
    private BigDecimal cpuLogicCore;

    @Column("num")
    private BigDecimal num;

    @Column("total_core")
    private BigDecimal totalCore;

    @Column("gpu_num")
    private BigDecimal gpuNum;

    @Column("cloud_delivery_time")
    private String cloudDeliveryTime;

    @Column("proj_set_name")
    private String projectName;
}
