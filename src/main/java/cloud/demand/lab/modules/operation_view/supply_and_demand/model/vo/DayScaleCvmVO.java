package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:43
 */
@Data
public class DayScaleCvmVO {

    @Column("stat_time")
    private String statTime;

    @Column("year_month")
    private String yearMonth;

    @Column("customhouse_title")
    private String customhouseTitle;//境内外

    @Column("country_name")
    private String countryName;//国家

    @Column("area_name")
    private String areaName;//大区

    @Column("region_name")
    private String regionName;//地域

    @Column("zone_category")
    private String zoneCategory;//可用区类别

    @Column("zone_name")
    private String zoneName;//可用区

    @Column("volume_type")
    private String volumeType;//存储类型

    @Column("amount")
    private String amount;//规模

}
