package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.CkDBUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.PplAndOrderAdjustVersionDataDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataAdjustDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.PplWaveProjectTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.AdjustDemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandTrendReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.PplOrderDemandTrendData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductAdjustDemandVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.order.DynamicProperties;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/11 16:29
 */
@Service
@Slf4j
public class DemandServiceImpl implements DemandService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DictService dictService;

    @Resource
    private ScaleStrategy scaleStrategy;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<DemandReportDetailVO> queryReport(DemandReportReq req) {
        List<DemandReportDetailVO> result = ListUtils.newArrayList();
        // 查询需求
        for (String statTime : req.getStatTimeList()) {
            DemandReq demandReq = DemandReq.transform(req, statTime);
            result.addAll(queryDemand(demandReq));
        }
        // 规模
        if (req.isNeedScale()) {
            List<String> notOtherUin = ListUtils.newArrayList();
            List<String> notOtherName = ListUtils.newArrayList();
            if (ListUtils.contains(req.getDims(), item -> StringUtils.equals("customerUin", item))) {
                notOtherUin = result.stream().map(DemandReportDetailVO::getCustomerUin).distinct().collect(Collectors.toList());
                notOtherUin.removeIf(item -> StringUtils.equals(Constant.EMPTY_STR, item));
            }
            if ((ListUtils.contains(req.getDims(), item -> StringUtils.equals("commonCustomerShortName", item)))) {
                notOtherName = result.stream().map(DemandReportDetailVO::getCommonCustomerShortName).distinct().collect(Collectors.toList());
            }
            result.addAll(scaleStrategy.queryChangeScaleDataFromLastMonth(req, notOtherUin, notOtherName));
        }
        return result;
    }

    @Override
    public List<PplOrderDemandTrendData> queryTrend(DemandTrendReq req) {
        List<PplOrderDemandTrendData> ret = ListUtils.newArrayList();
        List<String> statTimeList = ListUtils.newArrayList(req.getStartStatTime(), req.getEndStatTime());
        for (String statTime : statTimeList) {
            //未来需求
            DemandReq pplOrderDemandReq = DemandReq.transform(req, statTime);
            List<DemandReportDetailVO> demandList = queryDemand(pplOrderDemandReq);
            ret.addAll(ListUtils.transform(demandList, item -> PplOrderDemandTrendData.transform(item, statTime, true)));
            //过去规模
            List<DemandReportDetailVO> scaleList = scaleStrategy.queryChangeScaleDataFromLastMonth(req);
            ret.addAll(ListUtils.transform(scaleList, item -> PplOrderDemandTrendData.transform(item, statTime, false)));
        }
        return ret;
    }

    @Override
    public List<String> queryParams(SupplyDemandParamTypeReq req) {
        if (StringUtils.isBlank(req.getParamType())) {
            return new ArrayList<>();
        }

        String column = ORMUtils.getColumnByFieldName(ProductDemandDataDfDO.class, req.getParamType());
        if (column == null) {
            return new ArrayList<>();
        }
        String sql = "select distinct ${paramType} from std_crp.ppl_and_order_demand_data_df where product_category = ? ";

        return ckcldStdCrpDBHelper.getRaw(String.class, sql.replace("${paramType}", column), req.getProductCategory());

    }

    @Override
    public List<DemandReportDetailVO> queryDemand(DemandReq req) {
        List<DemandReportDetailVO> retList = ListUtils.newArrayList();
        List<ProductDemandDataAdjustDO> dbList = doQueryDemand(req);
        if (ListUtils.isEmpty(dbList)) {
            return retList;
        }
        if (StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CBS.getName()) && ListUtils.isNotEmpty(req.getProjectType())) {
            List<String> projectType = dbList.stream().map(item -> item.getProjectType()).filter(item -> !StringUtils.equals(item, Constant.EMPTY_STR)).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(projectType)) {
                //PPL 项目类型过滤,订单数据不进行过滤
                dbList = dbList.stream().filter(item -> StringUtils.equals(item.getDataSource(), "订单")
                                || (!StringUtils.equals(item.getDataSource(), "订单") && ListUtils.contains(req.getProjectType(), o -> StringUtils.equals(o, item.getProjectType()))))
                        .collect(Collectors.toList());
            }
        }

        Map<String, BigDecimal> memoryMap = demandDBHelper.getAll(BasDeviceMemoryDO.class).stream().collect(Collectors.toMap(BasDeviceMemoryDO::getPlanProductName, BasDeviceMemoryDO::getGbSaleMemory, (k1, k2) -> k1));
        dbList.forEach(item -> {
            String dataType = StringUtils.equals(item.getDataSource(), "订单") ? "订单" : "PPL";
            item.setDataType(dataType);
            item.setBillNumber(item.getBillNumber());
            item.fillDbNum(memoryMap);
        });
        List<String> dims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "yearMonth", "dataSource", "dataType"));

        List<String> sumFields = ListUtils.newArrayList("instanceNum", "totalCore", "buyTotalCore", "waitBuyTotalCore", "diskNum", "dbNum", "diskStorage", "buyDiskStorage", "waitBuyDiskStorage",
                "dbMemory", "buyDbMemory", "waitBuyDbMemory");
        ListUtils.groupBy(dbList, item -> GroupUtils.getDimsGroupKey(item, dims))
                .forEach((k, v) -> {
                    Map<String, List<ProductDemandDataAdjustDO>> map = ListUtils.groupBy(v, ProductDemandDataAdjustDO::getMonthType);
                    ProductDemandDataAdjustDO current = GroupUtils.mergeList(map.get(Constant.CURRENT_MONTH), dims, sumFields);
                    ProductDemandDataAdjustDO cross = GroupUtils.mergeList(map.get(Constant.CROSS_MONTH), dims, sumFields);
                    String dataSource = Objects.isNull(current) ? cross.getDataSource() : current.getDataSource();
                    Function<ProductDemandDataDfDO, BigDecimal> totalAmountGetter = ProductDemandDataDfDO.totalAmountTargetGetter(dataSource, req.getOrderRange(), req.getUnit(), req.getProductCategory());
                    Function<ProductDemandDataDfDO, BigDecimal> buyAmountGetter = ProductDemandDataDfDO.waitOrBuyAmountTargetGetter(Constant.ORDER_RANGE_BUY, req.getUnit(), req.getProductCategory());
                    Function<ProductDemandDataDfDO, BigDecimal> waitBuyAmountGetter = ProductDemandDataDfDO.waitOrBuyAmountTargetGetter(Constant.ORDER_RANGE_WAIT_BUY, req.getUnit(), req.getProductCategory());
                    Function<ProductDemandDataDfDO, BigDecimal> numGetter = ProductDemandDataDfDO.numTargetGetter(req.getProductCategory());
                    DemandReportDetailVO vo = DemandReportDetailVO.builder(req.getUnit(), current, cross, totalAmountGetter, waitBuyAmountGetter, buyAmountGetter, numGetter);
                    if (BigDecimal.ZERO.compareTo(vo.getTotalAmount()) == 0) {
                        return;
                    }
                    retList.add(vo);
                });
        for (DemandReportDetailVO item : retList) {
            if (req.isAdjust()) {
                item.setIntervene("是");
            }
        }
        return retList;
    }


    @Override
    public void saveAdjustDemand(List<ProductDemandDataAdjustDO> dataList, String statTime, String productCategory) {
        Map<String, String> regionMap = dictService.getRegionNameMap();
        Map<String, String> zoneMap = dictService.getZoneNameMap();
        for (ProductDemandDataAdjustDO item : dataList) {
            item.setProductCategory(productCategory);
            item.setRegion(regionMap.getOrDefault(item.getRegionName(), Constant.EMPTY_STR));
            item.setZone(zoneMap.getOrDefault(item.getZoneName(), Constant.EMPTY_STR));
            item.setDemandCaliber("人工校准");
        }

        List<ProductDemandDataAdjustDO> all = ckcldStdCrpDBHelper.getAll(ProductDemandDataAdjustDO.class, " where stat_time = ? ", statTime);
        all.removeIf(item -> Strings.equals(item.getProductCategory(), productCategory));
        all.addAll(dataList);
        // 删除分区
        CkDBUtils.delete(ckcldStdCrpDBHelper, statTime, ProductDemandDataAdjustDO.class);
        // 添加
        log.info(" size :" + dataList.size());
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, all);
    }


    private List<ProductDemandDataAdjustDO> doQueryDemand(DemandReq req) {
        boolean isAdjust = req.isAdjust() || StringUtils.equals(req.getDemandCaliber(), "人工校准");
        req.setAdjust(isAdjust);
        boolean abTest = DynamicProperties.allowABTest();
        if (abTest && req.isAdjust() && StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CVM.getName())) {
            AdjustDemandReq adjustDemandReq = AdjustDemandReq.transform(req);
            WhereBuilder whereBuilder = new WhereBuilder(adjustDemandReq, PplAndOrderAdjustVersionDataDO.class);
            WhereSQL whereSQL = whereBuilder.whereSQL();
            whereSQL.and(" deleted = 0 ");
            String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_demand_adjust_by_stat_time.sql");
            sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());
            //如果是查CVM校准数据
            List<ProductAdjustDemandVO> data = demandDBHelper.getRaw(ProductAdjustDemandVO.class, sql, whereSQL.getParams());
            return ListUtils.transform(data, ProductAdjustDemandVO::transform);
        }
        if (isAdjust) {
            req.setDemandCaliber("人工校准");
            //人工校准数据不存在，则不查校准数据
            long count = ckcldStdCrpDBHelper.getCount(ProductDemandDataAdjustDO.class, " where stat_time = ? and product_category = ? ", req.getStatTime(), req.getProductCategory());
            if (count <= 0) {
                req.setAdjust(false);
                req.setDemandCaliber("共识需求");
            }
        }

        WhereBuilder whereBuilder = new WhereBuilder(req, ProductDemandDataDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        if (req.isExcludeFinishDemand()) {
            whereSQL.and(" end_buy_date >= ? or end_buy_date = '(空值)'", req.getStatTime());
        }
        if (StringUtils.equals(UnitEnum.STORAGE.getName(), req.getUnit())) {
            whereSQL.and(" disk_storage != 0 ");
        } else if (StringUtils.equals(UnitEnum.CORE.getName(), req.getUnit())) {
            whereSQL.and(" total_core != 0 ");
        } else {
            whereSQL.and(" db_memory != 0 ");
        }

        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_demand_df_by_stat_time.sql");

        String tableName = req.isAdjust() ? "std_crp.ppl_and_order_demand_data_adjust" : "std_crp.ppl_and_order_demand_data_df";
        sql = SimpleSqlBuilder.doReplace(sql, "table_name", tableName);
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());
        sql = SimpleSqlBuilder.doReplace(sql, "adjustExcel", isAdjust & req.isExcel() ? "1" : "0");

        return ckcldStdCrpDBHelper.getRaw(ProductDemandDataAdjustDO.class, sql, whereSQL.getParams());
    }
}
