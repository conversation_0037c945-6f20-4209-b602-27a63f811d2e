package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasVersionDO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @since 2025/3/14 13:23
 */
@Data
public class VersionData {
    private String versionCode;

    private boolean definitive = false;

    private String startMonth;

    private String endMonth;

    private String productCategory;


    public static VersionData transform(BasVersionDO versionDO)  {
        VersionData versionData = new VersionData();
        BeanUtils.copyProperties(versionDO, versionData);
        return versionData;
    }
}
