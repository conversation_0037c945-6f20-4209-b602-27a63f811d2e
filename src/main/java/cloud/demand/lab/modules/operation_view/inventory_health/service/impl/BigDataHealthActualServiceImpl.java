package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandAuthDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.EKSDataHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.EKSDataHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.IndustryDemandProductEnumDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.BigDataTypeEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.BigDataHealthActualService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.OutsideHealthService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsSoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BigDataHealthActualServiceImpl implements BigDataHealthActualService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private OutsideViewOldService outsideViewOldService;

    @Resource
    DictService dictService;

    @Resource
    CvmPlanService cvmPlanService;



    @Override
    public List<BigDataHealthActualData> queryBigDataHealthActualReport(BigDataHealthActualReq req) {
        WhereSQL condition = req.genBasicCondition();
        //获取api成功率
        log.info("method:queryBigDataHealthActualReport 开始获取api成功率");
        List<DwsApiSuccessDataDfLocalDO> apiAll = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        log.info("method:queryBigDataHealthActualReport 获取api成功率成功");
        log.info("method:queryBigDataHealthActualReport 开始获取CvmType");
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        log.info("method:queryBigDataHealthActualReport 获取api成功率成功");
        Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);

        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            apiAll = apiAll.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceFamily()))).collect(Collectors.toList());
        }
        //针对成功率进行聚合计算操作
        List<BigDataHealthActualData> result = new ArrayList<>();
        Map<String, List<DwsApiSuccessDataDfLocalDO>> apiMapList = ListUtils.toMapList(apiAll,
                o -> String.join("@", o.getStatTime(), o.getInstanceFamily(), o.getZoneName()), o -> o);

        log.info("method:queryBigDataHealthActualReport 开始可用区类型与机型类型获取");
        //可用区类型与机型类型获取
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        log.info("method:queryBigDataHealthActualReport 可用区类型与机型类型获取完成");
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap = ListUtils.toMap(zoneConfigList, o -> o.getZoneName(),
                o -> o);
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> insConfigMap = ListUtils.toMapList(insConfigList,
                o -> o.getInstanceType(), o -> o);
        log.info("method:queryBigDataHealthActualReport 开始指标计算");
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : apiMapList.entrySet()) {
            List<DwsApiSuccessDataDfLocalDO> apiData = entry.getValue();
            BigDataHealthActualData item = new BigDataHealthActualData();
            BigDecimal apiSuc = BigDecimal.ZERO;
            BigDecimal apiTotal = BigDecimal.ZERO;
            if (ListUtils.isNotEmpty(apiData)) {
                apiSuc = NumberUtils.sum(apiData, DwsApiSuccessDataDfLocalDO::getSuccNum);
                apiTotal = NumberUtils.sum(apiData, DwsApiSuccessDataDfLocalDO::getTotalNum);
            }
            if (apiTotal.intValue() > 0) {
                 item.setApiSucRate(apiSuc.divide(apiTotal, 4, RoundingMode.UP));
            }
            item.setApiSucTotal(apiSuc);
            item.setApiTotal(apiTotal);
            item.setStatTime(apiData.get(0).getStatTime());
            item.setInstanceType(apiData.get(0).getInstanceFamily());
            item.setAreaName(apiData.get(0).getAreaName());
            item.setRegionName(apiData.get(0).getRegionName());
            item.setZoneName(apiData.get(0).getZoneName());
            item.setCustomhouseTitle(apiData.get(0).getCustomhouseTitle());
            item.setProduct(apiData.get(0).getProductType());
            if (item.getProduct().equals(BigDataTypeEnum.EMR.getCode())) {
                item.setGinsFamily(ginMap.get(item.getInstanceType()));
            }
            InventoryHealthMainZoneNameConfigDO zoneConfig = zoneConfigMap.get(
                    item.getZoneName());
            if (zoneConfig != null) {
                item.setZoneCategory(zoneConfig.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            if (item.getProduct().equals(BigDataTypeEnum.EMR.getCode())) {
                List<InventoryHealthMainInstanceTypeConfigDO> insConfig = insConfigMap.get(
                        item.getInstanceType());
                if (ListUtils.isNotEmpty(insConfig)) {
                    String customhouse = item.getCustomhouseTitle();
                    if (item.getCustomhouseTitle().equals("境外")) {
                        List<String> collect = insConfig.stream().map(
                                InventoryHealthMainInstanceTypeConfigDO::getRegionType).collect(Collectors.toList());
                        if (collect.contains("海外")) {
                            customhouse = "海外";
                        }
                    }
                    Map<String, InventoryHealthMainInstanceTypeConfigDO> map = ListUtils.toMap(insConfig,
                            o -> o.getRegionType(), o -> o);
                    InventoryHealthMainInstanceTypeConfigDO temp = map.get(customhouse);
                    if (temp != null) {
                        item.setInstanceCategory(temp.getType2Name());
                    }else {
                        item.setInstanceCategory("未分类");
                    }
                }else {
                    item.setInstanceCategory("未分类");
                }
            }
            result.add(item);
        }
        log.info("method:queryBigDataHealthActualReport 指标计算完成");
        return result;

    }

    @Override
    public List<EKSDataHealthActualData> queryEKSDataHealthTrendReport(EKSDataHealthActualReq req) {

        //处理时间,将天与对应的时间范围联系起来
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }

        //查询eks的api成功率数据
        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsApiSuccessDataDfLocalDO>> apiMap = ListUtils.toMapList(all,
                o -> String.join("@", o.getZoneName(), finalDateMap.get(o.getStatTime())), o -> o);
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap = ListUtils.toMap(configDOList, o -> o.getZoneName(),
                o -> o);

        //生成返回数据
        List<EKSDataHealthActualData> result = new ArrayList<>();
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : apiMap.entrySet()) {
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            EKSDataHealthActualData item = new EKSDataHealthActualData();
            item.setDate(dateMap.get(value.get(0).getStatTime()));
            item.setProduct("EKS");
            item.setZoneName(value.get(0).getZoneName());
            item.setAreaName(value.get(0).getAreaName());
            item.setRegionName(value.get(0).getRegionName());
            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            if (item.getApiTotal().intValue() > 0) {
                item.setApiSucRate(item.getApiSucTotal().divide(item.getApiTotal(), 4, RoundingMode.UP));
            }
            InventoryHealthMainZoneNameConfigDO zoneConfig = zoneConfigMap.get(
                    item.getZoneName());
            if (zoneConfig != null) {
                item.setZoneCategory(zoneConfig.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            result.add(item);
        }
        //根据国家过滤
        if (ListUtils.isNotEmpty(req.getCountry())) {
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
            Map<String, String> ret = new HashMap<>();
            for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
                String zone = entry.getKey();
                String region = entry.getValue();
                SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
                if (regionDO != null) {
                    ret.put(zone, regionDO.getCountryName());
                }
            }
            result = result.stream().filter(o -> {
                String country = ret.get(o.getZoneName());
                if (country == null) {
                    if (o.getCustomhouseTitle().equals("境内")) {
                        country = "中国内地";
                    }
                }
                if (country != null) {
                    return req.getCountry().contains(country);
                }
                return false;
            }).collect(
                    Collectors.toList());
        }
        return result;
    }

    private Map<String, String> getDayMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(DateUtils.formatDate(startDate), DateUtils.formatDate(startDate));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    private Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    private Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    @Override
    public List<String> queryBigDataInstance(String productType) {
        String sql = "select distinct instance_family from dwd_api_success_data_df where product_type = ? and stat_time > '2024-11-30'";
        return ckcldDBHelper.getRaw(String.class, sql, productType);
    }

    @Override
    public List<String> getBigDataProductPermission(String user) {

        List<IndustryDemandAuthDO> raw = demandDBHelper.getRaw(IndustryDemandAuthDO.class,
                "select * from industry_demand_auth where user_name = ? and role = 'ADMIN' and deleted = 0", user);
        if (ListUtils.isNotEmpty(raw)) {
            //管理员默认全部产品权限
            return BigDataTypeEnum.getAllBigDataType();
        }
        IndustryDemandAuthDO auth = demandDBHelper.getOne(IndustryDemandAuthDO.class,
                "where user_name = ? and role = ?", user,
                IndustryDemandAuthRoleEnum.PRODUCT_DATA_FOLLOWER.getCode());

        List<String> product = new ArrayList<>();
        if (auth != null) {
            String temp = auth.getProduct();
            if (StringUtils.isNotBlank(temp)) {
                String[] split = temp.split(";");
                for (String str : split) {
                    String codeByAuthName = BigDataTypeEnum.getCodeByAuthName(str);
                    if (StringUtils.isNotBlank(codeByAuthName)) {
                        product.add(codeByAuthName);
                    }
                }
            }
        }
        return product;
    }

    @Override
    public List<BigDataHealthTrendData> queryBigDataHealthTrendReport(BigDataHealthTrendReq req) {

        //处理时间,将天与对应的时间范围联系起来
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }
        //获取数据
        WhereSQL condition = req.genBasicCondition();
        List<DwsApiSuccessDataDfLocalDO> apiAll = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                condition.getSQL(), condition.getParams());
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);

        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            apiAll = apiAll.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceFamily()))).collect(Collectors.toList());
        }
        //可用区类型与机型类型获取
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap = ListUtils.toMap(zoneConfigList, o -> o.getZoneName(),
                o -> o);
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> insConfigMap = ListUtils.toMapList(insConfigList,
                o -> o.getInstanceType(), o -> o);

        //将数据聚合
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsApiSuccessDataDfLocalDO>> apiMap = ListUtils.toMapList(apiAll,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName(), o.getInstanceFamily()),
                o -> o);
        List<BigDataHealthTrendData> result = new ArrayList<>();
        for (Entry<String, List<DwsApiSuccessDataDfLocalDO>> entry : apiMap.entrySet()) {
            BigDataHealthTrendData item = new BigDataHealthTrendData();
            List<DwsApiSuccessDataDfLocalDO> value = entry.getValue();
            item.setDate(dateMap.get(value.get(0).getStatTime()));
            item.setInstanceType(value.get(0).getInstanceFamily());
            item.setProduct(value.get(0).getProductType());
            item.setZoneName(value.get(0).getZoneName());
            item.setRegionName(value.get(0).getRegionName());
            item.setAreaName(value.get(0).getAreaName());
            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            item.setApiSucTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getSuccNum));
            item.setApiTotal(NumberUtils.sum(value, DwsApiSuccessDataDfLocalDO::getTotalNum));
            if (item.getApiTotal().intValue() > 0) {
                item.setApiSucRate(item.getApiSucTotal().divide(item.getApiTotal(), 4, RoundingMode.UP));;
            }
            if (item.getProduct().equals(BigDataTypeEnum.EMR.getCode())) {
                item.setGinsFamily(ginMap.get(item.getInstanceType()));
            }
            InventoryHealthMainZoneNameConfigDO zoneConfig = zoneConfigMap.get(
                    item.getZoneName());
            if (zoneConfig != null) {
                item.setZoneCategory(zoneConfig.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            if (item.getProduct().equals(BigDataTypeEnum.EMR.getCode())) {
                List<InventoryHealthMainInstanceTypeConfigDO> insConfig = insConfigMap.get(
                        item.getInstanceType());
                if (ListUtils.isNotEmpty(insConfig)) {
                    String customhouse = item.getCustomhouseTitle();
                    if (item.getCustomhouseTitle().equals("境外")) {
                        List<String> collect = insConfig.stream().map(
                                InventoryHealthMainInstanceTypeConfigDO::getRegionType).collect(Collectors.toList());
                        if (collect.contains("海外")) {
                            customhouse = "海外";
                        }
                    }
                    Map<String, InventoryHealthMainInstanceTypeConfigDO> map = ListUtils.toMap(insConfig,
                            o -> o.getRegionType(), o -> o);
                    InventoryHealthMainInstanceTypeConfigDO temp = map.get(customhouse);
                    if (temp != null) {
                        item.setInstanceCategory(temp.getType2Name());
                    }else {
                        item.setInstanceCategory("未分类");
                    }
                }else {
                    item.setInstanceCategory("未分类");
                }
            }
            result.add(item);
        }
        //根据国家过滤
        if (ListUtils.isNotEmpty(req.getCountry())) {
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
            Map<String, String> ret = new HashMap<>();
            for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
                String zone = entry.getKey();
                String region = entry.getValue();
                SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
                if (regionDO != null) {
                    ret.put(zone, regionDO.getCountryName());
                }
            }
            result = result.stream().filter(o -> {
                String country = ret.get(o.getZoneName());
                if (country == null) {
                    if (o.getCustomhouseTitle().equals("境内")) {
                        country = "中国内地";
                    }
                }
                if (country != null) {
                    return req.getCountry().contains(country);
                }
                return false;
            }).collect(
                    Collectors.toList());
        }

         return result;
    }

}
