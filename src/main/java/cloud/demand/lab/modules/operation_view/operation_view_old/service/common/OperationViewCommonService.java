package cloud.demand.lab.modules.operation_view.operation_view_old.service.common;

import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.model.PlanDetailVO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model.ChCvmSaleDTO;
import io.vavr.Tuple2;
import java.util.Map;

public interface OperationViewCommonService {

    /**
     * 填充物理机其他需要关联的信息
     */
    void fillOtherFields(ReportOperationViewDetailDO o, String statTime);

    /**
     * 根据当前时间推导出前n个月的起止时间
     * example: statTime = 2022-12-08 ;
     *          n = 12
     *               返回结果Tuple<>("2021-12-31", "2022-11-30")
     */
    Tuple2<String, String> getTimeIntervalByMonth(String statTime, int n);


    /**
     * 根据当前时间推导出前n周的起止时间
     * example: statTime = 2022-12-08 ;
     *          n = 1
     *          返回结果Tuple<>("2022-11-28"(), "2022-12-04")
     */
    Tuple2<String, String> getTimeIntervalByWeek(String statTime, int n);

    /**
     * 判断当前月份是m减n，n实际的值
     * 例子：statTime为2022-12-xx，Map的年月{"2022-11":1, "2022-10":2, "2022-9":3, ...}
     */
    Map<String, Integer> getMReduceNMonthMap(String statTime);

    /**
     * 设置第m-n月的库存总量
     * 销售也可以在此赋值，但CVM的销售底表不是plan_detail，相关逻辑在另一个方法setHistorySaleByI中
     * @param i n
     * @param vo
     * @param one
     * @param productType
     */
    void setHistorySaleByI(int i, PlanDetailVO vo, ReportOperationViewDetailDO one, String productType);

    /**
     * 设置第m-n月的库存总量
     * 销售也可以在此赋值，但CVM的销售底表不是plan_detail，相关逻辑在另一个方法setHistorySaleByI中
     * @param i n
     * @param vo
     * @param one
     * @param productType
     */
    void setHistoryInvByI(int i, PlanDetailVO vo, ReportOperationViewDetailDO one, String productType);

    /**
     * 设置第m-n月的售卖总量
     * @param i n
     * @param dto
     * @param one
     */
    void setHistorySaleByI(int i, ChCvmSaleDTO dto, ReportOperationViewDetailDO one);

    /**
     * 设置第m-n周的售卖量
     * @param i n
     * @param dto
     * @param one
     */
    void setHistoryDemandByI(int i, ChCvmSaleDTO dto, ReportOperationViewDetailDO one);

    /**
     * 设置第m-n周的售卖量（其中数据源为plan_detail表）
     * @param i n
     * @param dto
     * @param one
     * @param productType
     */
    void setHistoryDemandByI(int i, PlanDetailVO dto, ReportOperationViewDetailDO one, String productType);


    /**
     * 根据当前时间推导出前n个月,计算每个月有多少天
     * Map结构{"2022-5" : 31 , "2022-6" : 30 ...}
     */
    Map<String, Integer> getMonthNumOfDays(String statTime, int n);
}
