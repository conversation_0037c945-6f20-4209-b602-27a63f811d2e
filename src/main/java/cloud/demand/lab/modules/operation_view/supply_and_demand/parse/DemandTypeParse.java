package cloud.demand.lab.modules.operation_view.supply_and_demand.parse;


import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.DemandTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.VolumeTypeEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import report.utils.query.IWhereParser;
import report.utils.query.WhereBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14 14:19
 */
public class DemandTypeParse implements IWhereParser {
    @Override
    public void parseSQL(WhereSQL content, WhereBuilder.WhereInfo whereInfo, Object t) {
        Object v = whereInfo.getV();
        List<String> ls = (List<String>) v;
        if (ListUtils.isNotEmpty(ls)) {
            String demandTypeColumn = whereInfo.getParseParams()[0]; //
            List<String> demandType = ListUtils.newArrayList();
            for (String item : ls) {
                demandType.add(DemandTypeEnum.getNameByCode(item));
            }
            content.andIf(ListUtils.isNotEmpty(demandType), demandTypeColumn + " in (?)", demandType);
        }
    }
}
