package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:43
 */
@Data
public class DayScaleCbsVO {

    @Column("stat_time")
    private String statTime;

    @Column("year_month")
    private String yearMonth;

    @Column("product_category")
    private String productCategory;// 产品类别

    @Column("product")
    private String product;//产品

    @Column("industry_dept")
    private String industryDept;//行业部门

    @Column("war_zone")
    private String warZone;//战区

    @Column("common_customer_short_name")
    private String commonCustomerShortName;//通用客户简称

    @Column("customer_uin")
    private String customerUin;//客户uin

    @Column("customer_short_name")
    private String customerShortName;//客户简称

    @Column("customhouse_title")
    private String customhouseTitle;//境内外

    @Column("country_name")
    private String countryName;//国家

    @Column("area_name")
    private String areaName;//大区

    @Column("region_name")
    private String regionName;//地域

    @Column("zone_category")
    private String zoneCategory;//可用区类别

    @Column("zone_name")
    private String zoneName;//可用区

    @Column("instance_category")
    private String instanceCategory;//实例类别

    @Column("instance_group")
    private String instanceGroup;//实例族

    @Column("instance_type")
    private String instanceType;//实例类型

    @Column("amount")
    private String amount;//规模

}
