package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.DwdDbSaleScaleDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DayCbsScaleReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DayDbScaleReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandCommonReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DayCbsScaleVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DayDbScaleVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ScaleService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:37
 */
@Service
public class ScaleDbServiceImpl implements ScaleService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.DB.getName();
    }

    @Override
    public List<DemandReportDetailVO> queryCurScaleData(DemandCommonReq commonReq) {
        DayDbScaleReq req = DayDbScaleReq.transform(commonReq);
        List<DayDbScaleVO> dbList = getDayDbScale(req);
        return ListUtils.transform(dbList, item -> DemandReportDetailVO.transform(item));
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req) {
        return queryChangeScaleDataFromLastMonth(req, null, null);
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq commonReq, List<String> notOtherCustomerUin, List<String> notOtherCommonCustomerShortName) {
        DayDbScaleReq req = DayDbScaleReq.transform(commonReq);
        List<String> allStatTime = addLastMonthStatTime(req.getStatTime());
        req.setOriginalStatTime(req.getStatTime());
        req.setStatTime(allStatTime);

        List<DayDbScaleVO> dbList = getDayDbScale(req);

        Function<DayDbScaleVO, String> keyFunc = item -> StringUtils.joinWith("@", item.getCustomhouseTitle(), item.getCountryName(),
                item.getAreaName(), item.getRegionName(), item.getZoneCategory(), item.getZoneName(), item.getProduct());
        Map<String, DayDbScaleVO> dbMap = dbList.stream().collect(Collectors.toMap(item -> StringUtils.joinWith("@", item.getStatTime(), item.getYearMonth(),
                keyFunc.apply(item)), Function.identity(), (k1, k2) -> k1));

        List<DemandReportDetailVO> retList = ListUtils.newArrayList();
        for (DayDbScaleVO item : dbList) {
            if (!req.getOriginalStatTime().contains(item.getStatTime())) {
                continue;
            }
            String lastMonthStatTime = LocalDate.parse(item.getStatTime()).minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE);
            String lastMonth = lastMonthStatTime.substring(0, 7);
            String key = StringUtils.joinWith("@", lastMonthStatTime, lastMonth, keyFunc.apply(item));
            DayDbScaleVO lastMonthScale = dbMap.get(key);
            retList.add(DemandReportDetailVO.transform(item, lastMonthScale));
        }
        return retList;
    }

    private List<DayDbScaleVO> getDayDbScale(DayDbScaleReq req) {

        WhereBuilder whereBuilder = new WhereBuilder(req, DwdDbSaleScaleDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();

        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_day_db_scale.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());

        List<DayDbScaleVO> dbList = ckcldDBHelper.getRaw(DayDbScaleVO.class, sql, whereSQL.getParams());
        if(StringUtils.equals(req.getUnit(), UnitEnum.NUM.getName())){
            Map<String, BigDecimal> memoryMap = demandDBHelper.getAll(BasDeviceMemoryDO.class)
                    .stream().collect(Collectors.toMap(BasDeviceMemoryDO::getPlanProductName, BasDeviceMemoryDO::getTbSaleMemory, (k1, k2) -> k1));
            for(DayDbScaleVO item : dbList){
                BigDecimal singleMemory = memoryMap.getOrDefault(item.getProduct(),BigDecimal.ZERO);
                if(BigDecimal.ZERO.compareTo(singleMemory) == 0){
                    item.setCurAmount(BigDecimal.ZERO);
                }else {
                    item.setCurAmount(SoeCommonUtils.divide(item.getCurAmount(), singleMemory));
                }
            }
        }
        return dbList;
    }

}
