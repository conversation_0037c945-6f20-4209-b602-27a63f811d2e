package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SafeInventoryCalcVO {

        //当前实际总库存:A
        private BigDecimal actualInventory;

        //实际周转库存:B
        private BigDecimal actualTurnoverInventory;

        //实际安全库存D = min(A-B,C)
        private BigDecimal actualSafeInventory;

        //安全库存目标值:C
        private BigDecimal safeInventory;

        public SafeInventoryCalcVO(BigDecimal actualInventory, BigDecimal actualTurnoverInventory, BigDecimal safeInventory){
            this.actualInventory = actualInventory;
            this.actualTurnoverInventory = actualTurnoverInventory;
            this.safeInventory = safeInventory;
            this.actualSafeInventory = BigDecimal.valueOf(Math.min(SoeCommonUtils.sub(actualInventory,actualTurnoverInventory).doubleValue(), safeInventory.doubleValue()));
        }
    }