package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.metal.impl;

import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.metal.GenMetalDetailService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.metal.model.MetalPlanDetailVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GenMetalDetailServiceImpl implements GenMetalDetailService {

    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private OperationViewCommonService commonService;

    @Override
    public void genMetalDetail(String statTime) {

    }

    /**
     * 生成当月最新库存数据Map
     */
    private Map<String, ReportOperationViewDetailDO> getNewestInvMap(String statTime) {
        // 1. 第一步：获得线上和线下库存的条目
        List<MetalPlanDetailVO> all = rrpDBHelper.getAll(MetalPlanDetailVO.class,
                "WHERE stat_time=? AND product_type='裸金属' AND compute_type='CPU'\n" +
                        "AND indicator_code IN ('c1','c2','c3','d3','d6')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                statTime);

        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

        // 1.2 合并重复的项
        Map<String, MetalPlanDetailVO> resultMap = removeRepeatData(all);

        // 1.3 to DO
        List<ReportOperationViewDetailDO> transform = ListUtils.transform(resultMap.values(), o -> o.toOperationViewDO(true));

        // 1.4 补全信息
        ListUtils.forEach(transform, o -> commonService.fillOtherFields(o, statTime));

        return ListUtils.toMap(transform, o -> o.getKey(), o -> o);
    }

    private Map<String, ReportOperationViewDetailDO> fillLast13WeekDemandData(String statTime,
            Map<String, ReportOperationViewDetailDO> monthData){
        //  获取过去13周的起止时间(end为上周周末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByWeek(statTime, 14);

        String start = timeInterval._1;
        Date curStart = DateUtils.parse(start);
        Date curEnd = DateUtils.addTime(curStart, Calendar.DATE, 6);

        Map<String, Map<String, BigDecimal>> cacheMap = new HashMap<>();

        for (int i = 14; i >= 1; i--) {
            List<MetalPlanDetailVO> all = rrpDBHelper.getAll(MetalPlanDetailVO.class,
                    "WHERE stat_time between ? and ? AND product_type='裸金属' AND compute_type='CPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY device_type,customhouse_title,area_name,region_name,zone_name,materialType"
                    , DateUtils.formatDate(curEnd), DateUtils.formatDate(curEnd));

            ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

            Map<String, List<MetalPlanDetailVO>> saleMap = ListUtils.groupBy(all, o -> o.getBaseGroupK());

            Map<String, BigDecimal> curCacheMap = new HashMap<>();
            for (Map.Entry<String, List<MetalPlanDetailVO>> entry : saleMap.entrySet()) {
                curCacheMap.put(entry.getKey(), NumberUtils.sum(entry.getValue(), o -> o.getSaleCores()));
            }

            cacheMap.put("m-" + i, curCacheMap);

            for (Map.Entry<String, List<MetalPlanDetailVO>> entry : saleMap.entrySet()) {
                if (i == 14){
                    break;
                }
                String key = entry.getKey();
                ReportOperationViewDetailDO detailDO = monthData.get(key);
                if (detailDO == null) {
                    ReportOperationViewDetailDO saleNewDO = entry.getValue().get(0).toOperationViewDO(false);
                    commonService.fillOtherFields(saleNewDO, statTime);
                    detailDO = saleNewDO;
                }

                //  合并list为一个DTO对象
                MetalPlanDetailVO dto = MetalPlanDetailVO.mergeListSaleDTO(entry.getValue());
                BigDecimal decimal = cacheMap.get("m-" + (i + 1)).get(key);
                //  获取已经cache了的往周map，取m-(n+1)周的值，作为上周周期的销量
                BigDecimal lastCycleSaleCores = decimal == null ? BigDecimal.ZERO : decimal;
                //  获取本周周期的销量
                BigDecimal curCycleSaleCores = dto.getSaleCores();
                //  取变化量作为销净增(简单记录下:demand_w_1 = sale_w_1 - sale_w_2
                dto.setSaleCores(curCycleSaleCores.subtract(lastCycleSaleCores));
                commonService.setHistoryDemandByI(i, dto, detailDO, ProductTypeEnum.METAL.getCode());
                monthData.put(key, detailDO);
            }
            curStart = DateUtils.addTime(curStart, Calendar.DATE, 7);
            curEnd = DateUtils.addTime(curEnd, Calendar.DATE, 7);
        }
        return monthData;
    }

    /**
     * 生成过去12个月的历史Map，包含库存、销售
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthInvMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);
        //  获取对应m-n月份的天数
        Map<String, Integer> monthNumOfDays = commonService.getMonthNumOfDays(statTime, 12);

        // 1、获得线上和线下库存的条目
        List<MetalPlanDetailVO> all = rrpDBHelper.getAll(MetalPlanDetailVO.class,
                "WHERE stat_time between ? and ? AND product_type='裸金属' AND compute_type='CPU'\n" +
                        "AND indicator_code IN ('c1','c2','c3','d3','d6')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                timeInterval._1, timeInterval._2);
        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
        // 1.2 合并重复的项
        Map<String, MetalPlanDetailVO> resultMap = removeRepeatData(all);

        Map<String, List<MetalPlanDetailVO>> map = ListUtils.groupBy(resultMap.values(), o -> o.getBaseGroupK());

        for (Map.Entry<String, List<MetalPlanDetailVO>> entry : map.entrySet()) {
            String key = entry.getKey();
            ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
            if (detailDO == null) {
                ReportOperationViewDetailDO history = entry.getValue().get(0).toOperationViewDO(false);
                commonService.fillOtherFields(history, statTime);
                detailDO = history;
            }
            for (MetalPlanDetailVO each : entry.getValue()) {
                String yearMonth = each.getYearMonth();
                Integer numOfDay = monthNumOfDays.get(yearMonth);
                Integer n = mReduceNMap.get(yearMonth);
                each.setOnlineCores(AmountUtils.divideScale6(each.getOnlineCores(), BigDecimal.valueOf(numOfDay)));
                each.setOfflineCores(AmountUtils.divideScale6(each.getOfflineCores(), BigDecimal.valueOf(numOfDay)));
                commonService.setHistoryInvByI(n, each, detailDO, ProductTypeEnum.METAL.getCode());
            }
            newestInvMap.put(key, detailDO);
        }
        return newestInvMap;
    }


    /**
     * 生成过去12个月的历史销售Map
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthSaleMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);

        String statTimeDate = timeInterval._2;
        for (int i = 1; i <= 12; i++) {
            int year = DateUtils.getYear(DateUtils.parse(statTimeDate));
            int month = DateUtils.getMonth(DateUtils.parse(statTimeDate));
            String curYearMonth = year + "-" + (month < 10 ? "0" + month : month);
            String curMonthFirstDay = curYearMonth + "-01";

            String lastMonthLastDay = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.DATE, -1));


            List<MetalPlanDetailVO> curMonth = rrpDBHelper.getAll(MetalPlanDetailVO.class,
                    "WHERE stat_time = ? AND product_type='裸金属' AND compute_type='CPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", statTimeDate);
            ListUtils.forEach(ListUtils.filter(curMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, MetalPlanDetailVO> curMonthMap = ListUtils.toMap(curMonth, o -> o.getBaseGroupK(), o -> o);

            List<MetalPlanDetailVO> lastMonth = rrpDBHelper.getAll(MetalPlanDetailVO.class,
                    "WHERE stat_time = ? AND product_type='裸金属' AND compute_type='CPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", lastMonthLastDay);
            ListUtils.forEach(ListUtils.filter(lastMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, MetalPlanDetailVO> lastMonthMap = ListUtils.toMap(lastMonth, o -> o.getBaseGroupK(), o -> o);


            Set<String> allK = new HashSet<>();
            allK.addAll(lastMonthMap.keySet());
            allK.addAll(curMonthMap.keySet());

            for (String key : allK) {
                MetalPlanDetailVO lastMonthData = lastMonthMap.get(key);
                MetalPlanDetailVO curMonthData = curMonthMap.get(key);
                String yearMonth = year + "-" + month;
                Integer n = mReduceNMap.get(yearMonth);
                MetalPlanDetailVO result = MetalPlanDetailVO.getDemandVO(lastMonthData, curMonthData);
                ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
                if (detailDO == null && result != null) {
                    ReportOperationViewDetailDO history = result.toOperationViewDO(false);
                    commonService.fillOtherFields(history, statTime);
                    detailDO = history;
                }
                commonService.setHistorySaleByI(n, result, detailDO, ProductTypeEnum.METAL.getCode());
                newestInvMap.put(key, detailDO);
            }

            statTimeDate = lastMonthLastDay;
        }
        return newestInvMap;
    }

    /**
     * 相同维度合并
     */
    public Map<String, MetalPlanDetailVO> removeRepeatData(List<MetalPlanDetailVO> all){
        Map<String, List<MetalPlanDetailVO>> map = ListUtils.groupBy(all, o -> o.getGroupK());
        Map<String, MetalPlanDetailVO> resultMap = new HashMap<>();

        for (Map.Entry<String, List<MetalPlanDetailVO>> entry : map.entrySet()) {
            List<MetalPlanDetailVO> value = entry.getValue();
            if (entry.getValue().size() > 1){
                MetalPlanDetailVO copyOne = value.get(0).copyOne();
                copyOne.setOnlineCores(NumberUtils.sum(value, o -> o.getOnlineCores()));
                copyOne.setOfflineCores(NumberUtils.sum(value, o -> o.getOfflineCores()));
                resultMap.put(entry.getKey(), copyOne);
            }else {
                resultMap.put(entry.getKey(), value.get(0));
            }
        }
        return resultMap;
    }


}
