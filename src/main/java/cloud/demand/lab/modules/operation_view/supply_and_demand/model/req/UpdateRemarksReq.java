package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateRemarksReq {

    @NotNull(message = "更新id不能为空")
    private Long id;

    @NotBlank(message = "备注不能空")
    @Length(max = 4096, message = "备注字符长度不能超过4096")
    private String remarks;

}
