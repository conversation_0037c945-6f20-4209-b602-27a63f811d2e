package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DayCbsScaleReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DayCbsScaleVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandCommonReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ScaleService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:37
 */
@Service
public class ScaleCbsServiceImpl implements ScaleService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DictService dictService;

    @Resource
    private DBHelper rrpDBHelper;

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CBS.getName();
    }

    @Override
    public List<DemandReportDetailVO> queryCurScaleData(DemandCommonReq commonReq) {
        DayCbsScaleReq req = DayCbsScaleReq.transform(commonReq);
        List<DayCbsScaleVO> dbList = getDayCbsScale(req);
        return ListUtils.transform(dbList,item -> DemandReportDetailVO.transform(item));
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req) {
        return queryChangeScaleDataFromLastMonth(req, null, null);
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq commonReq, List<String> notOtherCustomerUin, List<String> notOtherCommonCustomerShortName) {
        DayCbsScaleReq req = DayCbsScaleReq.transform(commonReq);
        List<String> allStatTime = addLastMonthStatTime(req.getStatTime());
        req.setOriginalStatTime(req.getStatTime());
        req.setStatTime(allStatTime);

        List<DayCbsScaleVO> dbList = getDayCbsScale(req);

        Function<DayCbsScaleVO, String> keyFunc = item -> StringUtils.joinWith("@", item.getCustomhouseTitle(), item.getCountryName(),
                item.getAreaName(), item.getRegionName(), item.getZoneCategory(), item.getZoneName(), item.getVolumeType());
        Map<String, DayCbsScaleVO> dbMap = dbList.stream().collect(Collectors.toMap(item -> StringUtils.joinWith("@", item.getStatTime(), item.getYearMonth(),
                keyFunc.apply(item)), Function.identity(), (k1, k2) -> k1));

        List<DemandReportDetailVO> retList = ListUtils.newArrayList();
        for (DayCbsScaleVO item : dbList) {
            if (!req.getOriginalStatTime().contains(item.getStatTime())) {
                continue;
            }
            String lastMonthStatTime = LocalDate.parse(item.getStatTime()).minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE);
            String lastMonth = lastMonthStatTime.substring(0, 7);
            String key = StringUtils.joinWith("@", lastMonthStatTime, lastMonth, keyFunc.apply(item));
            DayCbsScaleVO lastMonthScale = dbMap.get(key);
            retList.add(DemandReportDetailVO.transform(item, lastMonthScale));
        }
        return retList;
    }

    private List<DayCbsScaleVO> getDayCbsScale(DayCbsScaleReq req) {
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?",
                DateUtils.formatDate(DateUtils.yesterday()));
        if(ListUtils.isNotEmpty(req.getZoneCategory())){
            List<String>  zoneNameList = zoneConfigList.stream().filter(item -> ListUtils.contains(req.getZoneCategory(), o  -> StringUtils.equals(o,item.getTypeName())))
                    .map(item  -> item.getZoneName()).distinct().collect(Collectors.toList());
            req.setZoneName(ListUtils.union(req.getZoneName(), zoneNameList));
        }
        List<TxyRegionInfoDTO> allRegionInfoList = dictService.getAllTxyRegionInfo();
        if(ListUtils.isNotEmpty(req.getCountryName())){
            List<String>  regionNameList = allRegionInfoList.stream().filter(item -> ListUtils.contains(req.getCountryName(), o  -> StringUtils.equals(o,item.getCountry())))
                    .map(item  -> item.getRegionName()).distinct().collect(Collectors.toList());
            req.setRegionName(ListUtils.union(req.getRegionName(), regionNameList));
        }

        WhereBuilder whereBuilder = new WhereBuilder(req, ProductDemandDataDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        whereSQL.and("product_type = ?", "CBS");
        whereSQL.and("category = ? ", "销");

        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_day_cbs_scale.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());

        sql = SimpleSqlBuilder.doReplace(sql, "country_name", getCountryNameSelectCase(allRegionInfoList));
        sql = SimpleSqlBuilder.doReplace(sql, "zone_category", getZoneCategorySelectCase(zoneConfigList));

        List<String> fieldNames = Arrays.stream(DayCbsScaleVO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), req.getDims());
        List<DayCbsScaleVO> dbList = rrpDBHelper.getRaw(DayCbsScaleVO.class, sql, whereSQL.getParams());
        return dbList;
    }

}
