package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplyAndDemandVersionChangeResultDO;
import cloud.demand.lab.modules.operation_view.util.DimJoinUtil;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Map;

/**
 * 产品供应看板原因分析
 */
@Data
@ToString
public class SupplyAndDemandVersionChangeResultVO {

    private Long id;

    private String productCategory;

    /** 开始版本<br/>Column: [start_version] */
    private String startVersion;

    /** 结束版本<br/>Column: [end_version] */
    private String endVersion;

    /** 开始月份<br/>Column: [start_year_month] */
    private String startYearMonth;

    /** 结束月份<br/>Column: [end_year_month] */
    private String endYearMonth;

    /** 数据类型:采购-supply、需求-demand<br/>Column: [data_type] */
    private String dataType;

    /** 维度值集合,用@分割,开头结尾都有@<br/>Column: [dim_key] */
    private String dimKey;

    /** 维度值集合，用@分割,开头结尾都有@<br/>Column: [dim_value] */
    private String dimValue;

    /** 版本数据变动原因<br/>Column: [change_result] */
    private String changeResult;

    /** 查询md5值<br/>Column: [query_md5] */
    private String queryMd5;

    /** 查询json<br/>Column: [query_json] */
    private String queryJson;

    /** 更新时间<br/>Column: [update_time] */
    private Date updateTime;


    /** 最近改动人<br/>Column: [updater] */
    private String updater;

    public Map<String,String> getDim(){
        return DimJoinUtil.getDim(dimKey,dimValue);
    }

    public static SupplyAndDemandVersionChangeResultVO transform(SupplyAndDemandVersionChangeResultDO resultDO){
        SupplyAndDemandVersionChangeResultVO resultVO = new SupplyAndDemandVersionChangeResultVO();
        BeanUtils.copyProperties(resultDO,resultVO);
        return resultVO;
    }

}