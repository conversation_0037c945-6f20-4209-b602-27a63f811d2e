package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import report.utils.anno.WhereReport;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DayCvmScaleReq {

    @WhereReport
    private List<String> statTime;

    @WhereReport(sql = " product_class in (?)")
    private List<String> product;

    @WhereReport(sql = " origin_industry_dept in (?)")
    private List<String> industryDept;

    @WhereReport(sql = " crp_war_zone in (?)")
    private List<String> warZone;

    @WhereReport(sql = "un_customer_short_name in (?)")
    private List<String> commonCustomerShortName;

    @WhereReport(sql = " uin in (?)")
    private List<String> customerUin;

    @WhereReport(sql = "zone_category in (?)")
    private List<String> zoneCategory;

    @WhereReport
    private List<String> instanceCategory;

    @WhereReport
    private List<String> instanceGroup;

    @WhereReport
    private List<String> instanceType;

    @WhereReport
    private List<String> customhouseTitle;

    @WhereReport(sql = " country_name in (?)")
    private List<String> countryName;

    @WhereReport(sql = " area_name in (?) ")
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;

    private String queryRange = "服务用量";//计费用量、服务用量

    private List<String> dims;

    private List<String> notOtherCustomerUin;

    private List<String> notOtherCommonCustomerShortName;


    public static DayCvmScaleReq transform(DemandCommonReq req,List<String> notOtherCustomerUin,List<String> notOtherCommonCustomerShortName) {
        DayCvmScaleReq targetReq = new DayCvmScaleReq();
        BeanUtils.copyProperties(req, targetReq);
        targetReq.setStatTime(req.getScaleStatTime());
        targetReq.setNotOtherCustomerUin(notOtherCustomerUin);
        targetReq.setNotOtherCommonCustomerShortName(notOtherCommonCustomerShortName);
        return targetReq;
    }


}
