package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Table(value = "ppl_and_order_demand_data_adjust")
public class PplAndOrderDemandDataAdjustDO {

    /**
     * 分区键，代表数据版本<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 年月<br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 订单号<br/>Column: [order_number]
     */
    @Column(value = "order_number")
    private String orderNumber;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 通用客户简称<br/>Column: [common_customer_short_name]
     */
    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    @Column(value = "country_name")
    private String countryName;

    /**
     * 区域名称<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 地域编码<br/>Column: [region]
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区类型<br/>Column: [zone_category]
     */
    @Column(value = "zone_category")
    private String zoneCategory;

    /**
     * 可用区编码<br/>Column: [zone]
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_category]
     */
    @Column(value = "instance_category")
    private String instanceCategory;

    /**
     * 实例族<br/>Column: [instance_group]
     */
    @Column(value = "instance_group")
    private String instanceGroup;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 开始购买时间<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private String beginBuyDate;

    /**
     * 结束购买时间<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private String endBuyDate;

    /**
     * ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 数据源: 1.ORDER（订单数据） 2.VERSION（版本数据） 3.EXTEND（继承数据）<br/>Column: [data_source]
     */
    @Column(value = "data_source")
    private String dataSource;

    /**
     * 订单来源<br/>Column: [order_source]
     */
    @Column(value = "order_source")
    private String orderSource;

    /**
     * 干预备注<br/>Column: [remark]
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 错误信息<br/>Column: [error_msg]
     */
    @Column(value = "error_msg")
    private String errorMsg;

    /**
     * ppl需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private BigDecimal totalCore;

    /**
     * 购买核心数<br/>Column: [buy_total_core]
     */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalCore;

    /**
     * 待购买核心数<br/>Column: [wait_buy_total_core]
     */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore;

}