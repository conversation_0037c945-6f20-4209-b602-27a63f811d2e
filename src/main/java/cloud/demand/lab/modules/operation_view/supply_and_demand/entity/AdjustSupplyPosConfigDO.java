package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("adjust_supply_pos_config")
public class AdjustSupplyPosConfigDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 版本日期<br/>Column: [version_date] */
    @Column(value = "version_date")
    private String versionDate;

    /** 产品分类<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 生效状态<br/>Column: [valid_status] */
    @Column(value = "valid_status")
    private String validStatus;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 当前匹配Module<br/>Column: [module] */
    @Column(value = "module")
    private String module;

    /** 机位预启用日期<br/>Column: [pos_pre_start_datetime] */
    @Column(value = "pos_pre_start_datetime")
    private String posPreStartDatetime;

    /** 是否假机位<br/>Column: [is_fake_position] */
    @Column(value = "is_fake_position")
    private String isFakePosition;

    /** 台数<br/>Column: [num] */
    @Column(value = "num")
    private Integer num;

    /** 校准机位启用时间<br/>Column: [adjust_pos_pre_start_date] */
    @Column(value = "adjust_pos_pre_start_date")
    private String adjustPosPreStartDate;

    /** 校准备注<br/>Column: [adjust_remark] */
    @Column(value = "adjust_remark")
    private String adjustRemark;

}