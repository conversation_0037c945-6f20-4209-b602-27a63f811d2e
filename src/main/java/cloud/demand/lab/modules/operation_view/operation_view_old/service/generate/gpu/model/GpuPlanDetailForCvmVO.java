package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.model;

import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.model.PlanDetailVO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * GPU-CVM
 */
@Data
@Table("report_plan_detail")
public class GpuPlanDetailForCvmVO extends PlanDetailVO {

    @Column(value = "year_month", computed = "concat(year(stat_time), '-', month(stat_time))")
    private String yearMonth;

    /**
     * 母机机型<br/>Column: [device_name]
     */
    @Column(value = "device_type")
    private String deviceName;

    /**
     * 可用区名<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 地域名<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 地区名<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 好差呆类型，其中线下库存后处理
     */
    @Column(value = "materialType", computed = "(CASE WHEN indicator_code='d1' THEN '好料'\n" +
            "         WHEN indicator_code='d2' THEN '差料'\n" +
            "         WHEN indicator_code='d3' THEN '呆料'\n" +
            "         ELSE '' END)")
    private String materialType;

    /**
     * 线上库存
     */
    @Column(value = "onlineLogicNum",
            computed = "SUM((CASE WHEN indicator_code IN ('d1','d2','d3') THEN logic_num ELSE 0 END))")
    private BigDecimal onlineLogicNum;

    /**
     * 线下库存
     */
    @Column(value = "offlineLogicNum",
            computed = "SUM((CASE WHEN indicator_code IN ('e4','e8') THEN logic_num ELSE 0 END))")
    private BigDecimal offlineLogicNum;

    /**
     * 物理机单台核心数
     */
    @Column(value = "logicCpuCore", computed = "MAX(logic_cpu_core)")
    private BigDecimal logicCpuCore;

    /**
     * 售卖逻辑数
     */
    @Column(value = "saleLogicNum",
            computed = "SUM(CASE WHEN indicator_code IN ('b11', 'b12', 'b21', 'b22') THEN logic_num ELSE 0 END)")
    private BigDecimal saleLogicNum;

    public static GpuPlanDetailForCvmVO mergeListSaleDTO(List<GpuPlanDetailForCvmVO> list) {
        GpuPlanDetailForCvmVO result = new GpuPlanDetailForCvmVO();
        if (ListUtils.isEmpty(list)) {
            return result;
        } else {
            GpuPlanDetailForCvmVO dto = list.get(0);
            result.setCustomhouseTitle(dto.getCustomhouseTitle());
            result.setAreaName(dto.getAreaName());
            result.setRegionName(dto.getRegionName());
            result.setZoneName(dto.getZoneName());
            result.setDeviceName(dto.getDeviceName());
            result.setMaterialType(dto.getMaterialType());
        }
        result.setSaleLogicNum(NumberUtils.sum(list, o -> o.getSaleLogicNum()));
        return result;
    }

    /**
     * 通过上期和本期做减法得出净增量
     */
    public static GpuPlanDetailForCvmVO getDemandVO(GpuPlanDetailForCvmVO last, GpuPlanDetailForCvmVO current) {
        if (last == null) {
            return current;
        }
        if (current == null) {
            if (last.getSaleLogicNum() != null) {
                last.setSaleLogicNum(last.getSaleLogicNum().negate());
            }
            return last;
        }
        if (last != null && current != null) {
            GpuPlanDetailForCvmVO result = new GpuPlanDetailForCvmVO();
            result.setCustomhouseTitle(last.getCustomhouseTitle());
            result.setAreaName(last.getAreaName());
            result.setRegionName(last.getRegionName());
            result.setZoneName(last.getZoneName());
            result.setDeviceName(last.getDeviceName());
            result.setMaterialType(last.getMaterialType());
            result.setSaleLogicNum(current.getSaleLogicNum().subtract(last.getSaleLogicNum()));
            return result;
        }
        return null;
    }

    /**
     * 转换为OperationViewDetailDO
     */
    public ReportOperationViewDetailDO toOperationViewDO(boolean isForInventory) {
        ReportOperationViewDetailDO detailDO = new ReportOperationViewDetailDO();
        detailDO.setProductType(ProductTypeEnum.GPU.getCode());
        detailDO.setMaterialType(materialType);
        detailDO.setCustomhouseTitle(customhouseTitle);
        detailDO.setAreaName(areaName);
        detailDO.setRegionName(regionName);
        detailDO.setZoneName(zoneName);
        detailDO.setDeviceType(deviceName);
        if (isForInventory) {
            BigDecimal onlineLogicNum = this.onlineLogicNum == null ? BigDecimal.ZERO : this.onlineLogicNum;
            BigDecimal offlineLogicNum = this.offlineLogicNum == null ? BigDecimal.ZERO : this.offlineLogicNum;
            detailDO.setInvNewestTotal(onlineLogicNum.add(offlineLogicNum));
            detailDO.setInvNewestOnline(onlineLogicNum);
            detailDO.setInvNewestOffline(offlineLogicNum);
        }
        detailDO.setCpuLogicCore(logicCpuCore == null ? 0 : logicCpuCore.intValue());
        return detailDO;
    }

    public String getGroupK() {
        return String.join("-",
                yearMonth, deviceName, materialType, customhouseTitle, areaName, regionName, zoneName);
    }

    /**
     * 除yearMonth外的基础属性，这里获取这个k是为了去已经生成的DetailMap中找到对应的值
     */
    public String getBaseGroupK() {
        return String.join("-",
                ProductTypeEnum.GPU.getCode(), materialType, customhouseTitle, areaName, regionName, zoneName,
                deviceName);
    }

    /**
     * 复制一条具有基本属性的VO
     */
    public GpuPlanDetailForCvmVO copyOne() {
        GpuPlanDetailForCvmVO vo = new GpuPlanDetailForCvmVO();
        vo.setYearMonth(this.yearMonth);
        vo.setDeviceName(this.deviceName);
        vo.setZoneName(this.zoneName);
        vo.setRegionName(this.regionName);
        vo.setAreaName(this.areaName);
        vo.setCustomhouseTitle(this.customhouseTitle);
        vo.setMaterialType(this.materialType);
        vo.setLogicCpuCore(this.logicCpuCore);
        return vo;
    }
}
