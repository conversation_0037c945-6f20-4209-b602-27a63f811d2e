package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.DemandTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandCbsImportVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandCvmImportVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandDbImportVO;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.Constant;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
@ToString
@Table("ppl_and_order_demand_data_adjust")
public class ProductDemandDataAdjustDO extends ProductDemandDataDfDO{

    /** 干预备注<br/>Column: [remark] */
    @Column(value = "remark",insertValueScript = "'(空值)'")
    private String remark;

    /** 错误信息<br/>Column: [error_msg] */
    @Column(value = "error_msg",insertValueScript = "'(空值)'")
    private String errorMsg;

    public static ProductDemandDataAdjustDO transform(DemandCvmImportVO detail, ProductDemandDataDfDO dfDO) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(dfDO, ret);
        ret.setYearMonth(detail.getYearMonth());
        ret.setBeginBuyDate(detail.getBeginBuyDate());
        ret.setEndBuyDate(detail.getEndBuyDate());
        ret.setCustomhouseTitle(detail.getCustomhouseTitle());
        ret.setCountryName(detail.getCountryName());
        ret.setAreaName(detail.getAreaName());
        ret.setRegionName(detail.getRegionName());
        ret.setZoneName(detail.getZoneName());
        ret.setZoneCategory(detail.getZoneCategory());
        ret.setInstanceCategory(detail.getInstanceCategory());
        ret.setInstanceGroup(detail.getInstanceGroup());
        ret.setInstanceType(detail.getInstanceType());
        ret.setTotalCore(detail.getTotalAmount());
        ret.setBuyTotalCore(detail.getBuyAmount());
        ret.setWaitBuyTotalCore(detail.getWaitBuyAmount());

        return ret;
    }

    public static ProductDemandDataAdjustDO transform(DemandCbsImportVO detail, ProductDemandDataDfDO dfDO) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(dfDO, ret);
        ret.setYearMonth(detail.getYearMonth());
        ret.setBeginBuyDate(detail.getBeginBuyDate());
        ret.setEndBuyDate(detail.getEndBuyDate());
        ret.setCustomhouseTitle(detail.getCustomhouseTitle());
        ret.setCountryName(detail.getCountryName());
        ret.setAreaName(detail.getAreaName());
        ret.setRegionName(detail.getRegionName());
        ret.setZoneName(detail.getZoneName());
        ret.setZoneCategory(detail.getZoneCategory());
        ret.setVolumeType(detail.getVolumeType());
        ret.setDiskType(detail.getDiskType());
        ret.setDiskStorage(SoeCommonUtils.multiply(detail.getTotalAmount(),new BigDecimal("1024")));
        ret.setBuyDiskStorage(SoeCommonUtils.multiply(detail.getBuyAmount(),new BigDecimal("1024")));
        ret.setWaitBuyDiskStorage(SoeCommonUtils.multiply(detail.getWaitBuyAmount(),new BigDecimal("1024")));

        return ret;
    }

    public static ProductDemandDataAdjustDO transform(DemandDbImportVO detail, ProductDemandDataDfDO dfDO) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(dfDO, ret);
        ret.setYearMonth(detail.getYearMonth());
        ret.setBeginBuyDate(detail.getBeginBuyDate());
        ret.setEndBuyDate(detail.getEndBuyDate());
        ret.setCustomhouseTitle(detail.getCustomhouseTitle());
        ret.setCountryName(detail.getCountryName());
        ret.setAreaName(detail.getAreaName());
        ret.setRegionName(detail.getRegionName());
        ret.setZoneName(detail.getZoneName());
        ret.setZoneCategory(detail.getZoneCategory());
        ret.setDbDeployType(detail.getDbDeployType());
        ret.setDbStorageType(detail.getDbStorageType());
        ret.setDbFrameworkType(detail.getDbFrameworkType());
        ret.setDbMemory(detail.getTotalMemory());
        ret.setBuyDbMemory(detail.getBuyMemory());
        ret.setWaitBuyDbMemory(detail.getWaitBuyMemory());
        ret.setDiskStorage(SoeCommonUtils.multiply(detail.getTotalStorage(),new BigDecimal("1024")));
        ret.setBuyDiskStorage(SoeCommonUtils.multiply(detail.getBuyStorage(),new BigDecimal("1024")));
        ret.setWaitBuyDiskStorage(SoeCommonUtils.multiply(detail.getWaitBuyStorage(),new BigDecimal("1024")));
        return ret;
    }

    public static ProductDemandDataAdjustDO transform(DemandCvmImportVO detail) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(detail, ret);
        if (StringUtils.equals(detail.getDataSource(), "订单")) {
            ret.setDataSource("ORDER");
            ret.setOrderNumber(detail.getBillNumber());
            ret.setPplOrder(Constant.EMPTY_VALUE);
        } else if (StringUtils.equals(detail.getDataSource(), "需求PPL")) {
            detail.setDataSource("VERSION");
            ret.setOrderNumber(Constant.EMPTY_VALUE);
            ret.setPplOrder(detail.getBillNumber());
        }
        ret.setStatus(Constant.EMPTY_VALUE);
        ret.setOrderSource(Constant.EMPTY_VALUE);
        ret.setTotalCore(detail.getTotalAmount());
        ret.setBuyTotalCore(detail.getBuyAmount());
        ret.setWaitBuyTotalCore(detail.getWaitBuyAmount());
        ret.setDemandType(DemandTypeEnum.getCodeByName(detail.getDemandType()));
        return ret;
    }

    public static ProductDemandDataAdjustDO transform(DemandCbsImportVO detail) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(detail, ret);
        if (StringUtils.equals(detail.getDataSource(), "订单")) {
            ret.setDataSource("ORDER");
            ret.setOrderNumber(detail.getBillNumber());
            ret.setPplOrder(Constant.EMPTY_VALUE);
        } else if (StringUtils.equals(detail.getDataSource(), "需求PPL")) {
            detail.setDataSource("VERSION");
            ret.setOrderNumber(Constant.EMPTY_VALUE);
            ret.setPplOrder(detail.getBillNumber());
        }
        ret.setStatus(Constant.EMPTY_VALUE);
        ret.setOrderSource(Constant.EMPTY_VALUE);
        ret.setDiskStorage(SoeCommonUtils.multiply(detail.getTotalAmount(),new BigDecimal("1024")));
        ret.setBuyDiskStorage(SoeCommonUtils.multiply(detail.getBuyAmount(),new BigDecimal("1024")));
        ret.setWaitBuyDiskStorage(SoeCommonUtils.multiply(detail.getWaitBuyAmount(),new BigDecimal("1024")));
        ret.setDemandType(DemandTypeEnum.getCodeByName(detail.getDemandType()));
        return ret;
    }


    public static ProductDemandDataAdjustDO transform(DemandDbImportVO detail) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(detail, ret);
        if (StringUtils.equals(detail.getDataSource(), "订单")) {
            ret.setDataSource("ORDER");
            ret.setOrderNumber(detail.getBillNumber());
            ret.setPplOrder(Constant.EMPTY_VALUE);
        } else if (StringUtils.equals(detail.getDataSource(), "需求PPL")) {
            detail.setDataSource("VERSION");
            ret.setOrderNumber(Constant.EMPTY_VALUE);
            ret.setPplOrder(detail.getBillNumber());
        }
        ret.setStatus(Constant.EMPTY_VALUE);
        ret.setOrderSource(Constant.EMPTY_VALUE);
        ret.setDbMemory(detail.getTotalMemory());
        ret.setBuyDbMemory(detail.getBuyMemory());
        ret.setWaitBuyDbMemory(detail.getWaitBuyMemory());
        ret.setDiskStorage(SoeCommonUtils.multiply(detail.getTotalStorage(),new BigDecimal("1024")));
        ret.setBuyDiskStorage(SoeCommonUtils.multiply(detail.getBuyStorage(),new BigDecimal("1024")));
        ret.setWaitBuyDiskStorage(SoeCommonUtils.multiply(detail.getWaitBuyStorage(),new BigDecimal("1024")));
        ret.setDemandType(DemandTypeEnum.getCodeByName(detail.getDemandType()));
        return ret;
    }

}