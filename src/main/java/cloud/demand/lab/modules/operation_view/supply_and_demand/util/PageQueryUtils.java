package cloud.demand.lab.modules.operation_view.supply_and_demand.util;

import com.pugwoo.wooutils.collect.ListUtils;
import erp.base.fiber.support.dispatcher.FiberSupplier;
import erp.base.fiber.support.dispatcher.FiberTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import report.utils.utils.SecurityFiberSupplier;
import yunti.boot.exception.ITException;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/** 异步分页请求 */
@Slf4j(topic = "异步请求")
public class PageQueryUtils {

    // 线程执行器
    public final static FiberTaskExecutor fiberTaskExecutor = FiberTaskExecutor.newDefaultExecutor();

    /**
     * 提交后按批次执行
     * @param batchSize 批次大小
     * @param waitSecond 单次批次执行的等待时间
     * @param run 异步任务
     */
    public static void submit(int batchSize, long waitSecond, List<Runnable> run){
        if (ListUtils.isEmpty(run)){
            return;
        }
        if (batchSize <= 0){
            throw new ITException("批量提交数量不能小于1");
        }
        if (batchSize >= run.size()){
            if (run.size() > 10) {
                throw new ITException("提交数量不能超过10，请分批提交");
            }
            CountDownLatch latch = new CountDownLatch(run.size());
            for (Runnable runnable : run) {
                fiberTaskExecutor.submit(new SecurityFiberSupplier() {
                    @Override
                    public void consume() {
                        runnable.run();
                        latch.countDown();
                    }
                });
            }
            try {
                if (!latch.await(waitSecond, TimeUnit.SECONDS)){
                    throw new ITException(String.format("异步查询等待超时,等待时间(s)：【%s】", waitSecond));
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }else {
            int lave = run.size()%batchSize;
            int size = run.size()/batchSize;
            if (lave != 0){
                size += 1;
            }
            List<CountDownLatch> latchList = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                if (i == size - 1 && lave != 0){
                    latchList.add(new CountDownLatch(lave));
                }else {
                    latchList.add(new CountDownLatch(batchSize));
                }
            }
            int index = 0;
            for (CountDownLatch latch : latchList) {
                long count = latch.getCount();
                for (long i = 0; i < count; i++) {
                    Runnable runnable = run.get(index++);
                    fiberTaskExecutor.submit(new SecurityFiberSupplier() {
                        @Override
                        public void consume() {
                            runnable.run();
                            latch.countDown();
                        }
                    });
                }
                try {
                    if (!latch.await(waitSecond, TimeUnit.SECONDS)){
                        throw new ITException(String.format("异步查询等待超时,等待时间(s)：【%s】", waitSecond));
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }

    }

    /**
     * 异步查询
     * @param waitSecond 超时时间
     * @param suppliers 方法集合
     * @return 方法结果集
     */
    @SafeVarargs
    public static <T> List<T> waitAll(long waitSecond, Supplier<T>... suppliers) {
        List<T> ret = new ArrayList<>();
        // 规避异常情况，这里直接返回空集合
        if (suppliers == null || suppliers.length == 0) {
            return ret;
        }
        // 先默认填充null
        for (int i = 0; i < suppliers.length; i++) {
            ret.add(null);
        }
        FiberUtils.Executor executor = FiberUtils.getExecutor(suppliers.length);
        // 门栓和函数长度一致
        for (int i = 0; i < suppliers.length; i++) {
            Supplier<T> supplier = suppliers[i];
            if (supplier != null) {
                int finalI = i;
                executor.submit(() -> {
                    T o = supplier.get();
                    if (o != null) {
                        ret.set(finalI,o);
                    }
                });
            }
        }
        if (!executor.await(waitSecond, TimeUnit.SECONDS)){
            throw new ITException(String.format("异步查询等待超时,等待时间(s)：【%s】", waitSecond));
        }
        // 抛出首个异常
        if (executor.hasError()){
            throw new ITException(executor.getExceptionList().get(0));
        }
        return ret;
    }


    /**
     * 在事务提交后执行
     * @param fiberSupplier 需要执行的方法
     */
    public static void afterCommit(FiberSupplier fiberSupplier) {
        if (!TransactionSynchronizationManager.isSynchronizationActive()) {
            throw new IllegalStateException("当前没有活动的事务，无法注册事务同步");
        }
        long timeMillis = System.currentTimeMillis();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                log.info("事务提交后执行，等待耗时：{}ms", System.currentTimeMillis() - timeMillis);
                try {
                    fiberTaskExecutor.submit(fiberSupplier);
                } catch (Exception e) {
                    log.error("提交异步任务失败", e);
                }
            }
        });
    }
}
