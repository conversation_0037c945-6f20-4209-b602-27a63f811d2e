package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Data
@ToString
public class CdbCrsInventoryTargetVO implements IInventoryTargetGroupKey,CountryNameFiller,ZoneCategoryFiller{

    @Column(value = "stat_time")
    private String statTime;

    private String product;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    private String countryName;

    @Column(value = "area_name")
    private String areaName;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    private String zoneCategory;

    @Column(value = "inventory_target")
    private BigDecimal inventoryTarget;

    private BigDecimal stockThreshold;


    @Override
    public String getInventoryTargetGroupKey() {
        return StringUtils.joinWith("@", this.getStatTime(), this.getCustomhouseTitle(), this.getCountryName(), this.getAreaName(), this.getRegionName(),
                this.getZoneName());
    }

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.getRegionName();
    }

    @Override
    public void fillCountryName(String countryName) {
        this.setCountryName(countryName);
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.setZoneCategory(zoneCategory);
    }
}
