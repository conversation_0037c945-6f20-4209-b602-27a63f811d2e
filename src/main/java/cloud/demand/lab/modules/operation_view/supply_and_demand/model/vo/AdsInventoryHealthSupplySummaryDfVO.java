package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:39
 */
@Data
public class AdsInventoryHealthSupplySummaryDfVO {

    @Column("stat_time")
    private String statTime;

    @Column("any_zone_category")
    private String zoneCategory;

    @Column("any_instance_category")
    private String instanceCategory;

    @Column("any_customhouse_title")
    private String customhouseTitle;

    @Column("any_country_name")
    private String countryName;

    @Column("any_area_name")
    private String areaName;

    @Column("any_region_name")
    private String regionName;

    @Column("any_zone_name")
    private String zoneName;

    @Column("any_instance_group")
    private String instanceGroup;

    @Column("any_instance_type")
    private String instanceType;

    @Column("any_volume_type")
    private String volumeType;

    @Column("any_product")
    private String product;

    @Column("begin_inventory")
    private BigDecimal beginInventory;

    @Column("withhold_inventory_core")
    private BigDecimal withholdInventoryCore;

}
