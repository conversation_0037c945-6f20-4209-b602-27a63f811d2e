package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdsInventoryHealthSupplySummaryDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.AdsInventoryHealthSupplySummaryDfVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 14:54
 */
@Service
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<String> queryParams(SupplyDemandParamTypeReq req) {
        if (StringUtils.isBlank(req.getParamType())) {
            return new ArrayList<>();
        }

        String column = ORMUtils.getColumnByFieldName(AdsInventoryHealthSupplySummaryDfDO.class, req.getParamType());
        if (column == null) {
            return new ArrayList<>();
        }
        String sql = "select distinct ${paramType} from cloud_demand.ads_inventory_health_supply_summary_df where product_type = ? ";

        return ckcldDBHelper.getRaw(String.class, sql.replace("${paramType}", column), req.getProductCategory());
    }

    public List<SupplyDemandHedgingItemVO> queryInventory(SupplyDemandHedgingReq req) {
        WhereBuilder whereBuilder = new WhereBuilder(req, AdsInventoryHealthSupplySummaryDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        List<String> statTimeList = ListUtils.newArrayList();
        if (BooleanUtils.isTrue(req.isOverView())) {
            statTimeList.add(req.getStatTime());
        } else {
            if (StringUtils.isNotBlank(req.getStartStatTime())) {
                statTimeList.add(req.getStartStatTime());
            }
            if (StringUtils.isNotBlank(req.getEndStatTime())) {
                statTimeList.add(req.getEndStatTime());
            }
        }

        whereSQL.and(" stat_time in (?) ", statTimeList);
        whereSQL.and(" product_type = ? ", req.getProductCategory());
        whereSQL.andIf(ListUtils.isNotEmpty(req.getProduct()), " product in (?) ", req.getProduct());
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_inventory.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());

        List<String> allDims = ListUtils.newArrayList("zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName",
                "regionName", "zoneName", "instanceGroup", "instanceType", "volumeType", "product");
        List<String> fieldNames = Arrays.stream(AdsInventoryHealthSupplySummaryDfDO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), allDims);
        List<AdsInventoryHealthSupplySummaryDfVO> dataList = ckcldDBHelper.getRaw(AdsInventoryHealthSupplySummaryDfVO.class, sql, whereSQL.getParams());
        if (StringUtils.equals(req.getUnit(), UnitEnum.NUM.getName()) && StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())) {
            //获取数据库内存容量(目前只考虑一个规划产品一个机型
            Map<String, BigDecimal> deviceMap = demandDBHelper.getAll(BasDeviceMemoryDO.class)
                    .stream().collect(Collectors.toMap(BasDeviceMemoryDO::getPlanProductName, BasDeviceMemoryDO::getGbSaleMemory, (k1, k2) -> k1));
            for (AdsInventoryHealthSupplySummaryDfVO item : dataList) {
                BigDecimal singleMemory = deviceMap.getOrDefault(item.getProduct(), BigDecimal.ZERO);
                if (BigDecimal.ZERO.compareTo(singleMemory) == 0) {
                    item.setBeginInventory(BigDecimal.ZERO);
                } else {
                    item.setBeginInventory(SoeCommonUtils.divide(item.getBeginInventory(), singleMemory));
                }
            }
        }
        List<SupplyDemandHedgingItemVO> itemList = ListUtils.newArrayList();
        itemList.addAll(ListUtils.transform(dataList, item -> SupplyDemandHedgingItemVO.transform(item, "withholdInventory")));
        itemList.addAll(ListUtils.transform(dataList, item -> SupplyDemandHedgingItemVO.transform(item, "actualInventory")));

        List<String> mergeDims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type"));
        return SupplyDemandHedgingItemVO.mergeList(itemList, mergeDims);
    }
}
