package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdjustSupplyPosConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.FailAdjustPosConfig;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.OriginAdjustPosConfig;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplyOnTheWayDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.PlanProductData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyOnTheWayDetailData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyReportDetailReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyReportTrendData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandVersionChangeResultService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyOnTheWayReportService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.GroupUtils;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.DynamicProperties;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class SupplyOnTheWayReportServiceImpl implements SupplyOnTheWayReportService {

    @Resource
    DBHelper demandDBHelper;

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    DBHelper yuntiDBHelper;

    @Override
    public void adjustPositionByConfig(String version, String productType) {
        //获取当前版本的生效机位调整信息

        List<AdjustSupplyPosConfigDO> all = demandDBHelper.getAll(AdjustSupplyPosConfigDO.class,
                "where version_date = ? and valid_status = 'VALID'", version);

        //获取日切片数据
        List<SupplyOnTheWayDataDfDO> supplyAll = ckcldDBHelper.getAll(SupplyOnTheWayDataDfDO.class, "where stat_time = ? and product_type = ?",
                version, productType);

        //进行机位调整
        Map<String, List<SupplyOnTheWayDataDfDO>> supplyMap = ListUtils.toMapList(supplyAll,
                o -> String.join("@", o.getCustomhouseTitle(), o.getModule(), o.getPosPreStartDatetime(),
                        o.getIsFakePosition()), o -> o);
        Map<String, AdjustSupplyPosConfigDO> configMap = ListUtils.toMap(all,
                o -> String.join("@", o.getCustomhouseTitle(), o.getCustomhouseTitle(), o.getModule(),
                        o.getPosPreStartDatetime(), o.getIsFakePosition()), o -> o);
        for (Entry<String, List<SupplyOnTheWayDataDfDO>> entry : supplyMap.entrySet()) {
            List<SupplyOnTheWayDataDfDO> value = entry.getValue();
            String key = entry.getKey();
            AdjustSupplyPosConfigDO config = configMap.get(key);
            if (config != null && config.getAdjustPosPreStartDate() != null) {
                for (SupplyOnTheWayDataDfDO data : value) {
                    data.setAdjustPosDatetime(config.getAdjustPosPreStartDate());
                    data.setAdjustRemark(config.getAdjustRemark());
                    if (data.getAdjustPosDatetime().contains("1900") || data.getEstArriveDate().contains("1900")) {
                        data.setAdjustSlaDate("未知");
                        data.setAdjustSlaDate("未知");
                        data.setAdjustSlaDateMonth("未知");
                        data.setDeliveryPoint("未知");
                        data.setGap(-1);
                    } else {
                        LocalDate estDate = DateUtils.parseLocalDate(data.getEstArriveDate());
                        LocalDate posDate = DateUtils.parseLocalDate(data.getAdjustPosDatetime());
                        data.setAdjustSlaDate(estDate.isAfter(posDate) ? DateUtils.formatDate(estDate) : DateUtils.formatDate(posDate));
                        data.setDeliveryPoint(estDate.isAfter(posDate) ? "服务器" : "机位");
                        LocalDate useDate = DateUtils.parseLocalDate(data.getQuotaUseTime());
                        LocalDate adjustDate = DateUtils.parseLocalDate(data.getAdjustSlaDate());
                        data.setAdjustSlaDateMonth(YearMonth.of(adjustDate.getYear(), adjustDate.getMonthValue()).toString());
                        Period between = Period.between(useDate, adjustDate);
                        data.setGap(between.getDays() > 7 ? 1 : 0);
                    }
                }
            }

        }
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.supply_on_the_way_data_df_local ON CLUSTER default_cluster DROP PARTITION ?", version);
        ckcldDBHelper.insertBatchWithoutReturnId(supplyAll);
    }

    @Override
    public List<FailAdjustPosConfig> importAdjustPositionConfig(String version, String productType,
                                                                List<OriginAdjustPosConfig> configs) {
        List<FailAdjustPosConfig> result = new ArrayList<>();
        List<AdjustSupplyPosConfigDO> finalResult = new ArrayList<>();
        for (OriginAdjustPosConfig config : configs) {
            String adjust = config.getAdjustPosPreStartDate();
            if (StringUtils.isNotBlank(adjust) && DateUtils.parseLocalDate(adjust) == null) {
                FailAdjustPosConfig adjustSupplyPosConfigDTO = new FailAdjustPosConfig();
                BeanUtils.copyProperties(config, adjustSupplyPosConfigDTO);
                adjustSupplyPosConfigDTO.setFailReason("校准机位启用日期格式错误");
                result.add(adjustSupplyPosConfigDTO);
            }
            AdjustSupplyPosConfigDO adjustDO = new AdjustSupplyPosConfigDO();
            BeanUtils.copyProperties(config, adjustDO);
            adjustDO.setValidStatus("VALID");
            adjustDO.setVersionDate(version);
            adjustDO.setProductType(productType);
            finalResult.add(adjustDO);
        }
        if (ListUtils.isEmpty(result)) {
            List<AdjustSupplyPosConfigDO> all = demandDBHelper.getAll(AdjustSupplyPosConfigDO.class,
                    "where version_date = ? and valid_status = 'VALID' and product_type = ?", version, productType);
            if (ListUtils.isNotEmpty(all)) {
                all.forEach(o -> o.setValidStatus("INVALID"));
                demandDBHelper.update(all);
            }
            demandDBHelper.insert(finalResult);
        }
        return result;
    }

    @Override
    public List<OriginAdjustPosConfig> exportAdjustPositionConfig(String version, String productType) {
        List<OriginAdjustPosConfig> result = new ArrayList<>();

        //1.首先获取当前version的采购数据
        List<SupplyOnTheWayDataDfDO> supplyAll = ckcldDBHelper.getAll(SupplyOnTheWayDataDfDO.class,
                "where stat_time = ? and product_type = ?", version, productType);
        Map<String, List<SupplyOnTheWayDataDfDO>> supplyMap = ListUtils.toMapList(supplyAll,
                o -> String.join("@", o.getCustomhouseTitle(), o.getModule(), o.getPosPreStartDatetime(),
                        o.getIsFakePosition()), o -> o);
        //2.获取最新的配置
        List<AdjustSupplyPosConfigDO> adjustAll = demandDBHelper.getAll(AdjustSupplyPosConfigDO.class,
                "where version_date = ? and product_type = ?", version, productType);
        Map<String, AdjustSupplyPosConfigDO> adjustMap = ListUtils.toMap(adjustAll,
                o -> String.join("@", o.getCustomhouseTitle(), o.getModule(), o.getPosPreStartDatetime(),
                        o.getIsFakePosition()), o -> o);
        Set<String> keySet = new HashSet<>();
        if (ListUtils.isNotEmpty(supplyMap.keySet())) {
            keySet.addAll(supplyMap.keySet());
        }

        if (ListUtils.isNotEmpty(adjustMap.keySet())) {
            keySet.addAll(adjustMap.keySet());
        }
        for (String key : keySet) {
            List<SupplyOnTheWayDataDfDO> supply = supplyMap.get(key);
            AdjustSupplyPosConfigDO adjust = adjustMap.get(key);
            OriginAdjustPosConfig origin = new OriginAdjustPosConfig();
            if (adjust != null) {
                BeanUtils.copyProperties(adjust, origin);
            } else if (ListUtils.isNotEmpty(supply)) {
                SupplyOnTheWayDataDfDO onTheWay = supply.get(0);
                origin.setCustomhouseTitle(onTheWay.getCustomhouseTitle());
                origin.setModule(onTheWay.getModule());
                origin.setPosPreStartDatetime(onTheWay.getPosPreStartDatetime());
                origin.setIsFakePosition(onTheWay.getIsFakePosition());
                origin.setNum(NumberUtils.sum(supply, SupplyOnTheWayDataDfDO::getCpuNum).intValue());
            }
            result.add(origin);
        }

        return result;
    }

    @Override
    public List<SupplyOnTheWayDetailData> querySupplyOnTheWayDetailReport(SupplyReportDetailReq req) {
        List<SupplyOnTheWayDataDfDO> all = getOriginSupplyDetailData(req);
        List<SupplyOnTheWayDetailData> itemList = new ArrayList<>();

        Function<SupplyOnTheWayDataDfDO, BigDecimal> amountGetter = SupplyOnTheWayDataDfDO.amountGetter(req.getUnit());

        for (SupplyOnTheWayDataDfDO supply : all) {
            SupplyOnTheWayDetailData item = new SupplyOnTheWayDetailData();
            BeanUtils.copyProperties(supply, item);

            item.setCountryName(supply.getCountry());
            item.setInstanceGroup(supply.getGinFamily());
            item.setQuotaCampusName(supply.getCampusName());
            item.setSlaMonth(supply.getSlaDateMonth());
            item.setIsGap(supply.getGap());
            if (StringUtils.compare(supply.getSlaDateExpect(), supply.getQuotaUseTime()) < 0
                    && !StringUtils.equals("0000-00-00", supply.getSlaDateExpect())
                    && !StringUtils.equals("1900-01-01", supply.getSlaDateExpect())) {
                item.setRisk("无风险");
            } else {
                item.setRisk("有风险");
            }
            item.setQuotaDeviceClass(supply.getQuotaDeviceClass());
            BigDecimal amount = amountGetter.apply(supply);
            item.setAmount(amount);
            item.setInstanceNum(supply.getCpuNum());
            itemList.add(item);
        }

        List<SupplyOnTheWayDetailData> ret = ListUtils.newArrayList();
        List<String> dims = req.getDims();
        List<String> sumFields = ListUtils.newArrayList("amount", "instanceNum");
        ListUtils.groupBy(itemList, item -> GroupUtils.getDimsGroupKey(item, dims))
                .forEach((k, v) -> {
                    SupplyOnTheWayDetailData item = GroupUtils.mergeList(v, dims, sumFields);
                    item.setCpuNum(item.getInstanceNum().intValue());
                    item.setSupplyCore(item.getAmount().intValue());
                    ret.add(item);
                });

        return ret;
    }

    @Override
    public List<SupplyReportTrendData> querySupplyOnTheWayTrendReport(SupplyReportDetailReq req) {
        List<SupplyOnTheWayDetailData> dataList = querySupplyOnTheWayDetailReport(req);
        Map<String, List<SupplyOnTheWayDetailData>> mapList = ListUtils.toMapList(dataList,
                o -> String.join("@", o.getStatTime(), o.getSlaMonth()),
                o -> o);
        List<SupplyReportTrendData> result = new ArrayList<>();
        for (Entry<String, List<SupplyOnTheWayDetailData>> entry : mapList.entrySet()) {
            SupplyReportTrendData trend = new SupplyReportTrendData();
            SupplyOnTheWayDetailData data = entry.getValue().get(0);
            trend.setStatTime(data.getStatTime());
            trend.setMonth(data.getSlaMonth());
            trend.setSupplyCore(NumberUtils.sum(entry.getValue(), SupplyOnTheWayDetailData::getSupplyCore).intValue());
            trend.setAmount(NumberUtils.sum(entry.getValue(), SupplyOnTheWayDetailData::getAmount));
            trend.setCpuNum(NumberUtils.sum(entry.getValue(), SupplyOnTheWayDetailData::getCpuNum).intValue());
            trend.setInstanceNum(NumberUtils.sum(entry.getValue(), SupplyOnTheWayDetailData::getInstanceNum));
            result.add(trend);
        }
        return result;
    }

    @Override
    public DownloadBean exportSupplyDetailExcel(SupplyReportDetailReq req) {
        List<SupplyOnTheWayDataDfDO> data = getOriginSupplyDetailData(req);
        data.forEach(item -> item.setSingleLogicCapacity(SoeCommonUtils.divide(item.getLogicCapacity(), item.getCpuNum())));
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/supply_demand/supply_on_the_way_detail.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "采购在途数据明细" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public List<String> queryAllSupplyCampus() {
        return ckcldDBHelper.getRaw(String.class, "select distinct campus_name from supply_on_the_way_data_df");
    }

    @Override
    public List<PlanProductData> queryProductAndPlanProduct() {
        List<PlanProductData> result = new ArrayList<>();
        List<CloudDemandCsigResourceViewCategoryDO> categoryAll = yuntiDBHelper.getAll(
                CloudDemandCsigResourceViewCategoryDO.class);
        Map<String, List<String>> productMap = ListUtils.toMapList(categoryAll,
                CloudDemandCsigResourceViewCategoryDO::getCategory5,
                CloudDemandCsigResourceViewCategoryDO::getPlanProductName);
        for (Entry<String, List<String>> entry : productMap.entrySet()) {
            PlanProductData data = new PlanProductData();
            data.setProduct(entry.getKey());
            data.setPlanProducts(entry.getValue().stream().distinct().collect(Collectors.toList()));
            result.add(data);
        }
        return result;
    }

    @Override
    public List<String> queryParams(SupplyDemandParamTypeReq req) {
        if (StringUtils.isBlank(req.getParamType())) {
            return new ArrayList<>();
        }
        String column = null;
        List<String> existList = ListUtils.newArrayList("productType", "product","quotaPlanProductName", "instanceType", "quotaDeviceClass","xyIndustry","xyCustomerName","deliveryPoint", "projectName");
        if (ListUtils.contains(existList, item -> StringUtils.equals(item, req.getParamType()))) {
            column = ORMUtils.getColumnByFieldName(SupplyOnTheWayDataDfDO.class, req.getParamType());
        } else {
            if (StringUtils.equals(req.getParamType(), "quotaCampusName")) {
                column = "campus_name";
            } else if (StringUtils.equals(req.getParamType(), "instanceGroup")) {
                column = "gin_family";
            } else if (StringUtils.equals(req.getParamType(), "zoneCategory")) {
                List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class,
                        "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
                List<String> zoneCategory = zoneConfigList.stream().map(InventoryHealthMainZoneNameConfigDO::getTypeName).distinct().collect(Collectors.toList());
                zoneCategory.add("未分类");
                return zoneCategory;
            } else if (StringUtils.equals(req.getParamType(), "instanceCategory")) {
                List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class,
                        "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
                List<String> instanceCategory = insConfigList.stream().map(InventoryHealthMainInstanceTypeConfigDO::getType2Name).distinct().collect(Collectors.toList());
                instanceCategory.add("未分类");
                return instanceCategory;
            }
        }
        if (StringUtils.isEmpty(column)) {
            return new ArrayList<>();
        }
        String sql = "select distinct ${paramType} from cloud_demand.supply_on_the_way_data_df ${where} ";
        List<String> ret;
        if(StringUtils.isNotEmpty(req.getProductCategory())){
            sql = sql.replace("${where}", "where product_type = ? ");
            ret = ckcldDBHelper.getRaw(String.class, sql.replace("${paramType}", column), req.getProductCategory());
        }else {
            sql = sql.replace("${where}", " ");
            ret = ckcldDBHelper.getRaw(String.class, sql.replace("${paramType}", column));
        }

        ret = ret.stream().filter(item -> StringUtils.isNotEmpty(item)).collect(Collectors.toList());
        return ret;

    }

    @Override
    public List<ProductVO> queryProduct() {
        List<CloudDemandCsigResourceViewCategoryDO> categoryAll = yuntiDBHelper.getAll(
                CloudDemandCsigResourceViewCategoryDO.class);
        return categoryAll.stream()
                .map(item -> new ProductVO(item.getCategory5(), item.getPlanProductName()))
                .distinct().collect(Collectors.toList());
    }


    public List<SupplyOnTheWayDataDfDO> getOriginSupplyDetailData(SupplyReportDetailReq req) {

        WhereSQL condition = req.genBasicCondition();

        condition.andIf(StringUtils.isNotEmpty(req.getStartMonth()), "(sla_date_month >= ? or sla_date_month = ?)",
                req.getStartMonth(), req.isIncludeUnKnow() ? "未知" : "");
        condition.andIf(StringUtils.isNotEmpty(req.getEndMonth()), "(sla_date_month <= ? or sla_date_month = ?)",
                req.getEndMonth(), req.isIncludeUnKnow() ? "未知" : "");
        if (ListUtils.isNotEmpty(req.getProductType()) && req.getProductType().get(0).equals("数据库")) {
            //todo  后续接入真实q单
            List<String> quotaList = new ArrayList<>();
            condition.and("quota_id not in (?)", quotaList);
            if (ListUtils.isNotEmpty(req.getProjectName())) {
                condition.and("project_name in (?)", req.getProjectName());
            }
        }

        return ckcldDBHelper.getAll(SupplyOnTheWayDataDfDO.class, condition.getSQL(), condition.getParams());
    }


}
