package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Getter
public enum VolumeTypeEnum {
    UNKNOWN("unknown", "未知", ListUtils.newArrayList(),ListUtils.newArrayList()),

    SSD("ssd", "SSD",ListUtils.newArrayList("ssd","tssd"),ListUtils.newArrayList("云下存储CBS SSD","云下存储CBS极速")),
    PREMIUM("premium", "高性能",ListUtils.newArrayList("throughput", "premium"),ListUtils.newArrayList("云下存储CBS高效")),
    ;
    private final String code;
    private final String name;

    private final List<String> deviceType;

    private final List<String> deviceFamilyName;

    VolumeTypeEnum(String code, String name,List<String> deviceType,List<String> deviceFamilyName) {
        this.code = code;
        this.name = name;
        this.deviceType = deviceType;
        this.deviceFamilyName = deviceFamilyName;
    }

    public static VolumeTypeEnum getByName(String name) {
        for (VolumeTypeEnum e : VolumeTypeEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }

    public static VolumeTypeEnum getByDeviceFamilyName(String deviceFamilyName)  {
        for (VolumeTypeEnum e : VolumeTypeEnum.values()) {
            if (ListUtils.contains(e.getDeviceFamilyName(), item -> StringUtils.equals(item, deviceFamilyName))) {
                return e;
            }
        }
        return null;
    }

    public static VolumeTypeEnum getByCode(String code) {
        for (VolumeTypeEnum e : VolumeTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getNameByCode(String code) {
        VolumeTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        VolumeTypeEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }

    public static String getNameByDeviceFamilyName(String deviceFamilyName)  {
        VolumeTypeEnum e = getByDeviceFamilyName(deviceFamilyName);
        return e == null ? "" : e.getName();
    }

}