package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/13 13:40
 */
@Data
public class DayCvmScaleVO {
    @Column(value = "stat_time")
    private String statTime;

    @Column(value = "year_month")
    private String yearMonth;

    @Column(value = "any_product")
    private String product;

    @Column(value = "any_industry_dept")
    private String industryDept;

    @Column(value = "any_war_zone")
    private  String  warZone;

    @Column(value = "any_common_customer_short_name")
    private  String  commonCustomerShortName;

    @Column(value = "any_customer_uin")
    private  String  customerUin;

    @Column(value = "any_customer_short_name")
    private String customerShortName;

    @Column(value = "any_customhouse_title")
    private String customhouseTitle;

    @Column(value = "any_country_name")
    private String countryName;

    @Column(value = "any_area_name")
    private String areaName;

    @Column(value = "any_region_name")
    private String regionName;

    @Column(value = "any_zone_category")
    private String zoneCategory;

    @Column(value = "any_zone_name")
    private String zoneName;

    @Column(value = "any_instance_category")
    private String instanceCategory;

    @Column(value = "any_instance_group")
    private String  instanceGroup;

    @Column(value = "any_instance_type")
    private String instanceType;

    @Column(value = "total_amount")
    private BigDecimal totalAmount;
}
