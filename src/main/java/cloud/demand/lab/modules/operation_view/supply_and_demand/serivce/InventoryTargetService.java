package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/7 16:29
 */
public interface InventoryTargetService {

    Map<String, InventoryTargetVO> getInventoryTarget(InventoryTargetReq req);

    String getProductCategory();

    Boolean matchFlag(InventoryTargetReq req);

}
