package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DemandReportReq extends DemandCommonReq {

    @NotBlank(message = "开始版本不能为空")
    public String startStatTime;

    @NotBlank(message = "结束版本不能为空")
    public String endStatTime;

    @NotBlank(message = "开始月份不能为空")
    private String startYearMonth;

    @NotBlank(message = "结束月份不能为空")
    private String endYearMonth;

    public static DemandReportReq transform(SupplyDemandHedgingReq req) {
        DemandReportReq ret = new DemandReportReq();
        List<String> statTimeList = ListUtils.newArrayList();
        if (BooleanUtils.isTrue(req.isOverView())) {
            statTimeList = ListUtils.newArrayList(req.getStatTime());
        } else {
            if(StringUtils.isNotEmpty(req.getStartStatTime())){
                statTimeList.add(req.getStartStatTime());
            }
            if (StringUtils.isNotEmpty(req.getEndStatTime())) {
                statTimeList.add(req.getEndStatTime());
            }
        }
        ret.setStatTimeList(statTimeList);
        ret.setStartYearMonth(req.getStartYearMonth());
        ret.setEndYearMonth(req.getEndYearMonth());
        ret.setUnit(req.getUnit());
        ret.setProductCategory(req.getProductCategory());
        ret.setProduct(req.getProduct());
        ret.setVolumeType(req.getVolumeType());
        ret.setIsInner(req.getIsInner());

        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        ret.setCountryName(req.getCountryName());
        ret.setAreaName(req.getAreaName());
        ret.setRegionName(req.getRegionName());

        ret.setZoneCategory(req.getZoneCategory());
        ret.setInstanceCategory(req.getInstanceCategory());
        ret.setInstanceGroup(req.getInstanceGroup());
        ret.setInstanceType(req.getInstanceType());

        ret.setZoneName(req.getZoneName());
        ret.setDemandCaliber(req.getDemandCaliber());
        ret.setOrderRange(req.getOrderRange());
        ret.setNeedScale(false);
        ret.setExcludeFinishDemand(req.isExcludeFinishDemand());
        ret.setDims(req.getDims());
        ret.setDbStorageType(req.getDbStorageType());
        ret.setProjectType(req.getProjectType());
        return ret;
    }

    public static DemandReportReq transform(SimpleDemandReportReq req) {
        DemandReportReq ret = new DemandReportReq();
        ret.setStatTimeList(ListUtils.newArrayList(req.getStatTime()));
        ret.setStartYearMonth(req.getStartYearMonth());
        ret.setEndYearMonth(req.getEndYearMonth());
        ret.setNeedScale(false);
        ret.setDemandCaliber("人工校准");
        ret.setUnit("逻辑容量");
        ret.setOrderRange(req.getOrderRange());
        ret.setProductCategory(ProductCategoryEnum.CBS.getName());
        ret.setExcludeFinishDemand(req.isExcludeFinishDemand());
        ret.setDims(req.getDims());
        ret.setProjectType(ListUtils.newArrayList("头部项目","长尾模型预测"));
        return ret;
    }

    @Override
    public List<String> getScaleStatTime() {
        List<String> statTimeList = ListUtils.newArrayList();
        //版本当月
        String currentYearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        if (StringUtils.equals(currentYearMonth, this.getStartYearMonth())) {
            statTimeList.add(LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            String statTime = LocalDate.parse(this.getStartYearMonth() + "-01").minusDays(1).plusMonths(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
            statTimeList.add(statTime);
        }
        //月份的上一年
        LocalDate startDate = LocalDate.parse(this.getStartYearMonth() + "-01", DateTimeFormatter.ISO_LOCAL_DATE).minusYears(1);
        LocalDate endDate = LocalDate.parse(this.getEndYearMonth() + "-01", DateTimeFormatter.ISO_LOCAL_DATE).minusYears(1);
        while (!startDate.isAfter(endDate)) {
            statTimeList.add(startDate.with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE));
            startDate = startDate.plusMonths(1);
        }
        return statTimeList;
    }

}
