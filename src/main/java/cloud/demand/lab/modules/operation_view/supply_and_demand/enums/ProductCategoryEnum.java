package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/27 16:54
 */
@Getter
@AllArgsConstructor
public enum ProductCategoryEnum {

    CVM("CVM","CVM"),
    CBS("CBS","CBS"),
    DB("DATABASE","数据库"),
    COS("COS","COS"),
    GPU("GPU","GPU")
    ;
    private final String code;

    private final String name;


    public static ProductCategoryEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProductCategoryEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    public static ProductCategoryEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (ProductCategoryEnum value : values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ProductCategoryEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        ProductCategoryEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }
}
