package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.VersionData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.VersionReq;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/13 10:27
 */
public interface SupplyAndDemandDictService {

    boolean doDefinitive(VersionReq req);

    boolean checkDefinitive(String versionCode, String type, String productCategory);

    void insert(String versionCode, String startYearMonth, String endYearMonth, String type, String productCategory);

    List<VersionData> getAllVersion(VersionReq req);

    <T> void addUnclassified(T entity);

    String getDefinitiveVersion(VersionReq req);
}
