package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.model;

import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Cvm产品的售卖数据来自ClickHouse，通过这个DTO去获取
 */
@Data
public class ChCvmSaleDTO {

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "customhouseTitle")
    private String customhouseTitle;

    @Column(value = "areaName")
    private String areaName;

    @Column(value = "regionName")
    private String regionName;

    @Column(value = "zoneName")
    private String zoneName;

    @Column(value = "zoneId")
    private Integer zoneId;

    @Column(value = "ginsCategory")
    private String ginsCategory;

    @Column(value = "ginsFamily")
    private String ginsFamily;

    @Column(value = "ginsType")
    private String ginsType;

    @Column(value = "cpuCategory")
    private String cpuCategory;

    @Column(value = "saleCores")
    private BigDecimal saleCores;

    /**
     * 后填，通过机型族映射
     */
    private String deviceType;

    /**
     *  获取分组K
     */
    public String getGroupK(){
        String category = StringTools.isBlank(ginsCategory) || ginsCategory.length() < 2 ? "" : ginsCategory.substring(0, 2);
        return String.join("-",
                ProductTypeEnum.CVM.getCode(), category, customhouseTitle, areaName, regionName, zoneName, deviceType,
                cpuCategory);
    }

    public ReportOperationViewDetailDO toOperationViewDO(){
        ReportOperationViewDetailDO reportOperationViewDetailDO = new ReportOperationViewDetailDO();
        reportOperationViewDetailDO.setProductType(ProductTypeEnum.CVM.getCode());
        reportOperationViewDetailDO.setMaterialType(
                StringTools.isBlank(ginsCategory) || ginsCategory.length() < 2 ? "" : ginsCategory.substring(0, 2));
        reportOperationViewDetailDO.setCustomhouseTitle(customhouseTitle);
        reportOperationViewDetailDO.setAreaName(areaName);
        reportOperationViewDetailDO.setRegionName(regionName);
        reportOperationViewDetailDO.setZoneName(zoneName);
        reportOperationViewDetailDO.setDeviceType(deviceType);
        reportOperationViewDetailDO.setDeviceFamily(ginsFamily);
        reportOperationViewDetailDO.setCpuCategory(cpuCategory);
        return reportOperationViewDetailDO;
    }

    public static ChCvmSaleDTO mergeListSaleDTO(List<ChCvmSaleDTO> list){
        ChCvmSaleDTO result = new ChCvmSaleDTO();
        if (ListUtils.isEmpty(list)){
            return result;
        }else {
            ChCvmSaleDTO dto = list.get(0);
            result.setCustomhouseTitle(dto.getCustomhouseTitle());
            result.setAreaName(dto.getAreaName());
            result.setRegionName(dto.getRegionName());
            result.setZoneName(dto.getZoneName());
            result.setGinsCategory(dto.getGinsCategory());
            result.setGinsFamily(dto.getGinsFamily());
            result.setGinsType(dto.getGinsType());
            result.setDeviceType(dto.getDeviceType());
            result.setCpuCategory(dto.getCpuCategory());
        }
        result.setSaleCores(NumberUtils.sum(list, o -> o.getSaleCores()));
        return result;
    }

}

