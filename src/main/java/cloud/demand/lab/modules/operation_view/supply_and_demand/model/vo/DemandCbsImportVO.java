package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.common.excel.GroupConstant;
import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InnerFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/12 22:43
 */
@Data
@DotExcelEntity
public class DemandCbsImportVO implements ZoneCategoryFiller, ZoneInfoFiller, CountryNameFiller, InnerFiller {

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "版本号")
    private String statTime;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "需求年月")
    private String yearMonth;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "单据类型")
    private String dataSource;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "单号")
    private String billNumber;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "产品")
    private String product;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "行业部门")
    private String industryDept;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "战区")
    private String warZone;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "客户简称")
    private String commonCustomerShortName;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "客户UIN")
    private String customerUin;

    private Integer isInner;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "项目名称")
    private String projectName;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "需求场景")
    private String demandScene;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "需求类型")
    private String demandType;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "项目类型")
    private String projectType;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "境内外")
    private String customhouseTitle;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "国家")
    private String countryName;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "区域")
    private String areaName;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "地域")
    private String regionName;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "园区类型")
    private String zoneCategory;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "可用区")
    private String zoneName;
    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "SSD/高性能")
    private String volumeType;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "数据盘/系统盘")
    private String diskType;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "开始购买时间")
    private String beginBuyDate;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "结束购买时间")
    private String endBuyDate;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "云盘容量(TB)")
    private BigDecimal totalAmount;


    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "未履约容量(TB)")
    private BigDecimal waitBuyAmount;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "履约容量(TB)")
    private BigDecimal buyAmount;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "干预备注")
    private String remark;

    @DotExcelField(group = GroupConstant.PPL_AND_ORDER_CBS_DEMAND, excelColumnName = "是否人工干预")
    private String intervene;

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideZoneName() {
        return this.zoneName;
    }

    @Override
    public void fillZone(String zone) {

    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.customhouseTitle = customhouseTitle;
    }

    @Override
    public void fillRegionName(String regionName) {
        this.regionName = regionName;
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }

    @Override
    public String provideUin() {
        return this.customerUin;
    }

    @Override
    public String provideIndustryDept() {
        return this.industryDept;
    }

    @Override
    public void fillIsInner(Integer isInner) {
        this.isInner = isInner;
    }
}
