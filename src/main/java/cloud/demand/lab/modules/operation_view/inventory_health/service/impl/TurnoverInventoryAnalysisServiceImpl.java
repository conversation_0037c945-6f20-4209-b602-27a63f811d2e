package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.NotPreDeductInventoryData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.NotPreDeductInventoryReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.PreDeductAnalysisReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.PreDeductTurnoverData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryAnalysisData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryAnalysisReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryCustomerData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryTodayData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryTodayReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsIndustryCockpitV3WithholdDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsNewTurnoverInventoryDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.AppRoleEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.WithholdDurationEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.TurnoverInventoryAnalysisService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryDisassembleServiceImpl.TurnoverInventoryData;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.order.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class TurnoverInventoryAnalysisServiceImpl implements TurnoverInventoryAnalysisService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    OutsideViewOldService outsideViewOldService;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<TurnoverInventoryAnalysisData> queryTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req) {
        InventoryDisassembleServiceImpl bean = SpringUtil.getBean(InventoryDisassembleServiceImpl.class);
        WhereSQL condition = req.genBasicCondition();
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            condition.and("area_name in (?)", req.getAreaName());
        }
        InventoryDisassembleReq turnReq = new InventoryDisassembleReq();
        BeanUtils.copyProperties(req, turnReq);
        Map<String, TurnoverInventoryData> turnoverData = bean.getTurnoverData(condition, turnReq);
        List<TurnoverInventoryAnalysisData> result = new ArrayList<>();
        //获取地域信息
        List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zones, o -> o.getZoneName(), o -> o);
        for (Entry<String, TurnoverInventoryData> entry : turnoverData.entrySet()) {
            TurnoverInventoryAnalysisData item = new TurnoverInventoryAnalysisData();
            TurnoverInventoryData value = entry.getValue();
            item.setStatTime(value.getStatTime());
            item.setZoneName(value.getZoneName());
            item.setInstanceType(value.getInstanceType());
            StaticZoneDO staticZoneDO = zoneMap.get(value.getZoneName());
            if (staticZoneDO != null) {
                item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                item.setRegionName(staticZoneDO.getRegionName());
                item.setAreaName(staticZoneDO.getAreaName());
            }
            List<String> zoneAndInstanceCategory = getZoneAndInstanceCategory(zoneConfigList, insConfigList,
                    item.getZoneName(), item.getInstanceType(), item.getCustomhouseTitle());
            item.setZoneCategory(zoneAndInstanceCategory.get(0));
            item.setInstanceCategory(zoneAndInstanceCategory.get(1));
            item.setTotalTurnoverInventory(value.getTurnoverInventory());
            item.setNotPreDeductTurnoverInventory(value.getNotPreDeductTurnoverCores());
            item.setDeductTurnoverInventory(value.getPreDeductInventory());
            result.add(item);
        }
        return result;
    }

    @Override
    public List<TurnoverInventoryCustomerData> queryTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req) {
        //预扣周转用户角度
        WhereSQL condition = req.genBasicCondition();
        condition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        if (ListUtils.isNotEmpty(req.getIndustryDept())) {
            condition.and("industry_dept in (?)", req.getIndustryDept());
        }
        if (ListUtils.isNotEmpty(req.getWarZone())) {
            condition.and("war_zone in (?)", req.getWarZone());
        }
        if (ListUtils.isNotEmpty(req.getCustomerShortName())) {
            condition.and("customer_short_name in (?)", req.getCustomerShortName());
        }
        if (ListUtils.isNotEmpty(req.getAppId())) {
            condition.and("app_id in (?)", req.getAppId());
        }
        if (ListUtils.isNotEmpty(req.getUin())) {
            condition.and("uin in (?)", req.getUin());
        }
        List<String> appRole = req.getAppRole();
        if (ListUtils.isNotEmpty(req.getAppRole())) {
            if (appRole.contains("其他")) {
                List<String> notInAppRoleList = AppRoleEnum.getWithoutOther().stream().map(AppRoleEnum::getName).collect(
                        Collectors.toList());
                appRole.removeIf(e -> e.equals("其他"));
                condition.and(" app_role not in (?) or app_role in (?) ", notInAppRoleList, appRole);
            } else {
                condition.and(" app_role in (?) ", appRole);
            }
        }
        condition.and("biz_type = ?", "cvm");

        String field = "select stat_time, zone_name, region_name, customhouse_title, instance_type, industry_dept, war_zone, customer_short_name, app_id, uin, sum(cpu_count) as sum ";
        String groupBy = "stat_time, region_name, zone_name, customhouse_title, instance_type, industry_dept, war_zone, customer_short_name, app_id, uin";
        condition.addGroupBy(groupBy);
        String sql = field + " from dws_industry_cockpit_v3_withhold_df" + condition.getSQL();

        List<CustomerPreDeductData> preAll = ckcldStdCrpDBHelper.getRaw(CustomerPreDeductData.class,
                sql, condition.getParams());
        List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
        //筛选区域条件
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            Map<String, String> zoneToAreaMap = ListUtils.toMap(zones, StaticZoneDO::getZoneName,
                    StaticZoneDO::getAreaName);
            HashSet<String> areaSet = new HashSet<>(req.getAreaName());
            preAll = preAll.stream().filter(
                    o -> areaSet.contains(zoneToAreaMap.get(o.getZoneName()))).collect(Collectors.toList());
        }
        //未预扣周转
        NotPreDeductInventoryReq notReq = new NotPreDeductInventoryReq();
        BeanUtils.copyProperties(req, notReq);
        List<NotPreDeductInventoryData> notPreList = queryNotPreDeductInventoryReport(notReq);

        //筛选机型族
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            preAll = preAll.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
            notPreList = notPreList.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zones, o -> o.getZoneName(), o -> o);
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, List<CustomerPreDeductData>> preMapList = ListUtils.toMapList(preAll,
                o -> String.join("@", o.getStatTime(), o.getInstanceType(), o.getZoneName(),
                        o.getIndustryDept(), o.getCustomerShortName(), String.valueOf(o.getUin())), o -> o);
        Map<String, List<NotPreDeductInventoryData>> notPreMapList = ListUtils.toMapList(notPreList,
                o -> String.join("@", o.getStatTime(), o.getInstanceType(), o.getZoneName(),
                        o.getIndustryDept(), o.getCustomerShortName(), o.getCustomerShortName()), o -> o);
        Set<String> keySet = new HashSet<>();
        if (ListUtils.isNotEmpty(preMapList.keySet())) {
            keySet.addAll(preMapList.keySet());
        }
        if (ListUtils.isNotEmpty(notPreMapList.keySet())) {
            keySet.addAll(notPreMapList.keySet());
        }
        List<TurnoverInventoryCustomerData> result = new ArrayList<>();
        for (String key : keySet) {
            List<CustomerPreDeductData> preList = preMapList.get(key);
            List<NotPreDeductInventoryData> noPreList = notPreMapList.get(key);
            TurnoverInventoryCustomerData item = new TurnoverInventoryCustomerData();
            if (ListUtils.isNotEmpty(preList)) {
                item.setStatTime(preList.get(0).getStatTime());
                item.setInstanceType(preList.get(0).getInstanceType());
                item.setZoneName(preList.get(0).getZoneName());
                item.setRegionName(preList.get(0).getRegionName());
                item.setCustomhouseTitle(preList.get(0).getCustomhouseTitle());
                item.setCustomerShortName(preList.get(0).getCustomerShortName());
                item.setUin(String.valueOf(preList.get(0).getUin()));
                item.setIndustryDept(preList.get(0).getIndustryDept());
                item.setWarZone(preList.get(0).getWarZone());
                item.setAppId(String.valueOf(preList.get(0).getAppId()));
            }else if (ListUtils.isNotEmpty(noPreList)) {
                item.setStatTime(noPreList.get(0).getStatTime());
                item.setInstanceType(noPreList.get(0).getInstanceType());
                item.setZoneName(noPreList.get(0).getZoneName());
                item.setRegionName(noPreList.get(0).getRegionName());
                item.setCustomhouseTitle(noPreList.get(0).getCustomhouseTitle());
                item.setCustomerShortName(noPreList.get(0).getCustomerShortName());
                item.setUin(noPreList.get(0).getUin());
                item.setIndustryDept(noPreList.get(0).getIndustryDept());
                item.setWarZone(noPreList.get(0).getWarZone());
                item.setAppId(String.valueOf(noPreList.get(0).getAppId()));
            }
            StaticZoneDO staticZoneDO = zoneMap.get(item.getZoneName());
            if (staticZoneDO != null) {
                item.setAreaName(staticZoneDO.getAreaName());
            }
            List<String> zoneAndInstanceCategory = getZoneAndInstanceCategory(zoneConfigList, insConfigList,
                    item.getZoneName(), item.getInstanceType(), item.getCustomhouseTitle());
            item.setZoneCategory(zoneAndInstanceCategory.get(0));
            item.setInstanceCategory(zoneAndInstanceCategory.get(1));
            item.setNotPreDeductTurnoverInventory(NumberUtils.sum(noPreList,
                    NotPreDeductInventoryData::getNotPreTurnover).intValue());
            item.setDeductTurnoverInventory(NumberUtils.sum(preList, CustomerPreDeductData::getSum).intValue());
            item.setTotalTurnoverInventory(item.getNotPreDeductTurnoverInventory() + item.getDeductTurnoverInventory());
            result.add(item);
        }
        return result;
    }

    /**
     *用户预扣周转分析
     */
    @Override
    public List<PreDeductTurnoverData> queryPreDeductInventoryReport(PreDeductAnalysisReq req) {
        WhereSQL condition = req.genBasicCondition();
        List<String> appRole = req.getAppRole();
        if (ListUtils.isNotEmpty(req.getAppRole())) {
            if (appRole.contains("其他")) {
                List<String> notInAppRoleList = AppRoleEnum.getWithoutOther().stream().map(AppRoleEnum::getName).collect(
                        Collectors.toList());
                appRole.removeIf(e -> e.equals("其他"));
                condition.and(" app_role not in (?) or app_role in (?) ", notInAppRoleList, appRole);
            } else {
                condition.and(" app_role in (?) ", appRole);
            }
        }
        condition.and("biz_type = ?", "cvm");
        List<DwsIndustryCockpitV3WithholdDfDO> all = ckcldStdCrpDBHelper.getAll(DwsIndustryCockpitV3WithholdDfDO.class,
                condition.getSQL(), condition.getParams());
        //筛选区域条件
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
            Map<String, String> zoneToAreaMap = ListUtils.toMap(zones, StaticZoneDO::getZoneName,
                    StaticZoneDO::getAreaName);
            HashSet<String> areaSet = new HashSet<>(req.getAreaName());
            all = all.stream().filter(
                    o -> areaSet.contains(zoneToAreaMap.get(o.getZoneName()))).collect(Collectors.toList());
        }

        //筛选机型族
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zones, StaticZoneDO::getZoneName, o -> o);
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, List<DwsIndustryCockpitV3WithholdDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getIndustryDept(), o.getWarZone(),
                        o.getCustomerShortName(), String.valueOf(o.getUin()), String.valueOf(o.getAppId())), o -> o);
        List<PreDeductTurnoverData> result = new ArrayList<>();
        for (Entry<String, List<DwsIndustryCockpitV3WithholdDfDO>> entry : mapList.entrySet()) {
            PreDeductTurnoverData item = new PreDeductTurnoverData();
            List<DwsIndustryCockpitV3WithholdDfDO> value = entry.getValue();
            item.setStatTime(req.getStatTime());
            item.setInstanceType(value.get(0).getInstanceType());
            item.setZoneName(value.get(0).getZoneName());
            StaticZoneDO staticZoneDO = zoneMap.get(item.getZoneName());
            if (staticZoneDO != null) {
                item.setAreaName(staticZoneDO.getAreaName());
                item.setRegionName(staticZoneDO.getRegionName());
                item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
            }
            List<String> zoneAndInstanceCategory = getZoneAndInstanceCategory(zoneConfigList, insConfigList,
                    item.getZoneName(), item.getInstanceType(), item.getCustomhouseTitle());
            item.setZoneCategory(zoneAndInstanceCategory.get(0));
            item.setInstanceCategory(zoneAndInstanceCategory.get(1));
            item.setIndustryDept(value.get(0).getIndustryDept());
            item.setWarZone(value.get(0).getWarZone());
            item.setCustomerShortName(value.get(0).getCustomerShortName());
            item.setUin(String.valueOf(value.get(0).getUin()));
            item.setAppId(String.valueOf(value.get(0).getAppId()));
            item.setAppRole(value.get(0).getAppRole());
            //预扣时长分布
            Map<String, List<DwsIndustryCockpitV3WithholdDfDO>> withHoldMap = ListUtils.toMapList(value,
                    o -> String.valueOf(o.getWithholdDuration()), o -> o);
            Set<String> duration = withHoldMap.keySet();
            if (duration.contains(WithholdDurationEnum.LE_TWO_WEEK.getCode())) {
                List<DwsIndustryCockpitV3WithholdDfDO> list = withHoldMap.get(
                        WithholdDurationEnum.LE_TWO_WEEK.getCode());
                item.setZeroToTwo(NumberUtils.sum(list, DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());
            }
            if (duration.contains(WithholdDurationEnum.GT_TWO_WEEK_LT_FIVE_WEEK.getCode())) {
                List<DwsIndustryCockpitV3WithholdDfDO> list = withHoldMap.get(
                        WithholdDurationEnum.GT_TWO_WEEK_LT_FIVE_WEEK.getCode());
                item.setTwoToFive(NumberUtils.sum(list, DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());
            }
            if (duration.contains(WithholdDurationEnum.GE_FIVE_WEEK.getCode())) {
                List<DwsIndustryCockpitV3WithholdDfDO> list = withHoldMap.get(
                        WithholdDurationEnum.GE_FIVE_WEEK.getCode());
                item.setBiggerThanFive(NumberUtils.sum(list, DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());
            }

            //预扣类型分布
            item.setTotalPreDeduct(NumberUtils.sum(value, DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());
            item.setBufferPreDeduct(NumberUtils.sum(value.stream().filter(o -> o.getReserveMode().equals("弹性预扣")).collect(
                    Collectors.toList()), DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());
            item.setNormalPreDeduct(NumberUtils.sum(value.stream().filter(o -> o.getReserveMode().equals("普通预扣")).collect(
                    Collectors.toList()), DwsIndustryCockpitV3WithholdDfDO::getCpuCount).intValue());

            result.add(item);

        }
        return result;
    }

    /**
     * 未预扣周转库存分析
     */
    @Override
    public List<NotPreDeductInventoryData> queryNotPreDeductInventoryReport(NotPreDeductInventoryReq req) {

        //获取库存层面的未预扣周转库存
        WhereSQL condition = req.genBasicCondition();
        condition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            condition.and("area_name in (?)", req.getAreaName());
        }
        List<DwsNewTurnoverInventoryDataDfDO> notPreList = ckcldDBHelper.getAll(DwsNewTurnoverInventoryDataDfDO.class,
                condition.getSQL(), condition.getParams());
        //获取订单信息
        WhereSQL customerCondition = req.genCustomerCondition();
        customerCondition.and(req.genBasicCondition());
        customerCondition.and("order_node_code_name not in ('订单取消','订单关闭')");
        List<DwdCrpPplOrderItemAndInfoCfDO> orderList = ckcldStdCrpDBHelper.getAll(DwdCrpPplOrderItemAndInfoCfDO.class,
                customerCondition.getSQL(), customerCondition.getParams());

        //筛选机型族
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            notPreList = notPreList.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
            orderList = orderList.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }

        List<StaticZoneDO> zones = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zones, o -> o.getZoneName(), o -> o);
        //筛选区域
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            Map<String, String> zoneToAreaMap = ListUtils.toMap(zones, StaticZoneDO::getZoneName,
                    StaticZoneDO::getAreaName);
            HashSet<String> areaSet = new HashSet<>(req.getAreaName());
            orderList = orderList.stream().filter(
                    o -> areaSet.contains(zoneToAreaMap.get(o.getZoneName()))).collect(Collectors.toList());
        }

        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, List<DwsNewTurnoverInventoryDataDfDO>> notPreMap = ListUtils.toMapList(notPreList,
                o -> String.join("@", o.getStatTime(), o.getInstanceType(), o.getZoneName()), o -> o);
        Map<String, List<DwdCrpPplOrderItemAndInfoCfDO>> tempOrderMap = ListUtils.toMapList(orderList,
                o -> String.join("@", o.getInstanceType(), o.getZoneName()), o -> o);
        //将未预扣周转库存通过订单均分
        List<NotPreDeductInventoryData> result = new ArrayList<>();
        for (Entry<String, List<DwsNewTurnoverInventoryDataDfDO>> entry : notPreMap.entrySet()) {
            List<DwsNewTurnoverInventoryDataDfDO> value = entry.getValue();
            String key = String.join("@", value.get(0).getInstanceType(), value.get(0).getZoneName());
            List<DwdCrpPplOrderItemAndInfoCfDO> orderItemAndInfoCfDOList = tempOrderMap.get(key);
            if (ListUtils.isEmpty(orderItemAndInfoCfDOList)) {
                NotPreDeductInventoryData item = new NotPreDeductInventoryData();
                item.setStatTime(value.get(0).getStatTime());
                item.setZoneName(value.get(0).getZoneName());
                item.setInstanceType(value.get(0).getInstanceType());
                item.setRegionName(value.get(0).getRegionName());
                item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
                item.setAreaName(value.get(0).getAreaName());
                item.setNotPreTurnover(NumberUtils.sum(value,
                        DwsNewTurnoverInventoryDataDfDO::getNotPreDeductTurnoverCores).intValue());
                item.setNotPreActual(NumberUtils.sum(value, DwsNewTurnoverInventoryDataDfDO::getNotPreActualInventory).intValue());
                item.setTotalCores(NumberUtils.sum(value, DwsNewTurnoverInventoryDataDfDO::getTotalCores).intValue());
                item.setPreActual(NumberUtils.sum(value, DwsNewTurnoverInventoryDataDfDO::getActualPreDeductCores).intValue());
                item.setAwaitForInventory(NumberUtils.sum(value, DwsNewTurnoverInventoryDataDfDO::getAwaitForInventoryCores).intValue());
                List<String> zoneAndInstanceCategory = getZoneAndInstanceCategory(zoneConfigList, insConfigList,
                        item.getZoneName(), item.getInstanceType(), item.getCustomhouseTitle());
                item.setZoneCategory(zoneAndInstanceCategory.get(0));
                item.setInstanceCategory(zoneAndInstanceCategory.get(1));
                result.add(item);
            }else {
                Map<String, List<DwdCrpPplOrderItemAndInfoCfDO>> mapList = ListUtils.toMapList(orderItemAndInfoCfDOList,
                        DwdCrpPplOrderItemAndInfoCfDO::getOrderNumber, o -> o);
                int total = mapList.size();
                Map<String, List<DwdCrpPplOrderItemAndInfoCfDO>> deptMap = ListUtils.toMapList(orderItemAndInfoCfDOList,
                        o -> String.join("@", o.getIndustryDept()), o -> o);
                for (Entry<String, List<DwdCrpPplOrderItemAndInfoCfDO>> orderEntry : deptMap.entrySet()) {
                    List<DwdCrpPplOrderItemAndInfoCfDO> tempValue = orderEntry.getValue();
                    Map<String, List<DwdCrpPplOrderItemAndInfoCfDO>> numberList = ListUtils.toMapList(tempValue,
                            DwdCrpPplOrderItemAndInfoCfDO::getOrderNumber, o -> o);
                    for (Entry<String, List<DwdCrpPplOrderItemAndInfoCfDO>> numberEntry : numberList.entrySet()) {
                        NotPreDeductInventoryData item = new NotPreDeductInventoryData();
                        item.setStatTime(value.get(0).getStatTime());
                        item.setZoneName(numberEntry.getValue().get(0).getZoneName());
                        item.setInstanceType(numberEntry.getValue().get(0).getInstanceType());
                        item.setRegionName(numberEntry.getValue().get(0).getRegionName());
                        StaticZoneDO staticZoneDO = zoneMap.get(item.getZoneName());
                        if (staticZoneDO != null) {
                            item.setAreaName(staticZoneDO.getAreaName());
                            item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                        }
                        List<String> zoneAndInstanceCategory = getZoneAndInstanceCategory(zoneConfigList, insConfigList,
                                item.getZoneName(), item.getInstanceType(), item.getCustomhouseTitle());
                        item.setZoneCategory(zoneAndInstanceCategory.get(0));
                        item.setInstanceCategory(zoneAndInstanceCategory.get(1));
                        item.setIndustryDept(numberEntry.getValue().get(0).getIndustryDept());
                        item.setOrderNumber(numberEntry.getValue().get(0).getOrderNumber());
                        item.setWarZone(numberEntry.getValue().get(0).getWarZone());
                        item.setAppId(numberEntry.getValue().get(0).getAppId());
                        item.setOrderNodeCodeName(numberEntry.getValue().get(0).getOrderNodeCodeName());
                        item.setCustomerShortName(numberEntry.getValue().get(0).getCustomerShortName());
                        item.setUin(numberEntry.getValue().get(0).getCustomerUin());
                        item.setBeginBuyDate(DateUtils.format(numberEntry.getValue().get(0).getBeginBuyDate()));
                        item.setNotPreTurnover(NumberUtils.sum(value,
                                DwsNewTurnoverInventoryDataDfDO::getNotPreDeductTurnoverCores).intValue() / total);
                        item.setNotPreActual(NumberUtils.sum(value,
                                DwsNewTurnoverInventoryDataDfDO::getNotPreActualInventory).intValue() / total);
                        item.setTotalCores(NumberUtils.sum(value,
                                DwsNewTurnoverInventoryDataDfDO::getTotalCores).intValue() / total);
                        item.setPreActual(NumberUtils.sum(value,
                                DwsNewTurnoverInventoryDataDfDO::getActualPreDeductCores).intValue() / total);
                        item.setAwaitForInventory(NumberUtils.sum(value,
                                DwsNewTurnoverInventoryDataDfDO::getAwaitForInventoryCores).intValue() / total);
                        result.add(item);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public TurnoverInventoryTodayData queryTurnoverAnalysisReportToday(TurnoverInventoryTodayReq req) {
        TurnoverInventoryAnalysisReq tempReq = new TurnoverInventoryAnalysisReq();
        BeanUtils.copyProperties(req, tempReq);
        LocalDate yesterday = DateUtils.yesterday();
        tempReq.setStart(DateUtils.formatDate(yesterday));
        tempReq.setEnd(DateUtils.formatDate(yesterday));
        List<TurnoverInventoryCustomerData> dataList = queryTurnoverAnalysisReportCustomer(
                tempReq);
        TurnoverInventoryTodayData result = new TurnoverInventoryTodayData();
        result.setPreDeductTurnover(NumberUtils.sum(dataList,
                TurnoverInventoryAnalysisData::getDeductTurnoverInventory).intValue());
        result.setNotPreDeductTurnover(NumberUtils.sum(dataList,
                TurnoverInventoryAnalysisData::getNotPreDeductTurnoverInventory).intValue());
        result.setTotalTurnoverInventory(NumberUtils.sum(dataList,
                TurnoverInventoryAnalysisData::getTotalTurnoverInventory).intValue());
        return result;
    }

    @Override
    public DownloadBean exportTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req) {
        List<TurnoverInventoryAnalysisData> data = queryTurnoverAnalysisReportInventory(req);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/turnover_detail_inventory.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "周转明细分析" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public DownloadBean exportTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req) {
        List<TurnoverInventoryCustomerData> data = queryTurnoverAnalysisReportCustomer(req);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/turnover_detail_customer.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "周转明细分析" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public DownloadBean exportPreDeductInventoryReport(PreDeductAnalysisReq req) {
        List<PreDeductTurnoverData> data = queryPreDeductInventoryReport(req);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/turnover_detail_customer.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "用户预扣周转分析" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public DownloadBean exportNotPreDeductInventoryReport(NotPreDeductInventoryReq req) {
        List<NotPreDeductInventoryData> data = queryNotPreDeductInventoryReport(req);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/turnover_detail_customer.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "未预扣周转分析" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    public List<String> getZoneAndInstanceCategory(List<InventoryHealthMainZoneNameConfigDO> zoneConfigList, List<InventoryHealthMainInstanceTypeConfigDO> insConfigList, String zoneName, String instanceType, String customhouseTitle) {
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap = ListUtils.toMap(zoneConfigList, o -> o.getZoneName(),
                o -> o);
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> insConfigMap = ListUtils.toMapList(insConfigList,
                o -> o.getInstanceType(), o -> o);
        InventoryHealthMainZoneNameConfigDO zoneConfig = zoneConfigMap.get(zoneName);
        String zoneCategory;
        String insCategory;
        if (zoneConfig != null) {
            zoneCategory = zoneConfig.getTypeName();
        }else {
            zoneCategory = "未分类";
        }
        List<InventoryHealthMainInstanceTypeConfigDO> insConfig = insConfigMap.get(instanceType);
        if (ListUtils.isNotEmpty(insConfig)) {
            if (customhouseTitle.equals("境外")) {
                List<String> collect = insConfig.stream().map(
                        InventoryHealthMainInstanceTypeConfigDO::getRegionType).collect(Collectors.toList());
                if (collect.contains("海外")) {
                    customhouseTitle = "海外";
                }
            }
            Map<String, InventoryHealthMainInstanceTypeConfigDO> map = ListUtils.toMap(insConfig,
                    o -> o.getRegionType(), o -> o);
            InventoryHealthMainInstanceTypeConfigDO temp = map.get(customhouseTitle);
            if (temp != null) {
                insCategory = temp.getType2Name();
            }else {
                insCategory = "未分类";
            }
        }else {
            insCategory = "未分类";
        }
        return Arrays.asList(zoneCategory, insCategory);
    }

    @Data
    public static class CustomerPreDeductData {

        @Column("stat_time")
        String statTime;

        @Column("zone_name")
        String zoneName;

        @Column("region_name")
        String RegionName;

        @Column("customhouse_title")
        String customhouseTitle;

        @Column("instance_type")
        String instanceType;

        @Column("industry_dept")
        String industryDept;

        @Column("war_zone")
        String warZone;

        @Column("customer_short_name")
        String customerShortName;

        @Column("app_id")
        String appId;

        @Column("uin")
        String uin;

        @Column("sum")
        Integer sum;


    }
}
