package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;


import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.common.excel.DotExcelReadUtil;
import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.excel.checker.NotBlankColumnChecker;
import cloud.demand.lab.common.excel.checker.NotInStringListColumnChecker;
import cloud.demand.lab.common.excel.core.ReadResult;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.modules.common_dict.DO.StaticGinsfamilyDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthBufferConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthServiceLevelConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthExcelGroupEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType2;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.YunXiaoUtil;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.BaseDTO;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.InstanceTypeFullDTO;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.ZoneFullDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InventoryHealthConfigServiceImpl implements InventoryHealthConfigService {
    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper cdCommonDbHelper;

    @Resource
    TaskLogService taskLogService;

    @Override
    @TaskLog(taskName = "syncMainZoneNameConfig")
    public void syncMainZoneNameConfig(Boolean isInitial) {
        // 增量同步
        List<InventoryHealthMainZoneNameConfigDO> configDOS = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(new Date()));
        Map<String, InventoryHealthMainZoneNameConfigDO> configDOMap = ListUtils.toMap(configDOS, InventoryHealthMainZoneNameConfigDO::getZoneName, o -> o);
        // 初始化主力可用区，从云霄获取可用区数据
        List<ZoneFullDTO> zoneList = YunXiaoUtil.queryAllZoneFull();
        // 获取所有在售非主力可用区
        String sql = "select distinct zone_name from inventory_health_not_main_zone_name where deleted = 0";
        List<String> notMainZoneNames = demandDBHelper.getRaw(String.class, sql);

        // 数据转成内部结构，并设置默认可用区类型
        List<InventoryHealthMainZoneNameConfigDO> inventoryHealthMainZoneNameConfigDOS = zoneList.stream().map(zoneFull -> {
            InventoryHealthMainZoneNameConfigDO configDO = configDOMap.get(zoneFull.getZoneName());

            if (configDO != null && zoneFull.getSalesStrategy() != null && configDO.getType() != zoneFull.getSalesStrategy()) {
                configDO.setType(zoneFull.getSalesStrategy());
                configDO.setTypeName(InventoryHealthZoneType.getNameFromCode(configDO.getType()));
                return configDO;
            } else if (configDO != null) {
                return null;
            }

            InventoryHealthMainZoneNameConfigDO inventoryHealthMainZoneNameConfigDO = new InventoryHealthMainZoneNameConfigDO();
            inventoryHealthMainZoneNameConfigDO.setRegionType(zoneFull.getCustomhouse());
            inventoryHealthMainZoneNameConfigDO.setAreaName(zoneFull.getArea());
            // 清洗 regionName，从华南地区(广州)中截取"广州"
            String regionName = zoneFull.getRegionName().replace(zoneFull.getArea() + "(", "");
            regionName = regionName.replace(")", "");
            inventoryHealthMainZoneNameConfigDO.setRegionName(regionName);
            inventoryHealthMainZoneNameConfigDO.setZoneName(zoneFull.getZoneName());
            inventoryHealthMainZoneNameConfigDO.setZone(zoneFull.getZone());

            if (zoneFull.getSalesStrategy() != null) {
                inventoryHealthMainZoneNameConfigDO.setType(zoneFull.getSalesStrategy());
                inventoryHealthMainZoneNameConfigDO.setTypeName(InventoryHealthZoneType.getNameFromCode(zoneFull.getSalesStrategy()));
            } else {
                if (zoneFull.getMainZone()) {
                    inventoryHealthMainZoneNameConfigDO.setType(InventoryHealthZoneType.PRINCIPAL.getCode());
                    inventoryHealthMainZoneNameConfigDO.setTypeName(InventoryHealthZoneType.PRINCIPAL.getName());
                } else if (notMainZoneNames.contains(zoneFull.getZoneName())) {
                    inventoryHealthMainZoneNameConfigDO.setType(InventoryHealthZoneType.SECONDARY.getCode());
                    inventoryHealthMainZoneNameConfigDO.setTypeName(InventoryHealthZoneType.SECONDARY.getName());
                } else if (isInitial) {
                    inventoryHealthMainZoneNameConfigDO.setType(InventoryHealthZoneType.WITHDRAWING.getCode());
                    inventoryHealthMainZoneNameConfigDO.setTypeName(InventoryHealthZoneType.WITHDRAWING.getName());
                } else {
                    inventoryHealthMainZoneNameConfigDO.setType(InventoryHealthZoneType.SPECIAL.getCode());
                    inventoryHealthMainZoneNameConfigDO.setTypeName(InventoryHealthZoneType.SPECIAL.getName());
                }
            }

            inventoryHealthMainZoneNameConfigDO.setDate(new Date());
            return inventoryHealthMainZoneNameConfigDO;
        }).filter(o -> o != null).collect(Collectors.toList());

        log.info(inventoryHealthMainZoneNameConfigDOS.toString());
        demandDBHelper.insertOrUpdate(inventoryHealthMainZoneNameConfigDOS);
//        demandDBHelper.insertBatchWithoutReturnId(inventoryHealthMainZoneNameConfigDOS);
    }

    @Override
    @TaskLog(taskName = "syncMainInstanceTypeConfig")
    public void syncMainInstanceTypeConfig(Boolean isInitial) {
        // 增量同步
        List<InventoryHealthMainInstanceTypeConfigDO> configDOS = demandDBHelper.getAll(
                InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(new Date()));
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> configDOMap = ListUtils.toMapList(configDOS, InventoryHealthMainInstanceTypeConfigDO::getInstanceType, o -> o);
        // 初始化主力实例类型，从云霄获取实例类型数据
        List<InstanceTypeFullDTO> instanceTypeFullDTOS = YunXiaoUtil.queryAllInstanceTypeFull();
        // 获取所有在售非主力机型
        String sql = "select distinct instance_type from inventory_health_not_main_instance_type where deleted = 0";
        List<String> notMainInstanceTypes = demandDBHelper.getRaw(String.class, sql);
        // 从 metric 获取机型配置表，不在机型配置表中的机型会被忽略。过滤"历史遗留"机型
        List<StaticGinsfamilyDO> staticGinsfamilyDOS = cdCommonDbHelper.getAll(StaticGinsfamilyDO.class).stream().filter(o -> {
            return !o.getGinsfamilyName().contains("历史遗留");
        }).collect(Collectors.toList());
        Map<String, StaticGinsfamilyDO> staticGinsfamilyDOSMap = ListUtils.toMap(staticGinsfamilyDOS, StaticGinsfamilyDO::getGinsfamily, o -> o);

        Function<String, String> getProductName = ins -> {
            StaticGinsfamilyDO staticGinsfamilyDO = staticGinsfamilyDOSMap.get(ins);
            if (staticGinsfamilyDO != null) {
                if (staticGinsfamilyDO.getGpuChipPerCard() > 0) {
                    return "GPU";
                } else if (staticGinsfamilyDO.getBiztype().equals("cvm")) {
                    return "CVM";
                } else if (staticGinsfamilyDO.getBiztype().equals("baremetal")) {
                    return "裸金属";
                }
            }

            return "未知";
        };


        List<InventoryHealthMainInstanceTypeConfigDO> mainInstanceTypeConfigDOS = instanceTypeFullDTOS.stream().map(ins -> {
            List<InventoryHealthMainInstanceTypeConfigDO> configDO = configDOMap.get(ins.getInstanceFamily());

            Function<InventoryHealthMainInstanceTypeConfigDO, Object> setType2 = (InventoryHealthMainInstanceTypeConfigDO inventoryHealthMainZoneNameConfigDO) -> {
                // 忽略
                if (ins.getState() == null) {
                    inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.EOL.getCode());
                    inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.EOL.getName());
//                    taskLogService.genRunWarnLog("syncMainInstanceTypeConfig", "syncMainInstanceTypeConfig", "云霄同步的实例类型 " + ins.getInstanceFamily() + " 的主力非主力状态为 null，已被忽略");
                    return null;
                }

                if (ins.getState().equals(InventoryHealthInstanceFamilyType.PRINCIPAL.getCode())) {
                    inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.PRINCIPAL.getCode());
                    inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.PRINCIPAL.getName());
                } else if (notMainInstanceTypes.contains(ins.getInstanceFamily())) {
                    inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.ON_SALE.getCode());
                    inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.ON_SALE.getName());
                } else if (ins.getState().equals(InventoryHealthInstanceFamilyType.OTHER.getCode())) {
                    if (isInitial) {
                        inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.ON_SALE.getCode());
                        inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.ON_SALE.getName());
                    } else {
                        inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.NEW.getCode());
                        inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.NEW.getName());
                    }
                } else {
                    inventoryHealthMainZoneNameConfigDO.setType2(InventoryHealthInstanceFamilyType2.EOL.getCode());
                    inventoryHealthMainZoneNameConfigDO.setType2Name(InventoryHealthInstanceFamilyType2.EOL.getName());
                }

                return null;
            };

            // 删除在metric配置表中不存在的机型，以及存在并且名称包含"历史遗留"的机型
            if (configDO != null) {
                configDO.stream().forEach(c -> {
                    StaticGinsfamilyDO staticGinsfamilyDO = staticGinsfamilyDOSMap.get(c.getInstanceType());

                    if (staticGinsfamilyDO == null || staticGinsfamilyDO.getGinsfamilyName().contains("历史遗留")) {
                        c.setDeleted(true);
                    }
                });
            }

            Function<InventoryHealthMainInstanceTypeConfigDO, Boolean> setProduct = c -> {
                String productName = getProductName.apply(c.getInstanceType());
                String preProductName = c.getProduct();

                if (preProductName == null || !preProductName.equals(productName)) {
                    c.setProduct(productName);
                    return true;
                }

                return false;
            };

            if (configDO != null && !configDO.isEmpty()) {
                return configDO.stream().filter(c -> {
                    boolean isChanged = false;

                    if (ins.getState() != null && !c.getType1().equals(ins.getState())) {
                        c.setType1(ins.getState());
                        c.setType1Name(InventoryHealthInstanceFamilyType.getNameFromCode(c.getType1()));

                        setType2.apply(c);
                        isChanged = true;
                    }

                    return isChanged || setProduct.apply(c) || c.getDeleted();
                }).collect(Collectors.toList());

            } else if (configDO != null) {
                return null;
            }

            InventoryHealthMainInstanceTypeConfigDO inventoryHealthMainInstanceTypeConfigDO = new InventoryHealthMainInstanceTypeConfigDO();
            // TODO 云霄暂未支持境内境外机型
            inventoryHealthMainInstanceTypeConfigDO.setRegionType("境内");
            inventoryHealthMainInstanceTypeConfigDO.setInstanceType(ins.getInstanceFamily());
            String insState = ins.getState() == null ? InventoryHealthInstanceFamilyType.EOL.getCode() : ins.getState();
            inventoryHealthMainInstanceTypeConfigDO.setType1(insState);
            inventoryHealthMainInstanceTypeConfigDO.setType1Name(InventoryHealthInstanceFamilyType.getNameFromCode(insState));

            setType2.apply(inventoryHealthMainInstanceTypeConfigDO);
            inventoryHealthMainInstanceTypeConfigDO.setDate(new Date());

            setProduct.apply(inventoryHealthMainInstanceTypeConfigDO);

            List<InventoryHealthMainInstanceTypeConfigDO> res = ListUtils.newList(inventoryHealthMainInstanceTypeConfigDO);
            InventoryHealthMainInstanceTypeConfigDO oversea = InventoryHealthMainInstanceTypeConfigDO.copy(inventoryHealthMainInstanceTypeConfigDO);
            oversea.setRegionType("境外");
            res.add(oversea);
            return res;
        }).filter(o -> o != null).flatMap(o -> o.stream()).collect(Collectors.toList());

        log.info(instanceTypeFullDTOS.toString());
        demandDBHelper.insertOrUpdate(mainInstanceTypeConfigDOS);
    }


    @Override
    public Object configureTargetServiceLevel(ConfigureServiceLevelConfigReq req) {
        List<Long> ids = req.getItems().stream().map(item -> item.getId()).collect(Collectors.toList());
        List<InventoryHealthServiceLevelConfigDO> configDOS = demandDBHelper.getAll(InventoryHealthServiceLevelConfigDO.class, "where id in (?) and date = ?", ids, req.getDate());
        Map<Long, InventoryHealthServiceLevelConfigDO> configDOMap = ListUtils.toMap(configDOS, InventoryHealthServiceLevelConfigDO::getId, o -> o);

        for (ConfigureServiceLevelConfigReq.Item item : req.getItems()) {
            InventoryHealthServiceLevelConfigDO configDO = configDOMap.get(item.getId());
            if (configDO == null) {
                throw new RuntimeException("找不到指定的服务水平配置：" + item.getId());
            }

            configDO.setNum(item.getNum());
            demandDBHelper.update(configDO);
        }

        return MapUtils.of("code", "success");
    }

    @Override
    @Transactional("demandTransactionManager")
    public Object importTargetServiceLevel(MultipartFile file, String date) {
        // 校验 zone 类型
        NotInStringListColumnChecker zoneTypeNameChecker = new NotInStringListColumnChecker(Arrays.stream(InventoryHealthZoneType.values()).map(InventoryHealthZoneType::getName).collect(Collectors.toList()));
        // 校验机型类型
        NotInStringListColumnChecker instanceTypeNameChecker = new NotInStringListColumnChecker(Arrays.stream(InventoryHealthInstanceFamilyType2.values()).map(InventoryHealthInstanceFamilyType2::getName).collect(Collectors.toList()));
        // 非空校验
        NotBlankColumnChecker notBlankChecker = new NotBlankColumnChecker();

        try {
            InventoryHealthServiceLevelConfigDO demo = new InventoryHealthServiceLevelConfigDO();
            DotExcelReadUtil.DotExcelBuilder<InventoryHealthServiceLevelConfigDO> builder = DotExcelReadUtil
                    .createBuilder(InventoryHealthServiceLevelConfigDO.class, InventoryHealthExcelGroupEnum.SERVICE_LEVEL_GROUP_ENUM, 2)
                    .registerValueCheckerByGetter(demo::getZoneType, notBlankChecker, zoneTypeNameChecker)
                    .registerValueCheckerByGetter(demo::getInstanceType, instanceTypeNameChecker)
                    .inputStream(file.getInputStream());

            ReadResult<InventoryHealthServiceLevelConfigDO> result = DotExcelReadUtil.read(builder);
            if (ListUtils.isEmpty(result.getErrors())) {
                if (ListUtils.isEmpty(result.getData())) {
                    throw new BizException("无数据，请检查 Excel 是否为空");
                }

                log.info(result.getData().toString());
                demandDBHelper.delete(InventoryHealthServiceLevelConfigDO.class, "where date=?", date);
                demandDBHelper.insertBatchWithoutReturnId(result.getData().stream().map(item -> {
                    item.setDate(DateUtils.parse(date));
                    // 将 name 变成 code
                    item.setZoneType(InventoryHealthZoneType.getCodeFromName(item.getZoneType()));
                    item.setInstanceType(InventoryHealthInstanceFamilyType2.getCodeFromName(item.getInstanceType()));
                    return item;
                }).collect(Collectors.toList()));
            }
            return result;
        } catch (Exception e) {
            throw BizException.makeThrow("导入失败，请先导出EXCEL，修改后导入。如有问题联系 brightwwu 解决。" + e.getMessage());
        }
    }

    private void updateRemoteZone(String zone, String strategy) {
        Map<String, String> params = new HashMap<>();
        params.put("zone", zone);
        params.put("strategy", strategy);
        String result = YunXiaoUtil.postRaw("/compass/zone/update-sales-strategy", params, 3);
        BaseDTO baseDTO = JSON.parse(result, BaseDTO.class);

        if (!baseDTO.getSuccess()) {
            throw BizException.makeThrow(baseDTO.getMessage());
        }
    }

    @Override
    public Object configureZone(ConfigureZoneConfigReq req) {
        InventoryHealthMainZoneNameConfigDO configDO = demandDBHelper.getOne(InventoryHealthMainZoneNameConfigDO.class, "where id = ?", req.getId());

        if (configDO == null) {
            throw new RuntimeException("找不到指定的可用区配置");
        }

        // 调用云霄接口，将可用区配置同步到云霄
        updateRemoteZone(configDO.getZone(), req.getType());

        configDO.setType(req.getType());
        configDO.setTypeName(InventoryHealthZoneType.getNameFromCode(configDO.getType()));
        demandDBHelper.update(configDO);
        return MapUtils.of("code", "success");
    }

    @Override
    @Transactional("demandTransactionManager")
    public Object importZone(MultipartFile file, String date) {
        // 校验 zone 类型
        NotInStringListColumnChecker zoneTypeNameChecker = new NotInStringListColumnChecker(Arrays.stream(InventoryHealthZoneType.values()).map(InventoryHealthZoneType::getName).collect(Collectors.toList()));
        // 非空校验
        NotBlankColumnChecker notBlankChecker = new NotBlankColumnChecker();

        try {
            InventoryHealthMainZoneNameConfigDO demo = new InventoryHealthMainZoneNameConfigDO();
            DotExcelReadUtil.DotExcelBuilder<InventoryHealthMainZoneNameConfigDO> builder = DotExcelReadUtil
                    .createBuilder(InventoryHealthMainZoneNameConfigDO.class, InventoryHealthExcelGroupEnum.ZONE_GROUP_ENUM, 2)
                    .registerValueCheckerByGetter(demo::getTypeName, notBlankChecker, zoneTypeNameChecker)
                    .inputStream(file.getInputStream());

            ReadResult<InventoryHealthMainZoneNameConfigDO> result = DotExcelReadUtil.read(builder);
            if (ListUtils.isEmpty(result.getErrors())) {
                if (ListUtils.isEmpty(result.getData())) {
                    throw new BizException("无数据，请检查 Excel 是否为空");
                }

                log.info(result.getData().toString());
                // 获取当天的所有数据，有变化的部分同步到云霄
                List<InventoryHealthMainZoneNameConfigDO> configDOS = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date=?", date);
                Map<String, InventoryHealthMainZoneNameConfigDO> configDOMap = ListUtils.toMap(configDOS, InventoryHealthMainZoneNameConfigDO::getZoneName, o -> o);

                List<InventoryHealthMainZoneNameConfigDO> dataNeedToSync = result.getData().stream().filter(item -> {
                    InventoryHealthMainZoneNameConfigDO pre = configDOMap.get(item.getZoneName());
                    if (pre == null) {
                        throw new BizException("可用区不存在：" + item.getZoneName());
                    }
                    return !item.getTypeName().equals(pre.getTypeName());
                }).collect(Collectors.toList());

                if (!dataNeedToSync.isEmpty()) {
                    dataNeedToSync.parallelStream().forEach(item -> {
                        String type = InventoryHealthZoneType.getCodeFromName(item.getTypeName());
                        InventoryHealthMainZoneNameConfigDO pre = configDOMap.get(item.getZoneName());
                        String zone = pre.getZone();
                        log.info("compass-zone-updating: " + zone + " " + type);
                        updateRemoteZone(zone, type);
                    });

                    dataNeedToSync = dataNeedToSync.stream().map(item -> {
                        InventoryHealthMainZoneNameConfigDO pre = configDOMap.get(item.getZoneName());
                        if (pre == null) {
                            throw new BizException("可用区不存在：" + item.getZoneName());
                        }
                        pre.setTypeName(item.getTypeName());
                        pre.setType(InventoryHealthZoneType.getCodeFromName(item.getTypeName()));
                        return pre;
                    }).collect(Collectors.toList());

                    demandDBHelper.update(dataNeedToSync);
                }
            }
            return result;
        } catch (Exception e) {
            throw BizException.makeThrow("导入失败，请先导出EXCEL，修改后导入。如有问题联系 brightwwu 解决。" + e.getMessage());
        }
    }


    @Override
    public Object configureInstanceType(ConfigureInstanceTypeConfigReq req) {
        InventoryHealthMainInstanceTypeConfigDO configDO = demandDBHelper.getOne(InventoryHealthMainInstanceTypeConfigDO.class, "where id = ?", req.getId());

        if (configDO == null) {
            throw new RuntimeException("找不到指定的实例类型配置");
        }

        // 调用云霄接口，将实例类型配置同步到云霄
//        updateRemoteInstanceType(configDO.getInstanceType(), req.getType1());
        String type1Name = InventoryHealthInstanceFamilyType.getNameFromCode(req.getType1());
        if (type1Name == null) {
            throw BizException.makeThrow("当前配置的一级类型不存在：" + req.getType1());
        }
        configDO.setType1(req.getType1());
        configDO.setType1Name(type1Name);

        String type2Name = InventoryHealthInstanceFamilyType2.getNameFromCode(req.getType2());
        if (type2Name == null) {
            throw BizException.makeThrow("当前配置的二级类型不存在：" + req.getType2());
        }
        configDO.setType2(req.getType2());
        configDO.setType2Name(type2Name);
        demandDBHelper.update(configDO);

        return MapUtils.of("code", "success");
    }

    @Override
    @Transactional("demandTransactionManager")
    public Object importInstanceType(MultipartFile file, String date) {
        // 校验 一级 类型
        NotInStringListColumnChecker instanceTypeName1Checker = new NotInStringListColumnChecker(Arrays.stream(InventoryHealthInstanceFamilyType.values()).map(InventoryHealthInstanceFamilyType::getName).collect(Collectors.toList()));
        // 校验 二级 类型
        NotInStringListColumnChecker instanceTypeName2Checker = new NotInStringListColumnChecker(Arrays.stream(InventoryHealthInstanceFamilyType2.values()).map(InventoryHealthInstanceFamilyType2::getName).collect(Collectors.toList()));
        // 非空校验
        NotBlankColumnChecker notBlankChecker = new NotBlankColumnChecker();

        try {
            InventoryHealthMainInstanceTypeConfigDO demo = new InventoryHealthMainInstanceTypeConfigDO();
            DotExcelReadUtil.DotExcelBuilder<InventoryHealthMainInstanceTypeConfigDO> builder = DotExcelReadUtil
                    .createBuilder(InventoryHealthMainInstanceTypeConfigDO.class, InventoryHealthExcelGroupEnum.INSTANCE_TYPE_ENUM, 2)
                    .registerValueCheckerByGetter(demo::getRegionType, notBlankChecker)
                    .registerValueCheckerByGetter(demo::getInstanceType, notBlankChecker)
                    .registerValueCheckerByGetter(demo::getType1Name, notBlankChecker, instanceTypeName1Checker)
                    .registerValueCheckerByGetter(demo::getType2Name, notBlankChecker, instanceTypeName2Checker)
                    .inputStream(file.getInputStream());

            ReadResult<InventoryHealthMainInstanceTypeConfigDO> result = DotExcelReadUtil.read(builder);
            if (ListUtils.isEmpty(result.getErrors())) {
                if (ListUtils.isEmpty(result.getData())) {
                    throw new BizException("无数据，请检查 Excel 是否为空");
                }

                log.info(result.getData().toString());
                // 获取当天的所有数据，有变化的部分同步到云霄
                List<InventoryHealthMainInstanceTypeConfigDO> configDOS = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date=?", date);
                Map<String, InventoryHealthMainInstanceTypeConfigDO> configDOMap = ListUtils.toMap(configDOS, o -> Strings.join("_", o.getRegionType(), o.getInstanceType(), o.getProduct()), o -> o);
                List<InventoryHealthMainInstanceTypeConfigDO> newInstanceTypes = ListUtils.newList();
                List<InventoryHealthMainInstanceTypeConfigDO> dataNeedToSync = result.getData().stream().filter(item -> {
                    String key = Strings.join("_", item.getRegionType(), item.getInstanceType(), item.getProduct());
                    InventoryHealthMainInstanceTypeConfigDO pre = configDOMap.get(key);

                    if (pre == null) {
                        newInstanceTypes.add(item);
                        return false;
                    }

                    return !item.getType1Name().equals(pre.getType1Name()) || !item.getType2Name().equals(pre.getType2Name());
                }).collect(Collectors.toList());

                if (!dataNeedToSync.isEmpty()) {
                    dataNeedToSync.parallelStream().forEach(item -> {
                        String key = Strings.join("_", item.getRegionType(), item.getInstanceType(), item.getProduct());
                        InventoryHealthMainInstanceTypeConfigDO pre = configDOMap.get(key);

                        if (pre == null || item.getType1Name().equals(pre.getType1Name())) {
                            return;
                        }

                        String state = InventoryHealthInstanceFamilyType.getCodeFromName(item.getType1Name());
                        String instanceType = item.getInstanceType();
                        log.info("compass-instance-type-updating: " + instanceType + " " + state);
//                        updateRemoteInstanceType(instanceType, state);
                    });

                    dataNeedToSync = dataNeedToSync.stream().map(item -> {
                        String key = Strings.join("_", item.getRegionType(), item.getInstanceType(), item.getProduct());
                        InventoryHealthMainInstanceTypeConfigDO pre = configDOMap.get(key);
                        if (pre == null) {
                            return null;
                        }
                        pre.setType1Name(item.getType1Name());
                        pre.setType1(InventoryHealthInstanceFamilyType.getCodeFromName(item.getType1Name()));
                        pre.setType2Name(item.getType2Name());
                        pre.setType2(InventoryHealthInstanceFamilyType2.getCodeFromName(item.getType2Name()));
                        return pre;
                    }).filter(o -> o != null).collect(Collectors.toList());

                    demandDBHelper.update(dataNeedToSync);
                }

                // insert new instance types
                if (ListUtils.isNotEmpty(newInstanceTypes)) {
                    List<InventoryHealthMainInstanceTypeConfigDO> finalNewInstanceTypes = newInstanceTypes.stream().map(item -> {
                        item.setDate(DateUtils.parse(date));
                        item.setType1(InventoryHealthInstanceFamilyType.getCodeFromName(item.getType1Name()));
                        item.setType2(InventoryHealthInstanceFamilyType2.getCodeFromName(item.getType2Name()));
                        return item;
                    }).collect(Collectors.toList());

                    demandDBHelper.insert(finalNewInstanceTypes);
                }
            }
            return result;
        } catch (Exception e) {
            throw BizException.makeThrow("导入失败，请先导出EXCEL，修改后导入。如有问题联系 brightwwu 解决。" + e.getMessage());
        }
    }

    @Override
    public Object queryTargetServiceLevel(QueryServiceLevelConfigReq req) {
        WhereSQL cond = req.genCondition();
        List<InventoryHealthServiceLevelConfigDO> result = demandDBHelper.getAll(InventoryHealthServiceLevelConfigDO.class, cond.getSQL(), cond.getParams());
        return result;
    }

    @Override
    public FileNameAndBytesDTO exportTargetServiceLevel(String date) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/target_service_level.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", date);
        List<InventoryHealthServiceLevelConfigDO> data = demandDBHelper.getAll(InventoryHealthServiceLevelConfigDO.class, condition.getSQL(), condition.getParams());
        data = data.stream().map(item -> {
            item.setZoneType(InventoryHealthZoneType.getNameFromCode(item.getZoneType()));
            item.setInstanceType(InventoryHealthInstanceFamilyType2.getNameFromCode(item.getInstanceType()));
            return item;
        }).collect(Collectors.toList());

        String fileNamePrefix = "服务水平策略(" + date + ")";
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    @Override
    public Object queryZoneConfig(QueryZoneConfigReq req) {
        WhereSQL cond = req.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> result = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());

        // 排序按照境内外（境内、境外）、类型（主力园区、辅助园区、待收敛园区、收敛园区、其他园区）
        List<String> regionTypes = ListUtils.newList("境内", "境外");
        List<String> types = ListUtils.newList("主力可用区", "辅助可用区", "待收敛可用区", "已收敛可用区", "特殊专区", "其他可用区");

        result.sort((o1, o2) -> {
            Integer o1RegionTypeIndex = regionTypes.indexOf(o1.getRegionType());
            Integer o2RegionTypeIndex = regionTypes.indexOf(o2.getRegionType());

            if (o1RegionTypeIndex == o2RegionTypeIndex) {
                Integer o1TypeIndex = types.indexOf(o1.getTypeName());
                Integer o2TypeIndex = types.indexOf(o2.getTypeName());

                return o1TypeIndex.compareTo(o2TypeIndex);
            }

            return o1RegionTypeIndex.compareTo(o2RegionTypeIndex);
        });
        return result;
    }

    @Override
    public FileNameAndBytesDTO exportZoneConfig(String date) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/zone_type.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        req.setDate(date);
        List<InventoryHealthMainZoneNameConfigDO> data = (List<InventoryHealthMainZoneNameConfigDO>)queryZoneConfig(req);

        String fileNamePrefix = "可用区策略(" + date + ")";
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    @Override
    public Object queryInstanceTypeConfig(QueryInstanceTypeConfigReq req) {
        WhereSQL cond = req.genCondition();
        List<InventoryHealthMainInstanceTypeConfigDO> result = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, cond.getSQL(), cond.getParams());
        // 排序按照境内外（境内、境外）、一级类型（主力机型、其他状态、EOL机型）
        List<String> regionTypes = ListUtils.newList("境内", "境外");
        List<String> types = ListUtils.newList("主力机型", "其他机型", "EOL机型");
        List<String> types2 = ListUtils.newList("主力机型", "在售非主力机型", "新机型", "EOL停供机型", "EOL库存机型");

        result.sort((o1, o2) -> {
            Integer o1RegionTypeIndex = regionTypes.indexOf(o1.getRegionType());
            Integer o2RegionTypeIndex = regionTypes.indexOf(o2.getRegionType());

            if (o1RegionTypeIndex == o2RegionTypeIndex) {
                Integer o1TypeIndex = types.indexOf(o1.getType1Name());
                Integer o2TypeIndex = types.indexOf(o2.getType1Name());

                if (o1TypeIndex == o2TypeIndex) {
                    Integer o1Type2Index = types2.indexOf(o1.getType2Name());
                    Integer o2Type2Index = types2.indexOf(o2.getType2Name());

                    return o1Type2Index.compareTo(o2Type2Index);
                }

                return o1TypeIndex.compareTo(o2TypeIndex);
            }

            return o1RegionTypeIndex.compareTo(o2RegionTypeIndex);
        });

        return result;
    }

    @Override
    public FileNameAndBytesDTO exportInstanceTypeConfig(String date) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/type_of_instance_type.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        QueryInstanceTypeConfigReq req = new QueryInstanceTypeConfigReq();
        req.setDate(date);
        List<InventoryHealthMainInstanceTypeConfigDO> data = (List<InventoryHealthMainInstanceTypeConfigDO>)queryInstanceTypeConfig(req);

        String fileNamePrefix = "机型策略(" + date + ")";
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    @Override
    public void snapshotInventoryHealthConfig(String today) {
        // 如果当天已经存在数据，删除
        demandDBHelper.delete(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", today);
        demandDBHelper.delete(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", today);
        demandDBHelper.delete(InventoryHealthServiceLevelConfigDO.class, "where date = ?", today);
        demandDBHelper.delete(InventoryHealthBufferConfigDO.class, "where date = ?", today);

        // 每天自动继承上一天的配置
        String yesterday = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(today), Calendar.DATE, -1));
        List<InventoryHealthMainInstanceTypeConfigDO> instanceTypeConfigDOS = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", yesterday);

        List<InventoryHealthMainInstanceTypeConfigDO> newIns = instanceTypeConfigDOS.stream().map(ins -> {
            InventoryHealthMainInstanceTypeConfigDO insDo = InventoryHealthMainInstanceTypeConfigDO.copy(ins);
            insDo.setDate(DateUtils.parse(today));
            return insDo;
        }).collect(Collectors.toList());

        demandDBHelper.insert(newIns);

        List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOS = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", yesterday);

        List<InventoryHealthMainZoneNameConfigDO> newZones = zoneNameConfigDOS.stream().map(zone -> {
            InventoryHealthMainZoneNameConfigDO zoneDo = InventoryHealthMainZoneNameConfigDO.copy(zone);
            zoneDo.setDate(DateUtils.parse(today));
            return zoneDo;
        }).collect(Collectors.toList());

        demandDBHelper.insert(newZones);

        List<InventoryHealthServiceLevelConfigDO> serviceLevelConfigDOS = demandDBHelper.getAll(InventoryHealthServiceLevelConfigDO.class, "where date = ?", yesterday);

        List<InventoryHealthServiceLevelConfigDO> newSer = serviceLevelConfigDOS.stream().map(ser -> {
            InventoryHealthServiceLevelConfigDO sDo = InventoryHealthServiceLevelConfigDO.copy(ser);
            sDo.setDate(DateUtils.parse(today));
            return sDo;
        }).collect(Collectors.toList());

        demandDBHelper.insert(newSer);

        List<InventoryHealthBufferConfigDO> bufferConfigDOS = demandDBHelper.getAll(InventoryHealthBufferConfigDO.class, "where date = ?", yesterday);

        List<InventoryHealthBufferConfigDO> newBufferConfigList = bufferConfigDOS.stream().map(ser -> {
            InventoryHealthBufferConfigDO bufferConfigDO = InventoryHealthBufferConfigDO.copy(ser);
            // 计算 rate
            if (bufferConfigDO.getRoi() == null || bufferConfigDO.getRoi().equals(BigDecimal.ZERO)) {
                bufferConfigDO.setRate(BigDecimal.ONE);
            } else {
                bufferConfigDO.setRate(BigDecimal.ONE.divide(bufferConfigDO.getRoi(), 6, RoundingMode.HALF_UP));
            }
            bufferConfigDO.setDate(DateUtils.parse(today));
            return bufferConfigDO;
        }).collect(Collectors.toList());

        demandDBHelper.insert(newBufferConfigList);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, keyScript = "args[0]")
    public Map<String, List<String>> getInstanceTypeConfigMap(String today) {
        QueryInstanceTypeConfigReq req = new QueryInstanceTypeConfigReq();
        req.setDate(today);
        List<InventoryHealthMainInstanceTypeConfigDO> result = (List<InventoryHealthMainInstanceTypeConfigDO>)queryInstanceTypeConfig(req);

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainInstanceTypeConfigDO.class, "select * from inventory_health_main_instance_type_config where date = (select min(`date`) from inventory_health_main_instance_type_config where deleted = 0) and deleted = 0");
        }

        return ListUtils.toMapList(result, InventoryHealthMainInstanceTypeConfigDO::getType2, InventoryHealthMainInstanceTypeConfigDO::getInstanceType);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, keyScript = "args[0]")
    public Map<String, String> getInstanceTypeToType2ConfigMap(String today) {
        QueryInstanceTypeConfigReq req = new QueryInstanceTypeConfigReq();
        req.setDate(today);
        List<InventoryHealthMainInstanceTypeConfigDO> result = (List<InventoryHealthMainInstanceTypeConfigDO>)queryInstanceTypeConfig(req);

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainInstanceTypeConfigDO.class, "select * from inventory_health_main_instance_type_config where date = (select min(`date`) from inventory_health_main_instance_type_config where deleted = 0) and deleted = 0");
        }

        return ListUtils.toMap(result, InventoryHealthMainInstanceTypeConfigDO::getInstanceType, InventoryHealthMainInstanceTypeConfigDO::getType2);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, keyScript = "args[0]")
    public Map<String, List<String>> getZoneConfigMap(String today) {
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        req.setDate(today);
        List<InventoryHealthMainZoneNameConfigDO> result = (List<InventoryHealthMainZoneNameConfigDO>)queryZoneConfig(req);

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainZoneNameConfigDO.class, "select * from inventory_health_main_zone_name_config where date = (select min(`date`) from inventory_health_main_zone_name_config where deleted = 0) and deleted = 0");
        }
        return ListUtils.toMapList(result, InventoryHealthMainZoneNameConfigDO::getType, InventoryHealthMainZoneNameConfigDO::getZoneName);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, keyScript = "args[0]")
    public Map<String, InventoryHealthMainZoneNameConfigDO> getZoneNameToTypeConfigMap(String today) {
        QueryZoneConfigReq req = new QueryZoneConfigReq();
        req.setDate(today);
        List<InventoryHealthMainZoneNameConfigDO> result = (List<InventoryHealthMainZoneNameConfigDO>)queryZoneConfig(req);

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthMainZoneNameConfigDO.class, "select * from inventory_health_main_zone_name_config where date = (select min(`date`) from inventory_health_main_zone_name_config where deleted = 0) and deleted = 0");
        }

        return ListUtils.toMap(result, InventoryHealthMainZoneNameConfigDO::getZoneName, o -> o);
    }

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 7200, keyScript = "args[0]")
    public Map<String, BigDecimal> queryTargetServiceLevel(String date) {
        QueryServiceLevelConfigReq req = new QueryServiceLevelConfigReq();
        req.setDate(date);
        List<InventoryHealthServiceLevelConfigDO> result = (List<InventoryHealthServiceLevelConfigDO>)queryTargetServiceLevel(req);

        if (result.isEmpty()) {
            result = demandDBHelper.getRaw(InventoryHealthServiceLevelConfigDO.class, "select * from inventory_health_service_level_config where date = (select min(`date`) from inventory_health_service_level_config where deleted = 0) and deleted = 0");
        }

        return ListUtils.toMap(result, o -> getTargetServiceLevelKey(o.getRegionType(), o.getZoneType(), o.getInstanceType(), o.getBillType()), o -> o.getNum());
    }

    @Override
    public Object configureBufferPool(ConfigureBufferPoolConfigReq req) {
        InventoryHealthBufferConfigDO configDO = demandDBHelper.getOne(InventoryHealthBufferConfigDO.class, "where id = ?", req.getId());

        if (configDO == null) {
            throw new RuntimeException("找不到指定的弹性备货池策略配置");
        }

        configDO.setRoi(req.getRoi());
        if (req.getIncome() != null) {
            configDO.setIncome(req.getIncome());
        }
        if (req.getCost() != null) {
            configDO.setCost(req.getCost());
        }

        if (configDO.getRoi() == null || configDO.getRoi().compareTo(BigDecimal.ZERO) == 0) {
            configDO.setRate(BigDecimal.ONE);
        } else {
            configDO.setRate(BigDecimal.ONE.divide(configDO.getRoi(), 20, RoundingMode.HALF_UP));
        }

        demandDBHelper.update(configDO);

        return MapUtils.of("code", "success");
    }

    @Override
    public Object importBufferPool(MultipartFile file, String date) {
        // 非空校验
        NotBlankColumnChecker notBlankChecker = new NotBlankColumnChecker();

        try {
            InventoryHealthBufferConfigDO demo = new InventoryHealthBufferConfigDO();
            DotExcelReadUtil.DotExcelBuilder<InventoryHealthBufferConfigDO> builder = DotExcelReadUtil
                    .createBuilder(InventoryHealthBufferConfigDO.class, InventoryHealthExcelGroupEnum.BUFFER_POOL_ENUM, 2)
                    .registerValueCheckerByGetter(demo::getRoi, notBlankChecker)
                    .registerValueCheckerByGetter(demo::getInstanceType, notBlankChecker)
                    .registerValueCheckerByGetter(demo::getRegionType, notBlankChecker)
                    .inputStream(file.getInputStream());

            ReadResult<InventoryHealthBufferConfigDO> result = DotExcelReadUtil.read(builder);
            if (ListUtils.isEmpty(result.getErrors())) {
                if (ListUtils.isEmpty(result.getData())) {
                    throw new BizException("无数据，请检查 Excel 是否为空");
                }

                log.info(result.getData().toString());
                // 获取当天的所有数据标记为删除
                List<InventoryHealthBufferConfigDO> configDOS = demandDBHelper.getAll(InventoryHealthBufferConfigDO.class, "where date=?", date);
                configDOS.forEach(config -> config.setDeleted(true));
                demandDBHelper.update(configDOS);

                // 重新计算 rate = 1 / roi
                result.getData().forEach(item -> {
                    if (item.getRoi() == null || item.getRoi().compareTo(BigDecimal.ZERO) == 0) {
                        item.setRate(BigDecimal.ONE);
                    } else {
                        item.setRate(BigDecimal.ONE.divide(item.getRoi(), 6, RoundingMode.HALF_UP));
                    }
                    item.setDate(DateUtils.parse(date));
                });
                // 插入新的数据
                demandDBHelper.insert(result.getData());
            }
            return result;
        } catch (Exception e) {
            throw BizException.makeThrow("导入失败，请先导出EXCEL，修改后导入。如有问题联系 brightwwu 解决。" + e.getMessage());
        }
    }

    @Override
    public Object queryBufferPoolConfig(QueryBufferPoolConfigReq req) {
        WhereSQL cond = req.genCondition();
        List<InventoryHealthBufferConfigDO> result = demandDBHelper.getAll(InventoryHealthBufferConfigDO.class, cond.getSQL(), cond.getParams());

        return result;
    }

    @Override
    public FileNameAndBytesDTO exportBufferPoolConfig(String date) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/buffer_pool_config.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        QueryBufferPoolConfigReq req = new QueryBufferPoolConfigReq();
        req.setDate(date);
        List<InventoryHealthBufferConfigDO> data = (List<InventoryHealthBufferConfigDO>)queryBufferPoolConfig(req);

        String fileNamePrefix = "弹性备货池策略(" + date + ")";
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    public static String getTargetServiceLevelKey(String regionType, String zoneType, String instanceType, String billType) {
        return Strings.join("_", regionType, zoneType, instanceType, billType);
    }
}
