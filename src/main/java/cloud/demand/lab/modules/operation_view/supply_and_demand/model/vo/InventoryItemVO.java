package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.SupplyAndDemandUtils;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:39
 */
@Data
public class InventoryItemVO implements CountryNameFiller, InstanceGroupFiller {

    private String statTime;

    private String yearMonth;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private String type;//demand、supply、targetInventory、actualSafeInventory、actualInventory、withholdInventory

    private Integer core;

    public static InventoryItemVO transform(InventoryDisassembleResp.Item item) {
        InventoryItemVO ret = new InventoryItemVO();
        BeanUtils.copyProperties(item,ret);
        ret.setStatTime(LocalDate.parse(item.getStatTime()).plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        ret.setType("actualSafeInventory");
        ret.setCore(item.getActualSafeInventory());
        if(StringUtils.isBlank(item.getCustomhouseTitle())){
            ret.setCustomhouseTitle(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getAreaName())){
            ret.setAreaName(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getRegionName())){
            ret.setRegionName(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getZoneName())){
            ret.setZoneName(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getZoneCategory())){
            ret.setZoneCategory(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getInstanceCategory())){
            ret.setInstanceCategory(Constant.EMPTY_VALUE_STR);
        }
        if(StringUtils.isBlank(item.getInstanceType())){
            ret.setInstanceType(Constant.EMPTY_VALUE_STR);
        }
        return ret;
    }

    public static InventoryItemVO transform(AdsInventoryHealthSupplySummaryDfVO item, String type) {
        InventoryItemVO ret = new InventoryItemVO();
        BeanUtils.copyProperties(item,ret);
        ret.setStatTime(LocalDate.parse(item.getStatTime()).plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        ret.setType(type);
        if(StringUtils.equals("withholdInventory",type)){
            ret.setCore(item.getWithholdInventoryCore().intValue());
        }else {
            ret.setCore(item.getBeginInventory().intValue());
        }

        return ret;
    }

    public static InventoryItemVO transform(SupplyOnTheWayDetailData item) {
        InventoryItemVO ret = new InventoryItemVO();
        BeanUtils.copyProperties(item,ret);
        ret.setType("supply");
        ret.setYearMonth(item.getSlaMonth());
        ret.setCore(item.getSupplyCore().intValue());

        return ret;
    }

    public static InventoryItemVO merge(List<InventoryItemVO> list, List<String> dims) {
        InventoryItemVO ret = new InventoryItemVO();
        if (ListUtils.isEmpty(list)) {
            return ret;
        }
        InventoryItemVO first = list.get(0);
        ret.setStatTime(first.getStatTime());
        for (String dim : dims) {
            try {
                Field field = InventoryItemVO.class.getDeclaredField(dim);
                field.setAccessible(true);
                field.set(ret, field.get(first));
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        ret.setCore(list.stream().map(o -> o.getCore()).filter(o -> Objects.nonNull(o)).reduce(0, Integer::sum));
        return ret;
    }

    public static String getGroupKey(InventoryItemVO item) {
        return StringUtils.joinWith("@", item.getStatTime(), item.getZoneCategory(), item.getInstanceCategory(), item.getCustomhouseTitle(),
                item.getCountryName(), item.getAreaName(), item.getRegionName(), item.getZoneName(), item.getInstanceGroup(),item.getInstanceType());
    }

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.getRegionName();
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
    }

}
