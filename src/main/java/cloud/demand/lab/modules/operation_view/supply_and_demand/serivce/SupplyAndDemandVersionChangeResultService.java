package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyAndDemandVersionChangeResultVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateChangeResultReq;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/15 13:12
 */
public interface SupplyAndDemandVersionChangeResultService {
    /**
     * 版本变化原因
     *
     * @param req 请求参数
     * @return 返回的原因组
     */
    List<SupplyAndDemandVersionChangeResultVO> queryChangeResult(QueryChangeResultReq req);

    /**
     * 创建版本变化原因
     *
     * @param req 请求参数
     */
    void createChangeResult(CreateChangeResultReq req);

    /**
     * 更新版本变化原因
     *
     * @param req 请求参数
     */
    void updateChangeResult(UpdateChangeResultReq req);

    /**
     * 通过id主键组删除版本变化原因
     *
     * @param ids 待删除id组
     */
    void deleteChangeResult(List<Long> ids);
}
