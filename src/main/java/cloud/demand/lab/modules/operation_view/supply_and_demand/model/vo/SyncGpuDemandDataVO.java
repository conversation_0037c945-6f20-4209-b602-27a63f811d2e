package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.PplWaveProjectTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @since 2025/4/11 15:04
 */
@Data
public class SyncGpuDemandDataVO {
    /**
     * 分区键，代表数据版本<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 年月 <br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 订单号<br/>Column: [order_number]
     */
    @Column(value = "order_number")
    private String orderNumber;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 通用客户简称<br/>Column: [common_customer_short_name]
     */
    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 区域名称<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;


    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区编码<br/>Column: [zone]
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 数据源: 1.ORDER（订单数据） 2.VERSION（版本数据） 3.EXTEND（继承数据）<br/>Column: [data_source]
     */
    @Column(value = "data_source")
    private String dataSource;

    /**
     * 订单来源<br/>Column: [order_source]
     */
    @Column(value = "order_source")
    private String orderSource;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 开始购买时间<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private String beginBuyDate;

    /**
     * 结束购买时间<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private String endBuyDate;

    /**
     * '需求台数'<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private BigDecimal instanceNum;

    /**
     * 需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private BigDecimal totalCore = BigDecimal.ZERO;

    /**
     * 购买核心数<br/>Column: [buy_total_core]
     */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalCore = BigDecimal.ZERO;

    /**
     * 待购买核心数<br/>Column: [wait_buy_total_core]
     */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore = BigDecimal.ZERO;

    @Column( value = "cbs_is_spike")
    private int cbsIsSpike;

    /**
     * 弹性类型，一次性、日弹性、周弹性、月弹性
     */
    @Column(value = "elastic_type")
    private String elasticType;

    /** 卡型/gpu类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;

    /** GPU总卡数<br/>Column: [total_gpu_num] */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum = BigDecimal.ZERO;

    /** 购买GPU卡数<br/> */
    @Column(value = "buy_total_gpu_num")
    private BigDecimal buyTotalGpuNum = BigDecimal.ZERO;

    /** 待购买GPU卡数<br/> */
    @Column(value = "wait_buy_total_gpu_num")
    private BigDecimal waitBuyTotalGpuNum = BigDecimal.ZERO;

    public static ProductDemandDataDfDO transform(SyncGpuDemandDataVO item){
        ProductDemandDataDfDO ret = new ProductDemandDataDfDO();
        BeanUtils.copyProperties(item,ret);
        ret.setProductCategory(ProductCategoryEnum.GPU.getName());
        ret.setDemandCaliber("共识需求");
        ret.setDiskType(Constant.EMPTY_STR);
        ret.setVolumeType(Constant.EMPTY_STR);
        ret.setProjectType(PplWaveProjectTypeEnum.getNameByCode(item.getCbsIsSpike()));
        return ret;
    }

    public static List<ProductDemandDataDfDO> buildCrossMonthData(List<ProductDemandDataDfDO> list, LocalDate statTime) {
        List<ProductDemandDataDfDO> ret = ListUtils.newArrayList();
        for (ProductDemandDataDfDO item : list) {
            YearMonth beginMonth = YearMonth.of(LocalDate.parse(item.getBeginBuyDate()).getYear(),
                    LocalDate.parse(item.getBeginBuyDate()).getMonthValue());
            YearMonth curMonth = YearMonth.of(statTime.getYear(), statTime.getMonthValue());
            LocalDate endDate = DateUtils.parseLocalDate(item.getEndBuyDate());
            if (!endDate.isBefore(statTime) && item.getWaitBuyTotalGpuNum().compareTo(BigDecimal.ZERO) > 0) {
                if (beginMonth.isBefore(curMonth)) {
                    //弹性需求和新增需求
                    if (item.getDemandType().equals("ELASTIC") || item.getDemandType().equals("NEW")) {
                        ProductDemandDataDfDO retItem = new ProductDemandDataDfDO();
                        BeanUtils.copyProperties(item, retItem);
                        retItem.setYearMonth(YearMonth.of(statTime.getYear(), statTime.getMonthValue()).toString());
                        retItem.setMonthType("跨月");
                        ret.add(retItem);
                    }
                }
            }
        }
        return ret;
    }
}
