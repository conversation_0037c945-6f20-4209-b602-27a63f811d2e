package cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.IInventoryTargetGroupKey;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetContext;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SafeInventoryCalcVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.Constant;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 11:30
 */
@Data
public class SupplyDemandHedgingResp implements IInventoryTargetGroupKey {

    private String productCategory;

    private String statTime;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private String volumeType;

    private String projectType;

    private BigDecimal beginInventory;

    private SafeInventoryCalcVO safeInventoryCalc;

    private BigDecimal safetyInventory;
    private Integer safetyInventoryCore;

    private BigDecimal withholdInventory;

    private Integer withholdInventoryCore;

    private BigDecimal currentMonthScale;

    private List<SupplyDemandHedgingResp.Item> demand;

    private List<SupplyDemandHedgingResp.Item> supply;

    private List<SupplyDemandHedgingResp.Item> endInventory;

    private BigDecimal targetInventory;
    private BigDecimal targetThreshold;

    @Override
    public String getInventoryTargetGroupKey() {
        if (StringUtils.equals(ProductCategoryEnum.CVM.getName(), this.getProductCategory())) {
            return StringUtils.joinWith("@", this.getStatTime(), this.getInstanceType(), this.getRegionName());
        }
        if (StringUtils.equals(ProductCategoryEnum.CBS.getName(), this.getProductCategory())) {
            return StringUtils.joinWith("@",this.getStatTime(),this.getCustomhouseTitle(),this.getCountryName(),this.getAreaName(),this.getRegionName(),
                    this.getZoneName(),this.getZoneCategory(),this.getVolumeType());
        }
        if (StringUtils.equals(ProductCategoryEnum.DB.getName(), this.getProductCategory())) {
            return StringUtils.joinWith("@",this.getStatTime(),this.getCustomhouseTitle(),this.getCountryName(),this.getAreaName(),this.getRegionName(),
                    this.getZoneName());
        }
        return Constant.EMPTY_VALUE;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private String yearMonth;

        private BigDecimal totalAmount;

        private Integer totalCore;

        public Item(String yearMonth, BigDecimal totalAmount) {
            this.yearMonth = yearMonth;
            this.totalAmount = totalAmount;
            this.totalCore = Objects.nonNull(totalAmount) ? totalAmount.intValue() : null;
        }
    }

    public static List<SupplyDemandHedgingResp> builder(String productCategory, List<SupplyDemandHedgingItemVO> itemList, InventoryTargetContext targetContext, List<String> yearMonth) {
        List<SupplyDemandHedgingResp> respList = ListUtils.newArrayList();
        Map<String, List<SupplyDemandHedgingItemVO>> outGroup = ListUtils.groupBy(itemList, item -> SupplyDemandHedgingItemVO.getGroupKey(item));
        for (Map.Entry<String, List<SupplyDemandHedgingItemVO>> outEntry : outGroup.entrySet()) {
            List<SupplyDemandHedgingItemVO> innerList = outEntry.getValue();
            SupplyDemandHedgingResp resp = new SupplyDemandHedgingResp();
            BeanUtils.copyProperties(innerList.get(0), resp);
            Map<String, List<SupplyDemandHedgingItemVO>> innerGroup = ListUtils.groupBy(innerList, SupplyDemandHedgingItemVO::getType);
            //demand、scale、supply、targetInventory、actualSafeInventory、actualInventory、withholdInventory
            for (Map.Entry<String, List<SupplyDemandHedgingItemVO>> innerEntry : innerGroup.entrySet()) {
                SupplyDemandHedgingItemVO itemVO = innerEntry.getValue().get(0);
                resp.setProductCategory(productCategory);
                if (StringUtils.equals(innerEntry.getKey(), "scale")) {
                    resp.setCurrentMonthScale(itemVO.getAmount());
                }else if (StringUtils.equals(innerEntry.getKey(), "withholdInventory")) {
                    resp.setWithholdInventory(itemVO.getAmount());
                    resp.setWithholdInventoryCore(itemVO.getAmount().intValue());
                } else if (StringUtils.equals(innerEntry.getKey(), "actualInventory")) {
                    resp.setBeginInventory(itemVO.getAmount());
                } else if (StringUtils.equals(innerEntry.getKey(), "actualSafeInventory")) {
                    resp.setSafetyInventoryCore(itemVO.getAmount().intValue());
                    resp.setSafetyInventory(itemVO.getAmount());
                    resp.setSafeInventoryCalc(itemVO.getSafeInventoryCalc());
                } else {
                    List<SupplyDemandHedgingResp.Item> respItem = ListUtils.transform(innerEntry.getValue(), item -> new SupplyDemandHedgingResp.Item(item.getYearMonth(), item.getAmount()));
                    for (String itemYearMonth : yearMonth) {
                        if (ListUtils.isEmpty(respItem)) {
                            respItem.add(new SupplyDemandHedgingResp.Item(itemYearMonth, null));
                            continue;
                        }
                        if (!ListUtils.contains(respItem, item -> StringUtils.equals(itemYearMonth, item.getYearMonth()))) {
                            respItem.add(new SupplyDemandHedgingResp.Item(itemYearMonth, null));
                        }
                    }
                    respItem = respItem.stream().sorted(Comparator.comparing(item -> item.getYearMonth())).collect(Collectors.toList());
                    if (StringUtils.equals(innerEntry.getKey(), "demand")) {
                        resp.setDemand(respItem);
                    } else {
                        resp.setSupply(respItem);
                    }
                }
            }
            String key = resp.getInventoryTargetGroupKey();
            if (targetContext.getMatchFlag()) {
                InventoryTargetVO targetVO = null;
                if(targetContext.getTargetMap().containsKey(key)){
                    targetVO = targetContext.getTargetMap().get(key);
                }
                if(Objects.isNull(targetVO) &&  (StringUtils.equals(ProductCategoryEnum.CBS.getName(),productCategory) ||StringUtils.equals(ProductCategoryEnum.DB.getName(),productCategory))){
                    targetVO = new InventoryTargetVO();
                    targetVO.setInventoryTarget(BigDecimal.ZERO);
                }
                if(Objects.nonNull(targetVO)){
                    resp.setTargetInventory(targetVO.getInventoryTarget());
                    resp.setTargetThreshold(targetVO.getInventoryThreshold());
                }
            }
            //如果demand为null
            if (ListUtils.isEmpty(resp.getDemand())) {
                List<SupplyDemandHedgingResp.Item> demand = ListUtils.newArrayList();
                for (String itemYearMonth : yearMonth) {
                    demand.add(new SupplyDemandHedgingResp.Item(itemYearMonth, null));
                }
                resp.setDemand(demand);
            }
            //如果supply为null
            if (ListUtils.isEmpty(resp.getSupply())) {
                List<SupplyDemandHedgingResp.Item> supply = ListUtils.newArrayList();
                for (String itemYearMonth : yearMonth) {
                    supply.add(new SupplyDemandHedgingResp.Item(itemYearMonth, null));
                }
                resp.setSupply(supply);
            }
            respList.add(resp);
        }
        //计算期末库存
        respList.forEach(item -> calEndInventory(item));
        return respList;
    }

    private static void calEndInventory(SupplyDemandHedgingResp resp) {
        List<SupplyDemandHedgingResp.Item> respItem = ListUtils.newArrayList();
        List<String> demandMonth = ListUtils.isEmpty(resp.getDemand()) ? ListUtils.newArrayList() : resp.getDemand().stream().map(item -> item.getYearMonth()).collect(Collectors.toList());
        List<String> supplyMonth = ListUtils.isEmpty(resp.getSupply()) ? ListUtils.newArrayList() : resp.getSupply().stream().map(item -> item.getYearMonth()).collect(Collectors.toList());
        List<String> endMonth = ListUtils.union(demandMonth, supplyMonth).stream().distinct().collect(Collectors.toList());

        for (String month : endMonth) {
            BigDecimal beginInventory = resp.getBeginInventory();
            BigDecimal demand = getLessMonthAmount(month, resp.getDemand());
            BigDecimal supply = getLessMonthAmount(month, resp.getSupply());
            BigDecimal endInventory = SoeCommonUtils.addWithNull(SoeCommonUtils.sub(beginInventory, demand), supply);
            respItem.add(new SupplyDemandHedgingResp.Item(month, endInventory));
        }
        resp.setEndInventory(respItem);
    }

    private static BigDecimal getLessMonthAmount(String yearMonth, List<SupplyDemandHedgingResp.Item> items) {
        if (ListUtils.isEmpty(items)) {
            return null;
        }
        return items.stream()
                .filter(item -> StringUtils.compare(item.getYearMonth(), yearMonth) <= 0)
                .filter(item -> Objects.nonNull(item.getTotalAmount()))
                .map(item -> item.getTotalAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
