package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dwd_db_sale_scale_df")
public class DwdDbSaleScaleDfDO implements CountryNameFiller, ZoneCategoryFiller {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 数据库产品：CDB、CRS等<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 规模类别：外部计费规模、内部领用规模<br/>Column: [category_name] */
    @Column(value = "category_name")
    private String categoryName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 区域名称<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区类型<br/>Column: [zone_category] */
    @Column(value = "zone_category")
    private String zoneCategory;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 内存(TB)<br/>Column: [memory] */
    @Column(value = "memory")
    private BigDecimal memory;

    /** 磁盘(TB)<br/>Column: [storage] */
    @Column(value = "storage")
    private BigDecimal storage;

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.getRegionName();
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }
}