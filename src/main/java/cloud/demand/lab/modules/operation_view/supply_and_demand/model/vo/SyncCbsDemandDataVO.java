package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.PplDiskTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.PplWaveProjectTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.VolumeTypeEnum;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import com.alibaba.fastjson.JSONObject;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/11 15:04
 */
@Data
public class SyncCbsDemandDataVO extends SyncCvmDemandDataVO {

    /**
     * (系统盘)云盘类型
     */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /**
     * (数据盘)云盘类型
     */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    @Column(value = "system_disk")
    private String systemDisk;

    @Column(value = "data_disk")
    private String dataDisk;

    @Column(value = "single_system_disk_num")
    private BigDecimal singleSystemDiskNum = BigDecimal.ZERO;

    @Column(value = "single_system_disk_storage")
    private BigDecimal singleSystemDiskStorage = BigDecimal.ZERO;

    @Column(value = "single_data_disk_num")
    private BigDecimal singleDataDiskNum = BigDecimal.ZERO;

    @Column(value = "single_data_disk_storage")
    private BigDecimal singleDataDiskStorage = BigDecimal.ZERO;

    public static List<ProductDemandDataDfDO> builder(List<SyncCbsDemandDataVO> list) {
        List<ProductDemandDataDfDO> retList = ListUtils.newArrayList();
        for (SyncCbsDemandDataVO item : list) {
            List<ProductDemandDataDfDO> tempList = ListUtils.newArrayList();
            int fillCount = 0;
            //填充系统盘
            fillCount = fillResult(tempList, item, item.getInstanceNum(), item.getSingleSystemDiskNum(), item.getSingleSystemDiskStorage(),
                    Constant.SYSTEM_DISK, PplDiskTypeEnum.getCodeByName(item.getSystemDiskType()), fillCount);
            //填充数据盘
            fillCount = fillResult(tempList, item, item.getInstanceNum(), item.getSingleDataDiskNum(), item.getSingleDataDiskStorage(),
                    Constant.DATA_DISK, PplDiskTypeEnum.getCodeByName(item.getDataDiskType()), fillCount);
            if (!StringUtils.equals(item.getSystemDisk(), Constant.EMPTY_STR)) {
                List<Disk> disks = JSONObject.parseArray(item.getSystemDisk(), Disk.class);
                for (Disk disk : disks) {
                    //填充系统盘
                    fillCount = fillResult(tempList, item, disk, Constant.SYSTEM_DISK, fillCount);
                }
            }
            if (!StringUtils.equals(item.getDataDisk(), Constant.EMPTY_STR)) {
                List<Disk> disks = JSONObject.parseArray(item.getDataDisk(), Disk.class);
                for (Disk disk : disks) {
                    //填充数据盘
                    fillCount = fillResult(tempList, item, disk, Constant.DATA_DISK, fillCount);
                }
            }
            BigDecimal buyRate = SoeCommonUtils.divide(item.getBuyTotalCore(), item.getTotalCore());
            BigDecimal waitBuyRate = SoeCommonUtils.divide(item.getWaitBuyTotalCore(), item.getTotalCore());
            for (ProductDemandDataDfDO temp : tempList) {
                temp.setWaitBuyDiskStorage(SoeCommonUtils.multiply(waitBuyRate, temp.getDiskStorage()));
                temp.setBuyDiskStorage(SoeCommonUtils.multiply(buyRate, temp.getDiskStorage()));
            }
            retList.addAll(tempList);
        }
        return retList;
    }

    private static int fillResult(List<ProductDemandDataDfDO> result, SyncCbsDemandDataVO item, BigDecimal instanceNum, BigDecimal singleDiskNum, BigDecimal singleDiskStorage,
                                  String diskType, String volumeType, int fillCount) {
        if (StringUtils.equals(diskType, Constant.SYSTEM_DISK) && BigDecimal.ZERO.compareTo(singleDiskNum)  == 0) {
            //系统盘的数量为1
            singleDiskNum = BigDecimal.ONE;
        }
        if (BigDecimal.ZERO.compareTo(instanceNum) == 0 || BigDecimal.ZERO.compareTo(singleDiskNum) == 0 || BigDecimal.ZERO.compareTo(singleDiskStorage) == 0) {
            //剔除为0的
            return fillCount;
        }
        if (!PplDiskTypeEnum.ssdANdPremiumCodeList.contains(volumeType)) {
            //非SSD、高性能的剔除
            return fillCount;
        }
        ProductDemandDataDfDO ret = new ProductDemandDataDfDO();
        BeanUtils.copyProperties(item, ret);
        ret.setProjectType(PplWaveProjectTypeEnum.getNameByCode(item.getCbsIsSpike()));
        ret.setProductCategory(ProductCategoryEnum.CBS.getName());
        ret.setDiskNum(SoeCommonUtils.multiply(instanceNum, singleDiskNum));
        ret.setDiskStorage(SoeCommonUtils.multiply(singleDiskStorage, ret.getDiskNum()));
        ret.setDiskType(diskType);
        if (fillCount > 0) {
            ret.setWaitBuyTotalCore(BigDecimal.ZERO);
            ret.setBuyTotalCore(BigDecimal.ZERO);
            ret.setInstanceNum(BigDecimal.ZERO);
            ret.setTotalCore(BigDecimal.ZERO);
        }
        volumeType = PplDiskTypeEnum.ssdCodeList.contains(volumeType) ? VolumeTypeEnum.SSD.getName() : VolumeTypeEnum.PREMIUM.getName();
        ret.setVolumeType(volumeType);
        ret.setDemandCaliber("共识需求");
        result.add(ret);
        fillCount++;
        //跨月
        ProductDemandDataDfDO cross = buildCrossMonthData(ret, item.getWaitBuyTotalCore());
        if (Objects.nonNull(cross)) {
            result.add(cross);
        }
        return fillCount;
    }

    private static int fillResult(List<ProductDemandDataDfDO> result, SyncCbsDemandDataVO item, Disk disk, String diskType, int fillCount) {
        return fillResult(result, item, disk.getDiskNum(), BigDecimal.ONE, disk.getDiskStorage(), diskType, disk.getDiskType(), fillCount);
    }

    public static ProductDemandDataDfDO buildCrossMonthData(ProductDemandDataDfDO source, BigDecimal waitBuyTotalCore) {
        if (Objects.isNull(waitBuyTotalCore) || waitBuyTotalCore.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        int beginMonthValue = LocalDate.parse(source.getBeginBuyDate()).getMonthValue();
        int endMonthValue = LocalDate.parse(source.getEndBuyDate()).getMonthValue();
        int lastMonthValue = LocalDate.now().getMonthValue() - 1;
        if (beginMonthValue != endMonthValue && lastMonthValue == beginMonthValue) {
            ProductDemandDataDfDO target = new ProductDemandDataDfDO();
            BeanUtils.copyProperties(source, target);
            //月份+1
            String nextYearMonth = LocalDate.parse(source.getYearMonth() + "-01").plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            source.setYearMonth(nextYearMonth);
            source.setMonthType(Constant.CROSS_MONTH);
            return target;
        }
        return null;
    }

    public static List<ProductDemandDataDfDO> buildSystemData(List<ProductDemandDataDfDO> list) {
        List<ProductDemandDataDfDO> ret = ListUtils.newArrayList();
        for (ProductDemandDataDfDO item : list) {
            if (StringUtils.equals(item.getCustomhouseTitle(), "境内")) {
                //国内维持原样
                ProductDemandDataDfDO newInstance = new ProductDemandDataDfDO();
                BeanUtils.copyProperties(item, newInstance);
                newInstance.setDemandCaliber("系统校准");
                ret.add(newInstance);
            } else if (item.getIsInner() == 0) {
                //国外直接取外部*2
                ProductDemandDataDfDO newInstance = new ProductDemandDataDfDO();
                BeanUtils.copyProperties(item, newInstance);
                newInstance.setDemandCaliber("系统校准");
                BigDecimal two = new BigDecimal("2");
                newInstance.setInstanceNum(SoeCommonUtils.multiply(item.getInstanceNum(), two));
                newInstance.setTotalCore(SoeCommonUtils.multiply(item.getTotalCore(), two));
                newInstance.setBuyTotalCore(SoeCommonUtils.multiply(item.getBuyTotalCore(), two));
                newInstance.setWaitBuyTotalCore(SoeCommonUtils.multiply(item.getWaitBuyTotalCore(), two));
                newInstance.setDiskNum(SoeCommonUtils.multiply(item.getDiskNum(), two));
                newInstance.setDiskStorage(SoeCommonUtils.multiply(item.getDiskStorage(), two));
                newInstance.setWaitBuyDiskStorage(SoeCommonUtils.multiply(item.getWaitBuyDiskStorage(), two));
                newInstance.setBuyDiskStorage(SoeCommonUtils.multiply(item.getBuyDiskStorage(), two));
                ret.add(newInstance);
            }
        }
        return ret;
    }

    @Data
    private static class Disk {
        private BigDecimal diskNum;
        private BigDecimal diskSingleNum;

        private BigDecimal diskStorage;

        private String diskType;
    }
}
