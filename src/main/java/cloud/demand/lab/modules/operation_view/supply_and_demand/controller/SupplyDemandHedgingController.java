package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingOverviewResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyDemandHedgingService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:34
 */
@Slf4j
@JsonrpcController("/supply-demand/inventory/report")
public class SupplyDemandHedgingController {

    @Resource
    private SupplyDemandHedgingService inventoryService;

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;

    @RequestMapping
    public List<SupplyDemandHedgingResp> queryReport(@JsonrpcParam SupplyDemandHedgingReq req) {
        if (StringUtils.isBlank(req.getStartStatTime()) || StringUtils.isBlank(req.getEndStatTime())) {
            throw new BizException("开始或结束版本不能为空");
        }
        if (StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())) {
            if (ListUtils.isEmpty(req.getProduct())) {
                req.setProduct(ListUtils.newArrayList("CRS", "CDB"));
            }
        }
        supplyAndDemandDictService.addUnclassified(req);
        return inventoryService.queryReport(req);
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam SupplyDemandParamTypeReq req) {
        return inventoryService.queryParams(req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> exportInventory(@JsonrpcParam SupplyDemandHedgingReq req) {
        if (StringUtils.isBlank(req.getStartStatTime()) && StringUtils.isBlank(req.getEndStatTime())) {
            throw new BizException("开始和结束版本不能同时为空");
        }
        //未分类字段处理
        supplyAndDemandDictService.addUnclassified(req);
        //最细维度
        req.setDims(ListUtils.newArrayList("zoneCategory", "customhouseTitle", "countryName", "areaName", "regionName", "zoneName"));
        if (StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())) {
            if (ListUtils.isEmpty(req.getProduct())) {
                req.setProduct(ListUtils.newArrayList("CRS", "CDB"));
            }
        }
        if (StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CVM.getName())) {
            req.getDims().addAll(ListUtils.newArrayList("instanceCategory", "instanceGroup", "instanceType"));
        } else {
            req.getDims().addAll(ListUtils.newArrayList("volumeType", "projectType"));
        }
        return inventoryService.exportInventory(req);
    }

    @RequestMapping
    public List<SupplyDemandHedgingOverviewResp> queryOverviewReport(@JsonrpcParam SupplyDemandHedgingReq req) {
        if (StringUtils.isBlank(req.getStatTime())) {
            throw new BizException("版本不能为空");
        }
        supplyAndDemandDictService.addUnclassified(req);
        req.setOverView(true);
        if (StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())) {
            if (ListUtils.isEmpty(req.getProduct())) {
                req.setProduct(ListUtils.newArrayList("CRS", "CDB"));
            }
        }
        return inventoryService.queryInventoryOverview(req);
    }
}
