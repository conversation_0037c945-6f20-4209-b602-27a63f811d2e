package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.common.entity.BaseUserDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
@Table("bas_supply_and_demand_version")
public class BasVersionDO extends BaseUserDO {

    @Column("version_code")
    private String versionCode;

    @Column("definitive")
    private boolean definitive = false;

    @Column("start_year_month")
    private String startMonth;

    @Column("end_year_month")
    private String endMonth;

    @Column("type")
    private String type;

    @Column("product_category")
    private String productCategory;
}
