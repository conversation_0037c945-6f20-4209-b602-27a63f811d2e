package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.NotPreDeductInventoryData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.NotPreDeductInventoryReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.PreDeductTurnoverData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryAnalysisData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryAnalysisReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryCustomerData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.PreDeductAnalysisReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryTodayData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.TurnoverInventoryTodayReq;
import java.util.List;

public interface TurnoverInventoryAnalysisService {

    List<TurnoverInventoryAnalysisData> queryTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req);

    List<TurnoverInventoryCustomerData> queryTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req);

    List<PreDeductTurnoverData> queryPreDeductInventoryReport(PreDeductAnalysisReq req);

    List<NotPreDeductInventoryData> queryNotPreDeductInventoryReport(NotPreDeductInventoryReq req);

    TurnoverInventoryTodayData queryTurnoverAnalysisReportToday(TurnoverInventoryTodayReq req);

    DownloadBean exportTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req);

    DownloadBean exportTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req);

    DownloadBean exportPreDeductInventoryReport(PreDeductAnalysisReq req);

    DownloadBean exportNotPreDeductInventoryReport(NotPreDeductInventoryReq req);
}
