package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataAdjustDO;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
@ToString
public class ProductAdjustDemandVO{

    /**
     * 分区键，代表数据版本<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 年月 <br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 产品大类，例如CVM、CBS、数据库、GPU<br/>Column: [product]
     */
    @Column(value = "product_category")
    private String productCategory;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept", insertValueScript = "'(空值)'")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone", insertValueScript = "'(空值)'")
    private String warZone;
    /**
     * 通用客户简称<br/>Column: [common_customer_short_name]
     */
    @Column(value = "common_customer_short_name", insertValueScript = "'(空值)'")
    private String commonCustomerShortName;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title", insertValueScript = "'(空值)'")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    @Column(value = "country_name", insertValueScript = "'(空值)'")
    private String countryName;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name", insertValueScript = "'(空值)'")
    private String regionName;

    /**
     * 可用区类型<br/>Column: [zone_category]
     */
    @Column(value = "zone_category", insertValueScript = "'(空值)'")
    private String zoneCategory;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name", insertValueScript = "'(空值)'")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_category]
     */
    @Column(value = "instance_category", insertValueScript = "'(空值)'")
    private String instanceCategory;

    /**
     * 实例族<br/>Column: [instance_group]
     */
    @Column(value = "instance_group", insertValueScript = "'(空值)'")
    private String instanceGroup;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type", insertValueScript = "'(空值)'")
    private String instanceType;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type", insertValueScript = "'(空值)'")
    private String demandType;

    /**
     * 待购买核心数<br/>Column: [wait_buy_total_core]
     */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore = BigDecimal.ZERO;



    public static ProductDemandDataAdjustDO transform(ProductAdjustDemandVO vo) {
        ProductDemandDataAdjustDO ret = new ProductDemandDataAdjustDO();
        BeanUtils.copyProperties(vo, ret);
        ret.setDataSource("订单");
        return ret;
    }

}