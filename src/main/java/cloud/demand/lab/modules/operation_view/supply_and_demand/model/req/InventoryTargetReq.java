package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/23 15:22
 */
@Data
public class InventoryTargetReq {

    private String productCategory;

    private List<String> product;

    // 页面上所选的切片日期
    private List<String> statTime;

    private List<String> zoneCategory;

    private List<String> volumeType;

    private List<String> customhouseTitle;

    private List<String> countryName;

    private List<String> areaName;

    private List<String> regionName;

    private List<String> zoneName;

    private String uint;


    private List<String> dims;

    public static InventoryTargetReq transform(SupplyDemandHedgingReq req) {
        InventoryTargetReq ret = new InventoryTargetReq();

        List<String> statTime = ListUtils.newArrayList();
        if (StringUtils.isNotBlank(req.getStartStatTime())) {
            statTime.add(req.getStartStatTime());
        }
        if (StringUtils.isNotBlank(req.getEndStatTime())) {
            statTime.add(req.getEndStatTime());
        }
        if (StringUtils.isNotBlank(req.getStatTime())) {
            statTime.add(req.getStatTime());
        }

        ret.setProductCategory(req.getProductCategory());
        ret.setProduct(req.getProduct());
        ret.setZoneCategory(req.getZoneCategory());
        ret.setVolumeType(req.getVolumeType());
        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        ret.setCountryName(req.getCountryName());
        ret.setAreaName(req.getAreaName());
        ret.setRegionName(req.getRegionName());
        ret.setZoneName(req.getZoneName());
        ret.setStatTime(statTime);
        ret.setUint(req.getUnit());
        ret.setDims(req.getDims());
        return ret;
    }
}
