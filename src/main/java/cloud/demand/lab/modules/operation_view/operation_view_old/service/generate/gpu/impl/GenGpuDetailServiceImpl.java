package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.impl;

import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ReportOperationViewDetailDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.GenGpuDetailService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.model.GpuPlanDetailForCvmVO;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.model.GpuPlanDetailForMetalVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GenGpuDetailServiceImpl implements GenGpuDetailService {

    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private OperationViewCommonService commonService;

    @Override
    public void genGpuDetail(String statTime) {

    }

    private Map<String, ReportOperationViewDetailDO> getForMetal(String statTime){
        //   1、获取当日的线上、线下、总库存数据
        Map<String, ReportOperationViewDetailDO> newestInvMap = getNewestMetalInvMap(statTime);
        //  2、获取近一年时间范围内的总库存数据
        Map<String, ReportOperationViewDetailDO> byMonthInv = getLast12MonthMetalInvMap(statTime, newestInvMap);
        //  3、获取近一年时间范围内的总销售数据
        Map<String, ReportOperationViewDetailDO> byMonthSale = getLast12MonthMetalSaleMap(statTime, byMonthInv);
        //  3、获取By周售卖数据
        return fillLast13WeekDemandMetalData(statTime, byMonthSale);
    }

    private Map<String, ReportOperationViewDetailDO> getForCvm(String statTime){
        //   1、获取当日的线上、线下、总库存数据
        Map<String, ReportOperationViewDetailDO> newestInvMap = getNewestCvmInvMap(statTime);
        //  2、获取近一年时间范围内的总库存数据
        Map<String, ReportOperationViewDetailDO> byMonthInv = getLast12MonthCvmInvMap(statTime, newestInvMap);
        //  3、获取近一年时间范围内的总销售数据
        Map<String, ReportOperationViewDetailDO> byMonthSale = getLast12MonthCvmSaleMap(statTime, byMonthInv);
        //  3、获取By周售卖数据
        return fillLast13WeekDemandCvmData(statTime, byMonthSale);
    }

    private Map<String, ReportOperationViewDetailDO> merge(Map<String, ReportOperationViewDetailDO> cvmMap,
            Map<String, ReportOperationViewDetailDO> metalMap){

        if ((cvmMap == null && metalMap == null) || (cvmMap.isEmpty() && metalMap.isEmpty())){
            return new HashMap<>();
        }if ((cvmMap == null || cvmMap.isEmpty()) && metalMap != null){
            return metalMap;
        }else if ((metalMap == null || metalMap.isEmpty()) && cvmMap != null){
            return cvmMap;
        }

        Map<String, ReportOperationViewDetailDO> resultMap = new HashMap<>();
        Set<String> allKeys = new HashSet<>();
        allKeys.addAll(cvmMap.keySet());
        allKeys.addAll(metalMap.keySet());
        for (String key : allKeys) {
            ReportOperationViewDetailDO cvmMapDO = cvmMap.get(key);
            ReportOperationViewDetailDO metalMapDO = metalMap.get(key);
            ReportOperationViewDetailDO resultDO = null;
            if (cvmMapDO != null && metalMapDO != null){
                resultDO = ReportOperationViewDetailDO.merge(cvmMapDO, metalMapDO);
            }else if (cvmMapDO != null && metalMapDO == null){
                resultDO = cvmMapDO;
            }else if (cvmMapDO == null && metalMapDO != null){
                resultDO = metalMapDO;
            }
            resultMap.put(key, resultDO);
        }
        return resultMap;
    }

    /**
     * 生成当月最新库存数据Map
     */
    private Map<String, ReportOperationViewDetailDO> getNewestMetalInvMap(String statTime) {
        // 1. 第一步：获得线上和线下库存的条目
        List<GpuPlanDetailForMetalVO> all = rrpDBHelper.getAll(GpuPlanDetailForMetalVO.class,
                "WHERE stat_time=? AND product_type='裸金属' AND compute_type='GPU'\n" +
                        "AND indicator_code IN ('c1','c2','c3','d3','d6')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                statTime);

        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

        // 1.2 合并重复的项
        Map<String, GpuPlanDetailForMetalVO> resultMap = removeMetalRepeatData(all);

        // 1.3 to DO
        List<ReportOperationViewDetailDO> transform = ListUtils.transform(resultMap.values(), o -> o.toOperationViewDO(true));

        // 1.4 补全信息
        ListUtils.forEach(transform, o -> commonService.fillOtherFields(o, statTime));

        return ListUtils.toMap(transform, o -> o.getKey(), o -> o);
    }

    /**
     * 生成当月最新库存数据Map
     */
    private Map<String, ReportOperationViewDetailDO> getNewestCvmInvMap(String statTime) {
        // 1. 第一步：获得线上和线下库存的条目
        List<GpuPlanDetailForCvmVO> all = rrpDBHelper.getAll(GpuPlanDetailForCvmVO.class,
                "WHERE stat_time=? AND product_type='CVM' AND compute_type='GPU'\n" +
                        "AND indicator_code IN ('d1','d2','d3','e4','e8')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                statTime);

        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

        // 1.2 合并重复的项
        Map<String, GpuPlanDetailForCvmVO> resultMap = removeCvmRepeatData(all);

        // 1.3 to DO
        List<ReportOperationViewDetailDO> transform = ListUtils.transform(resultMap.values(), o -> o.toOperationViewDO(true));

        // 1.4 补全信息
        ListUtils.forEach(transform, o -> commonService.fillOtherFields(o, statTime));

        return ListUtils.toMap(transform, o -> o.getKey(), o -> o);
    }

    /**
     * 生成过去12个月的历史Map，包含库存、销售
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthMetalInvMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);
        //  获取对应m-n月份的天数
        Map<String, Integer> monthNumOfDays = commonService.getMonthNumOfDays(statTime, 12);

        // 1、获得线上和线下库存的条目
        List<GpuPlanDetailForMetalVO> all = rrpDBHelper.getAll(GpuPlanDetailForMetalVO.class,
                "WHERE stat_time between ? and ? AND product_type='裸金属' AND compute_type='GPU'\n" +
                        "AND indicator_code IN ('c1','c2','c3','d3','d6')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                timeInterval._1, timeInterval._2);
        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
        // 1.2 合并重复的项
        Map<String, GpuPlanDetailForMetalVO> resultMap = removeMetalRepeatData(all);

        Map<String, List<GpuPlanDetailForMetalVO>> map = ListUtils.groupBy(resultMap.values(), o -> o.getBaseGroupK());

        for (Map.Entry<String, List<GpuPlanDetailForMetalVO>> entry : map.entrySet()) {
            String key = entry.getKey();
            ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
            if (detailDO == null) {
                ReportOperationViewDetailDO history = entry.getValue().get(0).toOperationViewDO(false);
                commonService.fillOtherFields(history, statTime);
                detailDO = history;
            }
            for (GpuPlanDetailForMetalVO each : entry.getValue()) {
                String yearMonth = each.getYearMonth();
                Integer numOfDay = monthNumOfDays.get(yearMonth);
                Integer n = mReduceNMap.get(yearMonth);
                each.setOnlineLogicNum(AmountUtils.divideScale6(each.getOnlineLogicNum(), BigDecimal.valueOf(numOfDay)));
                each.setOfflineLogicNum(AmountUtils.divideScale6(each.getOfflineLogicNum(), BigDecimal.valueOf(numOfDay)));
                commonService.setHistoryInvByI(n, each, detailDO, ProductTypeEnum.GPU.getCode());
            }
            newestInvMap.put(key, detailDO);
        }
        return newestInvMap;
    }



    /**
     * 生成过去12个月的历史Map，包含库存、销售
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthMetalSaleMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);

        String statTimeDate = timeInterval._2;
        for (int i = 1; i <= 12; i++) {
            int year = DateUtils.getYear(DateUtils.parse(statTimeDate));
            int month = DateUtils.getMonth(DateUtils.parse(statTimeDate));
            String curYearMonth = year + "-" + (month < 10 ? "0" + month : month);
            String curMonthFirstDay = curYearMonth + "-01";

            String lastMonthLastDay = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.DATE, -1));

            List<GpuPlanDetailForMetalVO> curMonth = rrpDBHelper.getAll(GpuPlanDetailForMetalVO.class,
                    "WHERE stat_time = ? AND product_type='裸金属' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", statTimeDate);
            ListUtils.forEach(ListUtils.filter(curMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, GpuPlanDetailForMetalVO> curMonthMap = ListUtils.toMap(curMonth, o -> o.getBaseGroupK(), o -> o);

            List<GpuPlanDetailForMetalVO> lastMonth = rrpDBHelper.getAll(GpuPlanDetailForMetalVO.class,
                    "WHERE stat_time = ? AND product_type='裸金属' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", lastMonthLastDay);
            ListUtils.forEach(ListUtils.filter(lastMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, GpuPlanDetailForMetalVO> lastMonthMap = ListUtils.toMap(lastMonth, o -> o.getBaseGroupK(), o -> o);

            Set<String> allK = new HashSet<>();
            allK.addAll(lastMonthMap.keySet());
            allK.addAll(curMonthMap.keySet());

            for (String key : allK) {
                GpuPlanDetailForMetalVO lastMonthData = lastMonthMap.get(key);
                GpuPlanDetailForMetalVO curMonthData = curMonthMap.get(key);
                String yearMonth = year + "-" + month;
                Integer n = mReduceNMap.get(yearMonth);
                GpuPlanDetailForMetalVO result = GpuPlanDetailForMetalVO.getDemandVO(lastMonthData, curMonthData);
                ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
                if (detailDO == null && result != null) {
                    ReportOperationViewDetailDO history = result.toOperationViewDO(false);
                    commonService.fillOtherFields(history, statTime);
                    detailDO = history;
                }
                commonService.setHistorySaleByI(n, result, detailDO, ProductTypeEnum.GPU.getCode());
                newestInvMap.put(key, detailDO);
            }
            statTimeDate = lastMonthLastDay;
        }
        return newestInvMap;
    }

    /**
     * 生成过去12个月的历史Map，包含库存、销售
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthCvmInvMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);
        //  获取对应m-n月份的天数
        Map<String, Integer> monthNumOfDays = commonService.getMonthNumOfDays(statTime, 12);

        // 1、获得线上和线下库存的条目
        List<GpuPlanDetailForCvmVO> all = rrpDBHelper.getAll(GpuPlanDetailForCvmVO.class,
                "WHERE stat_time between ? and ? AND product_type='CVM' AND compute_type='GPU'\n" +
                        "AND indicator_code IN ('d1','d2','d3','e4','e8')\n" +
                        "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType",
                timeInterval._1, timeInterval._2);

        // 1.1 补全线下库存的好差呆类型
        ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
        // 1.2 合并重复的项
        Map<String, GpuPlanDetailForCvmVO> resultMap = removeCvmRepeatData(all);

        Map<String, List<GpuPlanDetailForCvmVO>> map = ListUtils.groupBy(resultMap.values(), o -> o.getBaseGroupK());

        for (Map.Entry<String, List<GpuPlanDetailForCvmVO>> entry : map.entrySet()) {
            String key = entry.getKey();
            ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
            if (detailDO == null) {
                ReportOperationViewDetailDO history = entry.getValue().get(0).toOperationViewDO(false);
                commonService.fillOtherFields(history, statTime);
                detailDO = history;
            }

            for (GpuPlanDetailForCvmVO each : entry.getValue()) {
                String yearMonth = each.getYearMonth();
                Integer n = mReduceNMap.get(yearMonth);
                Integer numOfDay = monthNumOfDays.get(yearMonth);
                each.setOnlineLogicNum(AmountUtils.divideScale6(each.getOnlineLogicNum(), BigDecimal.valueOf(numOfDay)));
                each.setOfflineLogicNum(AmountUtils.divideScale6(each.getOfflineLogicNum(), BigDecimal.valueOf(numOfDay)));
                commonService.setHistoryInvByI(n, each, detailDO, ProductTypeEnum.GPU.getCode());
            }
            newestInvMap.put(key, detailDO);
        }
        return newestInvMap;
    }


    /**
     * 生成过去12个月的历史Map，包含库存、销售
     */
    private Map<String, ReportOperationViewDetailDO> getLast12MonthCvmSaleMap(String statTime,
            Map<String, ReportOperationViewDetailDO> newestInvMap) {
        //  获取过去12个月的起止时间(end为上月月末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByMonth(statTime, 12);
        //  获取对应年月为m的基础上减掉的月份n
        Map<String, Integer> mReduceNMap = commonService.getMReduceNMonthMap(statTime);


        String statTimeDate = timeInterval._2;
        for (int i = 1; i <= 12; i++) {
            int year = DateUtils.getYear(DateUtils.parse(statTimeDate));
            int month = DateUtils.getMonth(DateUtils.parse(statTimeDate));
            String curYearMonth = year + "-" + (month < 10 ? "0" + month : month);
            String curMonthFirstDay = curYearMonth + "-01";

            String lastMonthLastDay =
                    DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(curMonthFirstDay), Calendar.DATE, -1));

            List<GpuPlanDetailForCvmVO> curMonth = rrpDBHelper.getAll(GpuPlanDetailForCvmVO.class,
                    "WHERE stat_time = ? AND product_type='CVM' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b11', 'b12', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", statTimeDate);
            // 1.1 补全线下库存的好差呆类型
            ListUtils.forEach(ListUtils.filter(curMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, GpuPlanDetailForCvmVO> curMonthMap = ListUtils.toMap(curMonth, o -> o.getBaseGroupK(), o -> o);

            List<GpuPlanDetailForCvmVO> lastMonth = rrpDBHelper.getAll(GpuPlanDetailForCvmVO.class,
                    "WHERE stat_time = ? AND product_type='CVM' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b11', 'b12', 'b21', 'b22')\n" +
                            "GROUP BY `year_month`, device_type,customhouse_title,area_name,region_name,zone_name,materialType", lastMonthLastDay);
            // 1.1 补全线下库存的好差呆类型
            ListUtils.forEach(ListUtils.filter(lastMonth, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));
            Map<String, GpuPlanDetailForCvmVO> lastMonthMap = ListUtils.toMap(lastMonth, o -> o.getBaseGroupK(), o -> o);

            Set<String> allK = new HashSet<>();
            allK.addAll(lastMonthMap.keySet());
            allK.addAll(curMonthMap.keySet());

            for (String key : allK) {
                GpuPlanDetailForCvmVO lastMonthData = lastMonthMap.get(key);
                GpuPlanDetailForCvmVO curMonthData = curMonthMap.get(key);
                String yearMonth = year + "-" + month;
                Integer n = mReduceNMap.get(yearMonth);
                GpuPlanDetailForCvmVO result = GpuPlanDetailForCvmVO.getDemandVO(lastMonthData, curMonthData);
                ReportOperationViewDetailDO detailDO = newestInvMap.get(key);
                if (detailDO == null && result != null) {
                    ReportOperationViewDetailDO history = result.toOperationViewDO(false);
                    commonService.fillOtherFields(history, statTime);
                    detailDO = history;
                }
                commonService.setHistorySaleByI(n, result, detailDO, ProductTypeEnum.GPU.getCode());
                newestInvMap.put(key, detailDO);
            }
            statTimeDate = lastMonthLastDay;
        }
        return newestInvMap;
    }

    private Map<String, ReportOperationViewDetailDO> fillLast13WeekDemandCvmData(String statTime,
            Map<String, ReportOperationViewDetailDO> monthData){
        //  获取过去13周的起止时间(end为上周周末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByWeek(statTime, 14);

        String start = timeInterval._1;
        Date curStart = DateUtils.parse(start);
        Date curEnd = DateUtils.addTime(curStart, Calendar.DATE, 6);

        Map<String, Map<String, BigDecimal>> cacheMap = new HashMap<>();

        for (int i = 14; i >= 1; i--) {
            List<GpuPlanDetailForCvmVO> all = rrpDBHelper.getAll(GpuPlanDetailForCvmVO.class,
                    "WHERE stat_time between ? and ? AND product_type='CVM' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b11', 'b12', 'b21', 'b22')\n" +
                            "GROUP BY device_type,customhouse_title,area_name,region_name,zone_name,materialType"
                    , DateUtils.formatDate(curEnd), DateUtils.formatDate(curEnd));

            ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

            Map<String, List<GpuPlanDetailForCvmVO>> saleMap = ListUtils.groupBy(all, o -> o.getBaseGroupK());

            Map<String, BigDecimal> curCacheMap = new HashMap<>();
            for (Map.Entry<String, List<GpuPlanDetailForCvmVO>> entry : saleMap.entrySet()) {
                curCacheMap.put(entry.getKey(), NumberUtils.sum(entry.getValue(), o -> o.getSaleLogicNum()));
            }

            cacheMap.put("m-" + i, curCacheMap);

            for (Map.Entry<String, List<GpuPlanDetailForCvmVO>> entry : saleMap.entrySet()) {
                if (i == 14){
                    break;
                }
                String key = entry.getKey();
                ReportOperationViewDetailDO detailDO = monthData.get(key);
                if (detailDO == null) {
                    ReportOperationViewDetailDO saleNewDO = entry.getValue().get(0).toOperationViewDO(false);
                    commonService.fillOtherFields(saleNewDO, statTime);
                    detailDO = saleNewDO;
                }

                //  合并list为一个DTO对象
                GpuPlanDetailForCvmVO dto = GpuPlanDetailForCvmVO.mergeListSaleDTO(entry.getValue());
                BigDecimal decimal = cacheMap.get("m-" + (i + 1)).get(key);
                //  获取已经cache了的往周map，取m-(n+1)周的值，作为上周周期的销量
                BigDecimal lastCycleSaleLogicNum = decimal == null ? BigDecimal.ZERO : decimal;
                //  获取本周周期的销量
                BigDecimal curCycleSaleLogicNum = dto.getSaleLogicNum();
                //  取变化量作为销净增(简单记录下:demand_w_1 = sale_w_1 - sale_w_2
                dto.setSaleLogicNum(curCycleSaleLogicNum.subtract(lastCycleSaleLogicNum));
                commonService.setHistoryDemandByI(i, dto, detailDO, ProductTypeEnum.GPU.getCode());
                monthData.put(key, detailDO);
            }
            curStart = DateUtils.addTime(curStart, Calendar.DATE, 7);
            curEnd = DateUtils.addTime(curEnd, Calendar.DATE, 7);
        }
        return monthData;
    }



    private Map<String, ReportOperationViewDetailDO> fillLast13WeekDemandMetalData(String statTime,
            Map<String, ReportOperationViewDetailDO> monthData){
        //  获取过去13周的起止时间(end为上周周末)
        Tuple2<String, String> timeInterval = commonService.getTimeIntervalByWeek(statTime, 14);

        String start = timeInterval._1;
        Date curStart = DateUtils.parse(start);
        Date curEnd = DateUtils.addTime(curStart, Calendar.DATE, 6);

        Map<String, Map<String, BigDecimal>> cacheMap = new HashMap<>();

        for (int i = 14; i >= 1; i--) {
            List<GpuPlanDetailForMetalVO> all = rrpDBHelper.getAll(GpuPlanDetailForMetalVO.class,
                    "WHERE stat_time between ? and ? AND product_type='裸金属' AND compute_type='GPU'\n" +
                            "AND indicator_code IN ('b1', 'b21', 'b22')\n" +
                            "GROUP BY device_type,customhouse_title,area_name,region_name,zone_name,materialType"
                    , DateUtils.formatDate(curEnd), DateUtils.formatDate(curEnd));

            ListUtils.forEach(ListUtils.filter(all, o -> StringTools.isBlank(o.getMaterialType())),
                    o -> o.setMaterialType(dictService.getAllGoodDeviceType().containsKey(o.getDeviceName()) ? "好料" : "差料"));

            Map<String, List<GpuPlanDetailForMetalVO>> saleMap = ListUtils.groupBy(all, o -> o.getBaseGroupK());

            Map<String, BigDecimal> curCacheMap = new HashMap<>();
            for (Map.Entry<String, List<GpuPlanDetailForMetalVO>> entry : saleMap.entrySet()) {
                curCacheMap.put(entry.getKey(), NumberUtils.sum(entry.getValue(), o -> o.getSaleLogicNum()));
            }

            cacheMap.put("m-" + i, curCacheMap);

            for (Map.Entry<String, List<GpuPlanDetailForMetalVO>> entry : saleMap.entrySet()) {
                if (i == 14){
                    break;
                }
                String key = entry.getKey();
                ReportOperationViewDetailDO detailDO = monthData.get(key);
                if (detailDO == null) {
                    ReportOperationViewDetailDO saleNewDO = entry.getValue().get(0).toOperationViewDO(false);
                    commonService.fillOtherFields(saleNewDO, statTime);
                    detailDO = saleNewDO;
                }

                //  合并list为一个DTO对象
                GpuPlanDetailForMetalVO dto = GpuPlanDetailForMetalVO.mergeListSaleDTO(entry.getValue());
                BigDecimal decimal = cacheMap.get("m-" + (i + 1)).get(key);
                //  获取已经cache了的往周map，取m-(n+1)周的值，作为上周周期的销量
                BigDecimal lastCycleSaleLogicNum = decimal == null ? BigDecimal.ZERO : decimal;
                //  获取本周周期的销量
                BigDecimal curCycleSaleLogicNum = dto.getSaleLogicNum();
                //  取变化量作为销净增(简单记录下:demand_w_1 = sale_w_1 - sale_w_2
                dto.setSaleLogicNum(curCycleSaleLogicNum.subtract(lastCycleSaleLogicNum));
                commonService.setHistoryDemandByI(i, dto, detailDO, ProductTypeEnum.GPU.getCode());
                monthData.put(key, detailDO);
            }
            curStart = DateUtils.addTime(curStart, Calendar.DATE, 7);
            curEnd = DateUtils.addTime(curEnd, Calendar.DATE, 7);
        }
        return monthData;
    }

    /**
     * 相同维度合并
     */
    public Map<String, GpuPlanDetailForMetalVO> removeMetalRepeatData(List<GpuPlanDetailForMetalVO> all){
        Map<String, List<GpuPlanDetailForMetalVO>> map = ListUtils.groupBy(all, o -> o.getGroupK());
        Map<String, GpuPlanDetailForMetalVO> resultMap = new HashMap<>();

        for (Map.Entry<String, List<GpuPlanDetailForMetalVO>> entry : map.entrySet()) {
            List<GpuPlanDetailForMetalVO> value = entry.getValue();
            if (entry.getValue().size() > 1){
                GpuPlanDetailForMetalVO copyOne = value.get(0).copyOne();
                copyOne.setOnlineLogicNum(NumberUtils.sum(value, o -> o.getOnlineLogicNum()));
                copyOne.setOfflineLogicNum(NumberUtils.sum(value, o -> o.getOfflineLogicNum()));
                resultMap.put(entry.getKey(), copyOne);
            }else {
                resultMap.put(entry.getKey(), value.get(0));
            }
        }
        return resultMap;
    }

    /**
     * 相同维度合并
     */
    public Map<String, GpuPlanDetailForCvmVO> removeCvmRepeatData(List<GpuPlanDetailForCvmVO> all){
        Map<String, List<GpuPlanDetailForCvmVO>> map = ListUtils.groupBy(all, o -> o.getGroupK());
        Map<String, GpuPlanDetailForCvmVO> resultMap = new HashMap<>();

        for (Map.Entry<String, List<GpuPlanDetailForCvmVO>> entry : map.entrySet()) {
            List<GpuPlanDetailForCvmVO> value = entry.getValue();
            if (entry.getValue().size() > 1){
                GpuPlanDetailForCvmVO copyOne = value.get(0).copyOne();
                copyOne.setOnlineLogicNum(NumberUtils.sum(value, o -> o.getOnlineLogicNum()));
                copyOne.setOfflineLogicNum(NumberUtils.sum(value, o -> o.getOfflineLogicNum()));
                resultMap.put(entry.getKey(), copyOne);
            }else {
                resultMap.put(entry.getKey(), value.get(0));
            }
        }
        return resultMap;
    }


}
