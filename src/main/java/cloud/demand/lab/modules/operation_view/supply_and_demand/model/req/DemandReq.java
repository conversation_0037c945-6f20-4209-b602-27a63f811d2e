package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.parse.DemandTypeParse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import report.utils.anno.WhereReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DemandReq {

    @WhereReport
    private String statTime;

    @WhereReport(sql = " year_month >= ? ")
    private String startYearMonth;

    @WhereReport(sql = " year_month <= ? ")
    private String endYearMonth;

    @WhereReport
    private String productCategory;

    @WhereReport
    private List<String> product;

    @WhereReport(parsers = DemandTypeParse.class, parseParams = "demand_type")
    private List<String> demandType;

    @WhereReport
    private List<String> industryDept;

    @WhereReport
    private List<String> warZone;

    @WhereReport
    private List<String> commonCustomerShortName;

    @WhereReport
    private List<String> customerUin;

    @WhereReport
    private List<String> zoneCategory;

    @WhereReport
    private List<String> instanceCategory;

    @WhereReport
    private List<String> instanceGroup;

    @WhereReport
    private List<String> instanceType;

    @WhereReport
    private List<String> customhouseTitle;

    @WhereReport
    private List<String> countryName;

    @WhereReport
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;


    @WhereReport
    private List<String> projectName;

    @WhereReport
    private List<String> demandScene;

    @WhereReport
    private List<String> volumeType;

    @WhereReport
    private List<String> diskType;

    @WhereReport
    private List<String> dbStorageType;//数据库存储类型：通用版、云盘版

    @WhereReport
    private List<String> dbDeployType;//数据库部署类型：通用型、独享型

    @WhereReport
    private List<String> dbFrameworkType;//数据库框架类型

    @WhereReport
    private List<String> isInner;//内外部：0-外部 1-内部

    @WhereReport
    private String demandCaliber = "共识需求";//人工校准、共识需求

    private String orderRange;

    private List<String> dims;

    private boolean isAdjustExcel = false;

    private boolean adjust = false;

    private boolean excludeFinishDemand = true;

    private String unit;//单位:台、逻辑核、卡、逻辑容量、逻辑内存量

    private boolean excel = false;

    private List<String> projectType;

    public static DemandReq transform(DemandCommonReq req, String statTime) {
        DemandReq ret = new DemandReq();
        BeanUtils.copyProperties(req, ret);
        ret.setStatTime(statTime);
        if(StringUtils.isEmpty(req.getUnit())){
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CVM.getName())){
                 ret.setUnit(UnitEnum.CORE.getName());
            }
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CBS.getName())){
                ret.setUnit(UnitEnum.STORAGE.getName());
            }
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())){
                ret.setUnit(UnitEnum.MEMORY.getName());
            }
        }
        ret.setAdjust(StringUtils.equals("人工校准",req.getDemandCaliber()));
        return ret;
    }

    public static DemandReq transform(DemandTrendReq req, String statTime) {
        DemandReq ret = new DemandReq();
        BeanUtils.copyProperties(req, ret);
        ret.setStatTime(statTime);

        if(StringUtils.isEmpty(req.getStartYearMonth())){
            ret.setStartYearMonth(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        if(StringUtils.isEmpty(req.getEndYearMonth())){
            ret.setEndYearMonth(LocalDate.now().plusMonths(3).format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        if(StringUtils.isEmpty(req.getUnit())){
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CVM.getName())){
                ret.setUnit(UnitEnum.CORE.getName());
            }
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.CBS.getName())){
                ret.setUnit(UnitEnum.STORAGE.getName());
            }
            if(StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())){
                ret.setUnit(UnitEnum.MEMORY.getName());
            }
        }
        ret.setAdjust(StringUtils.equals("人工校准",req.getDemandCaliber()));
        return ret;
    }


    public static String getYearMonth(Integer year, Integer month) {
        return year + "-" + fixNumber(month);
    }

    public static String fixNumber(int num) {
        if (num < 10 && num >= 0) {
            return "0" + num;
        }
        return "" + num;
    }

}
