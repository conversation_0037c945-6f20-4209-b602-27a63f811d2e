package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DeleteRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateRemarksReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ProductReportDimRemarksVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ProductReportDimRemarksService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/15 14:15
 */
@Slf4j
@JsonrpcController("/supply-demand/remarks")
public class ProductReportDimRemarksController {

    @Resource
    private ProductReportDimRemarksService remarksService;

    /**
     * 查询备注
     *
     * @param req 请求参数
     * @return 返回的备注
     */
    @RequestMapping
    public List<ProductReportDimRemarksVO> queryRemarks(@JsonrpcParam QueryRemarksReq req) {
        return remarksService.queryRemarks(req);
    }

    /**
     * 创建备注
     *
     * @param req 请求参数
     */
    @RequestMapping
    public int createRemarks(@JsonrpcParam CreateRemarksReq req) {
        return remarksService.createRemarks(req);
    }

    /**
     * 更新备注
     *
     * @param req 请求参数
     */
    @RequestMapping
    public int updateRemarks(@JsonrpcParam UpdateRemarksReq req) {
        return remarksService.updateRemarks(req);
    }

    /**
     * 通过id主键组删除备注
     */
    @RequestMapping
    public int deleteRemarks(@JsonrpcParam DeleteRemarksReq req) {
        return remarksService.deleteRemarks(req.getIds());
    }
}
