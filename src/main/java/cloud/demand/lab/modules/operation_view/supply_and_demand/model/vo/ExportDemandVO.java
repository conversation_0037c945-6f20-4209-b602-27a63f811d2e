package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import yunti.boot.exception.BizException;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/31 13:38
 */
@Data
public class ExportDemandVO {

    private List<List<String>> headList;

    private List<List<Object>> dataList;

    public ExportDemandVO init(List<ExportDemandItemVO> data, List<String> headValueList, List<String> feildList) {
        ExportDemandVO vo = new ExportDemandVO();
        vo.setHeadList(buildHeadList(headValueList));
        vo.setDataList(buildDataList(data,feildList));
        return vo;
    }

    private static List<List<String>> buildHeadList(List<String> headValueList) {
        List<List<String>> headList = ListUtils.newArrayList();
        for(String value : headValueList) {
            headList.add(ListUtils.newArrayList(value));
        }
        return headList;
    }

    private static List<List<Object>> buildDataList(List<ExportDemandItemVO> data,List<String> fieldNameList) {
        List<List<Object>> rows = ListUtils.newArrayList();
        for (ExportDemandItemVO item : data) {
            List<Object> row = ListUtils.newArrayList();
            for(String fieldName : fieldNameList) {
                try {
                    Field field = ExportDemandItemVO.class.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    row.add(field.get(item));
                } catch (IllegalAccessException | NoSuchFieldException e) {
                    throw new BizException("fieldName:" + fieldName + " is not exist");
                }
            }
            rows.add(row);
        }
        return rows;
    }

}
