package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.operation_view.entity.p2p.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.lab.modules.operation_view.entity.web.common.Result;
import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.AdsMckForecastSummaryDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.ConfigureMckRestockManualConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryManualConfigDetailReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryManualConfigDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockDetailReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockForecastDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockForecastReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockInventoryDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryMckRestockSupplyDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryOperationActionDetailResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryOperationActionDetailResp.Item;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryTotalDetailSummaryReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.mck_restock.QueryTotalDetailSummaryResp;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMckRestockManualConfig;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock.CommonRestockDataItemDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock.CommonRestockForecastDataItemDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.mck_restock.OperationActionDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.ForecastTypeEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthMckRestockService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthMckForecastMonthlySafeDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsInventoryHealthSupplySummaryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsSafeInventoryHistoryMonthlyDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigSnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

@Slf4j
@Service
public class InventoryHealthMckRestockServiceImpl implements InventoryHealthMckRestockService {
    @Resource
    private DictService dictService;
    @Resource
    private InventoryHealthDictService inventoryHealthDictService;
    @Resource
    private DBHelper ckcldDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private OutsideViewOldService outsideViewOldService;

    private final ExecutorService threadPool = Executors.newFixedThreadPool(4);

    @Override
    public QueryMckRestockResp queryMckRestockReport(QueryMckRestockReq req) {
        boolean isContainDimension = false;
        List<String> temp = new ArrayList<>();
        if (ListUtils.isNotEmpty(req.getGroupByDimension())) {
            isContainDimension = true;
            temp = req.getGroupByDimension();
            req.setGroupByDimension(null);
        }
        //查看安全库存切换字段是否为空，为空则设置为默认值
        if (req.getSwitchSafeInventory() == null) {
            req.setSwitchSafeInventory("mckForecastMonthlySafe");
        }

        //查看复选框是否为空，为空则设置为默认值
        if (req.getIsContainFullSupply() == null) {
            req.setIsContainFullSupply(false);
        }

        //查看头部客户需求量筛选框是否为空，为空则设置为默认值
        Map<String, String> demandMap = new HashMap<>();
        demandMap.put("订单", "预约单");
        demandMap.put("预测", "大客户");
        if (req.getDemandSelect() == null) {
            req.setDemandSelect("预约单");
        }else if (!req.getDemandSelect().equals("MAX")){
            req.setDemandSelect(demandMap.get(req.getDemandSelect()));
        }

        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();

        // 1. 系统取数
        //  1.1 库存实际数
        Map<String, List<CommonRestockDataItemDO>> actualInvDOSWeekMap = getActualInvByWeek(req, allCvmType);
        //  1.2 供应汇总表
        Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> supplySummaryDfDOSWeekMap = getSupplySummary(req, allCvmType);
        //  1.3 取当天的所有人工调整以及原因
        Map<String, List<InventoryHealthMckRestockManualConfig>> manualConfigDOSWeekMap = getInventoryHealthMckRestockManualConfig(req, allCvmType);
        //  1.4 取当天的需求数据，需求数据包月先不均摊，中长尾均摊到周。
        Map<String, List<CommonRestockForecastDataItemDO>> mckForecastSummaryDfDOSWeekMap = getMckForecastSummaryDfDOS(req, allCvmType);
        //  1.5 取预测周转库存（or 计算周转库存）
        //  1.5 TODO 预测周转库存等指标，暂时按照补货模型 excel 逻辑计算，后面底数和逻辑统一后，从预测周转库存表取数。业务王丽丽反馈暂时两套独立，等后续运营一段时间后再看底数统一的方案

        //  1.6 取预测安全库存（or 计算预测安全库存）
        Map<String, List<DwsInventoryHealthMckForecastMonthlySafeDfDO>> mckForecastMonthlySafeInvDfDOSWeekMap = getMckForecastMonthlySafeInvDfDOS(req, allCvmType);
        //  1.7 取上一周12周平均周净峰
        Map<String, List<CommonRestockDataItemDO>> historyPeakWeekMap = getHistoryPeakByWeek(req, allCvmType);
        //  1.8 取弹性备货配额
        Map<String, List<DwsBufferSafeInventoryDfDO>> bufferSafeInventoryDfDOWeekMap = getBufferSafeInventoryDfDOS(req, allCvmType);

        //1.9 如果安全库存需要转换成历史包月安全库存，取历史包月安全库存,同时取运营视图中的人工调整
        Map<String, List<DwsSafeInventoryHistoryMonthlyDfDO>> safeInventoryHistoryMonthlyMap = new HashMap<>();
        Map<String, BigDecimal> manualConfigHistoryMap = new HashMap<>();
        if (req.getSwitchSafeInventory().equals("safeInventoryHistoryMonthly")) {
            safeInventoryHistoryMonthlyMap = getSafeInventoryHistoryMonthly(req, allCvmType);
            manualConfigHistoryMap = getaManualConfigHistory(req, allCvmType);
        }

        //1.10 获取运营动作
        Map<String, List<QueryOperationActionDetailResp.Item>> operationActionMap = getOperationActionMap(req);


        // 汇总所有的机型 + 可用区
        Set<String> allInstanceTypes = new HashSet<>();
        allInstanceTypes.addAll(actualInvDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(supplySummaryDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(manualConfigDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(mckForecastSummaryDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(mckForecastMonthlySafeInvDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(historyPeakWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        allInstanceTypes.addAll(bufferSafeInventoryDfDOWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        if (ListUtils.isNotEmpty(safeInventoryHistoryMonthlyMap)) {
            allInstanceTypes.addAll(safeInventoryHistoryMonthlyMap.entrySet().stream().map(o -> o.getValue().get(0).getInstanceType()).collect(Collectors.toList()));
        }
        Set<String> allZoneNames = new HashSet<>();
        allZoneNames.addAll(actualInvDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(supplySummaryDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(manualConfigDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(mckForecastSummaryDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(mckForecastMonthlySafeInvDfDOSWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(historyPeakWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        allZoneNames.addAll(bufferSafeInventoryDfDOWeekMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        if (ListUtils.isNotEmpty(safeInventoryHistoryMonthlyMap)) {
            allZoneNames.addAll(safeInventoryHistoryMonthlyMap.entrySet().stream().map(o -> o.getValue().get(0).getZoneName()).collect(Collectors.toList()));
        }

        // 2. 取未来 13 周（包括当前周），一共 14 周，遍历每一周
        List<HolidayWeekInfoDTO> weekInfoDTOS = getWeeks(req);

        // 每周的结果映射，（产品类型 + 机型 + 可用区 + 周）作为键
        Map<String, QueryMckRestockResp.Item> weekRespMap = new HashMap<>();

        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where status = ?",
                "PROCESS");

        for (HolidayWeekInfoDTO holidayWeekInfoDTO : weekInfoDTOS) {
            // 分别计算每一周的指标，对于所有的机型和可用区
            for (String instanceType : allInstanceTypes) {
                for (String zoneName : allZoneNames) {
                    // 2.1  处理期初库存
                    this.insertOrUpdateBeginInventory(actualInvDOSWeekMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                    // 2.2 处理供应
                    this.insertOrUpdateSupply(supplySummaryDfDOSWeekMap, manualConfigDOSWeekMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                    // 2.3 处理需求
                    this.insertOrUpdateDemand(mckForecastSummaryDfDOSWeekMap, manualConfigDOSWeekMap, historyPeakWeekMap, holidayWeekInfoDTO, instanceType, zoneName, req.getDemandSelect(), weekRespMap, pplVersion);
                    // 2.4 处理期末库存
                    this.insertOrUpdateEndInventory(mckForecastMonthlySafeInvDfDOSWeekMap, actualInvDOSWeekMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                    // 2.5 处理预测安全库存和缺口
                    this.insertOrUpdateSafeInventory(bufferSafeInventoryDfDOWeekMap, manualConfigDOSWeekMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                    // 2.6 如果安全库存取历史安全库存，处理历史安全库存
                    if (req.getSwitchSafeInventory().equals("safeInventoryHistoryMonthly")) {
                        this.insertOrUpdateHistorySafeInventory(safeInventoryHistoryMonthlyMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap, manualConfigHistoryMap);
                    }
                    // 2.7 处理人工调整原因
                    this.handleManualConfigReason(manualConfigDOSWeekMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                    //2.8 处理运营动作
                    this.handleOperationActionDetail(operationActionMap, holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
                }
            }
        }

        List<QueryMckRestockResp.Item> weekRespItems = weekRespMap.values().stream().collect(Collectors.toList());
        QueryMckRestockResp resp = new QueryMckRestockResp();
        if (!isContainDimension) {
            resp.setData(weekRespItems);
        }else {
            req.setGroupByDimension(temp);
            resp.setData(mergeResultsByDimension(weekRespItems, req));
        }
        return resp;
    }

    private Map<String, BigDecimal> getaManualConfigHistory(QueryMckRestockReq req, List<CvmType> allCvmType) {
        String statTime = req.getDate().toString();
        List<InventoryHealthManualConfigSnapshotDO> all = demandDBHelper.getAll(
                InventoryHealthManualConfigSnapshotDO.class, "where stat_time = ?", statTime);
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return ListUtils.toMap(all, o ->
                        Strings.join("@", o.getZoneName(), o.getInstanceType()),
                InventoryHealthManualConfigSnapshotDO::getNum);
    }

    private void handleOperationActionDetail(Map<String, List<QueryOperationActionDetailResp.Item>> operationActionMap, HolidayWeekInfoDTO holidayWeekInfoDTO, String instanceType, String zoneName, Map<String, QueryMckRestockResp.Item> weekRespMap) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        String start = holidayWeekInfoDTO.getStartDate();
        String end = holidayWeekInfoDTO.getEndDate();
        String key = String.join("@", zoneName, instanceType);
        List<QueryOperationActionDetailResp.Item> actions = operationActionMap.get(key);
        if (ListUtils.isNotEmpty(actions)) {
            List<QueryOperationActionDetailResp.Item> collect = actions.stream().filter(o -> {
                String date = o.getUseTime();
                return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;
            }).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(collect)) {
                item.setIsContainData(true);
            }else {
                item.setIsContainData(false);
            }
        }else {
            item.setIsContainData(false);
        }
    }


    /**
     * 按照stat_time获取所有运营动作，按照可用区+实例类型进行聚合
     */
    private Map<String, List<QueryOperationActionDetailResp.Item>> getOperationActionMap(QueryMckRestockReq req) {
        //获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/operation_action_detail.sql");
        Map<String, Object> param = new HashMap<>();
        param.put("statTime",req.getDate().toString());
        //这里先不根据时间范围进行查询
        sql = sql.replace("${START_END}", "");
        //建立一个境内-国内 境外-海外的映射
        Map<String, String> customTitle = new HashMap<>();
        customTitle.put("境内", "国内");
        customTitle.put("境外", "海外");
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            List<String> collect = req.getCustomhouseTitle().stream().map(o -> {
                String str = customTitle.get(o);
                if (str != null) {
                    return str;
                }
                return o;
            }).collect(Collectors.toList());
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "and area_type in (:area)");
            param.put("area", collect);
        } else {
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "");
        }
        List<OperationActionDetailDO> all = ckcubesDBHelper.getRaw(OperationActionDetailDO.class, sql, param);
        List<QueryOperationActionDetailResp.Item> data = new ArrayList<>();
        //设置数据并过滤
        Map<String, StaticZoneDO> zoneInfoMap = dictService.getCampus2ZoneInfoMap();
        all.forEach(o ->{
            QueryOperationActionDetailResp.Item item = new QueryOperationActionDetailResp.Item();
            //从campus中获取zone_name area_name regin_name
            StaticZoneDO staticZoneDO = zoneInfoMap.get(o.getCampus());
            if (staticZoneDO != null) {
                item.setZoneName(staticZoneDO.getZoneName());
                item.setAreaName(staticZoneDO.getAreaName());
                item.setRegionName(staticZoneDO.getRegionName());
            }

            //设置实例类型
            item.setDeviceType(o.getDeviceType());
            String instanceType = dictService.getCsigInstanceTypeByDeviceType(o.getDeviceType());
            if (instanceType != null) {
                item.setInstanceType(instanceType);
            }

            //设置时间
            item.setCreateTime(o.getCreateTime());
            item.setExpectDeliveryTime(o.getExpectDeliveryTime());
            item.setUseTime(o.getUseTime());

            //设置其余信息
            item.setQuotaId(o.getQuotaId());
            item.setIndustryDept(o.getIndustryDept());
            item.setCustomName(o.getCustomName());

            //设置核数
            item.setDemandCoreNum(o.getDemandCoreNum());
            item.setNoArrivalCoreNum(o.getNoArrivalCoreNum());
            data.add(item);
        });
        return ListUtils.toMapList(data, o -> String.join("@",o.getZoneName(), o.getInstanceType()), o -> o);
    }


    /**
     * 填充默认人工调整原因
     */
    private void handleManualConfigReason(Map<String, List<InventoryHealthMckRestockManualConfig>> manualConfigDOSWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        String key = this.getKey(item.getProductType(), instanceType, zoneName, holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        List<InventoryHealthMckRestockManualConfig> config = manualConfigDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(config)) {
            //按照instanceType、zoneName、year、week聚合的config只有一个
            InventoryHealthMckRestockManualConfig manualConfig = config.get(0);
            item.setDemandManualConfigReason(manualConfig.getDemandManualConfigReason());
            item.setSupplyManualConfigReason(manualConfig.getSupplyManualConfigReason());
            item.setSafeInventoryManualConfigReason(manualConfig.getSafeInventoryManualConfigReason());
        }
    }

    /**
     * 将预测安全库存变更为历史安全库存
     */
    private void insertOrUpdateHistorySafeInventory(Map<String, List<DwsSafeInventoryHistoryMonthlyDfDO>> safeInventoryHistoryMonthlyMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap,
            Map<String, BigDecimal> manualConfigHistoryMap) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        String key = String.join("@", "CVM", instanceType, zoneName);
        List<DwsSafeInventoryHistoryMonthlyDfDO> resultList = safeInventoryHistoryMonthlyMap.get(key);
        BigDecimal bigDecimal = manualConfigHistoryMap.get(String.join("@", zoneName, instanceType));
        BigDecimal manual = bigDecimal == null ? BigDecimal.ZERO : bigDecimal;
        if (ListUtils.isNotEmpty(resultList)) {
            //首先修改安全库存以及安全库存总数
            BigDecimal monthSafe = NumberUtils.sum(resultList, o -> o.getMonthlySafetyInv());
            BigDecimal monthlySafeInventory = item.getMonthlySafeInventory();
            item.setMonthlySafeInventory(monthSafe);
            //接着修改人工调整
            BigDecimal safeInventoryManualConfig = item.getSafeInventoryManualConfig() == null? BigDecimal.ZERO: item.getSafeInventoryManualConfig();
            item.setSafeInventoryManualConfig(manual);
            BigDecimal sum = item.getSafeInventoryTotal().subtract(monthlySafeInventory).add(monthSafe).subtract(safeInventoryManualConfig).add(manual);
            item.setSafeInventoryTotal(sum);
            //接着修改整体缺口
            BigDecimal endInventory = item.getEndInventory();
            item.setTotalGap(endInventory.subtract(sum));

        }
    }

    /**
     * 按照机型 + 可用区维度查询历史包月安全库存，算法使用MCK历史&预测需求
     */
    private Map<String, List<DwsSafeInventoryHistoryMonthlyDfDO>> getSafeInventoryHistoryMonthly(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", req.getDate().toString());
        condition.and("algorithm = ?", "MCK历史&预测需求");
        condition.and("product_type = ?", "CVM");
        condition.and("customer_custom_group = ?", "ALL");
        condition.and(req.genBasicCondition());
        List<DwsSafeInventoryHistoryMonthlyDfDO> all = ckcldDBHelper.getAll(DwsSafeInventoryHistoryMonthlyDfDO.class, condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        //按照机型加可用区进行分类，由于历史包月安全库存每周相同，因此不考虑周维度
        return ListUtils.toMapList(all, o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()), o -> o);
    }

    @Override
    public QueryMckRestockInventoryDetailResp queryMckRestockReportInventoryDetail(QueryMckRestockDetailReq req) {
        WhereSQL condition = getInventoryCondition(req);

        List<DwsInventoryHealthSupplySummaryDfDO> data = ckcldDBHelper.getAll(DwsInventoryHealthSupplySummaryDfDO.class, condition.getSQL(), condition.getParams());

        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            data = data.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }

        QueryMckRestockInventoryDetailResp result = new QueryMckRestockInventoryDetailResp();
        result.setData(data);
        return result;
    }

    @Override
    public QueryMckRestockSupplyDetailResp queryMckRestockReportSupplyDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType) {
        QueryMckRestockSupplyDetailResp result = new QueryMckRestockSupplyDetailResp();
        //查看req，是否包含累计数据
        if (req.getIsCumulative() == null) {
            req.setIsCumulative(false);
        }
        Boolean isCumulative = req.getIsCumulative();
        List<DwsInventoryHealthSupplySummaryDfDO> data = new ArrayList<>();
        if (isCumulative) {
            //如果包含累计，则获取T0-Tindex的数据
            Integer index = req.getWeekIndex();
            List<HolidayWeekInfoDTO> weeksByIndex = getWeeksByIndex(req.getDate(), index);
            for (HolidayWeekInfoDTO week : weeksByIndex) {
                req.setYear(week.getYear());
                req.setWeek(week.getWeek());
                req.setWeekIndex(week.getWeekNFromNow());
                List<DwsInventoryHealthSupplySummaryDfDO> detail = getMckRestockReportSupplyDetail(req, allCvmType);
                if (ListUtils.isNotEmpty(detail)) {
                    data.addAll(detail);
                }
            }
        } else {
            data = getMckRestockReportSupplyDetail(req, allCvmType);
        }
        result.setData(data);
        return result;
    }

    public List<HolidayWeekInfoDTO> getWeeksByIndex(LocalDate date, Integer index) {
        String statTime = DateUtils.formatDate(date);
        List<HolidayWeekInfoDTO> holidayWeekInfo = ListUtils.newList();
        // 当周也算
        ResPlanHolidayWeekDO curWeek = dictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);
        if (index > 0) {
            List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(date, index);
            holidayWeekInfo.addAll(holidayWeekInfoDTOS);
        }
        return holidayWeekInfo;
    }

    public List<DwsInventoryHealthSupplySummaryDfDO> getMckRestockReportSupplyDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType) {
        Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> supplySummaryDetail = getSupplySummaryDetail(req, allCvmType);
        List<DwsInventoryHealthSupplySummaryDfDO> data = new ArrayList<>();
        List<String> zoneList = req.getZoneName();
        List<String> instanceList = req.getInstanceType();
        if (ListUtils.isEmpty(zoneList)) {
            zoneList = SpringUtil.getBean(OperationViewService2Impl.class).getZoneNamesByZoneCategory(req.getZoneCategory(),req.getDate().toString());
        }
        if (ListUtils.isEmpty(instanceList)) {
            instanceList = SpringUtil.getBean(OperationViewService2Impl.class).getInstanceTypesByInstanceCategory(req.getInstanceTypeCategory(), req.getDate().toString(), req.getCustomhouseTitle()).stream().collect(Collectors.toList());
        }
        for (String instance : instanceList) {
            for (String zoneName : zoneList) {
                String key = this.getKey("CVM", instance, zoneName, req.getYear(), req.getWeek());
                List<DwsInventoryHealthSupplySummaryDfDO> entity = supplySummaryDetail.get(key);
                if (ListUtils.isNotEmpty(entity)) {
                    data.addAll(entity);
                }
            }
        }
        return data;
    }

    @Override
    public QueryMckRestockForecastDetailResp queryMckRestockReportForecastDetail(QueryMckRestockForecastReq req, List<CvmType> allCvmType) {
        //查看req，是否包含累计数据
        if (req.getIsCumulative() == null) {
            req.setIsCumulative(false);
        }
        Boolean isCumulative = req.getIsCumulative();
        List<AdsMckForecastSummaryDfDO> data = new ArrayList<>();
        if (isCumulative) {
            //如果包含累计，则获取T0-Tindex的数据
            Integer index = req.getWeekIndex();
            List<HolidayWeekInfoDTO> weeksByIndex = getWeeksByIndex(req.getDate(), index);
            for (HolidayWeekInfoDTO week : weeksByIndex) {
                req.setYear(week.getYear());
                req.setWeek(week.getWeek());
                req.setWeekIndex(week.getWeekNFromNow());
                List<AdsMckForecastSummaryDfDO> detail = getMckRestockReportForecastDetail(req, allCvmType);
                if (ListUtils.isNotEmpty(detail)) {
                    data.addAll(detail);
                }
            }
        } else {
            data = getMckRestockReportForecastDetail(req, allCvmType);
        }
        QueryMckRestockForecastDetailResp result = new QueryMckRestockForecastDetailResp();
        result.setData(data);
        return result;
    }

    public List<AdsMckForecastSummaryDfDO> getMckRestockReportForecastDetail(QueryMckRestockForecastReq req, List<CvmType> allCvmType) {
        WhereSQL condition = getForecastCondition(req);

        if (!req.getType().equals(ForecastTypeEnum.LONG_TAIL_MODEL.getType())) {
            condition.and("begin_buy_year = ?", req.getYear());
            condition.and("begin_buy_week = ?", req.getWeek());
        }

        ResPlanHolidayWeekDO middleMonthWeek = dictService.getHolidayWeekInfoByYearWeek(req.getYear(), req.getWeek());
        String middleMonth = LocalDate.of(middleMonthWeek.getYear(), middleMonthWeek.getMonth(), 15).toString();
        ForecastTypeEnum Type = ForecastTypeEnum.getByType(req.getType());
        if (Type == null) {
            throw new ITException(String.format("未知需求类型，可选类型：【%s】",
                    "bigCustomerMonthly(Forecast,Order)，bigCustomerElastic,longTailIndustry(Forecast,Order),longTailModel,reservation"));
        }

        // bigCustomerMonthly, bigCustomerMonthlyForecast, bigCustomerMonthlyOrder, bigCustomerElastic
        // longTailIndustry, longTailIndustryImportant, longTailIndustryNormal, longTailIndustryForecast, longTailIndustryOrder
        // longTailModelImportant, longTailModelNormal, longTailModel
        switch (Type) {
            case BIG_CUSTOMER_MONTHLY:
                // 头部客户（包年包月）: "MAX" + "非弹性"
                condition.and("demand_type <> 'ELASTIC'");
                break;
            //头部客户（包年包月-预测）: "非弹性" + "大客户与中长尾"
            case BIG_CUSTOMER_MONTHLY_FORECAST:
                condition.and("demand_type <> 'ELASTIC'");
                condition.and("demand_source in ('大客户', '中长尾')");
                break;
            //头部客户（包年包月-订单）: "非弹性" + "预约单"
            case BIG_CUSTOMER_MONTHLY_ORDER:
                condition.and("demand_type <> 'ELASTIC'");
                condition.and("demand_source = '预约单'");
                break;
            case BIG_CUSTOMER_ELASTIC:
                // 头部客户（弹性）：筛选 "弹性" + "MAX"
                condition.and("demand_type = 'ELASTIC'");
                break;
            case LONG_TAIL_INDUSTRY:
                // 中长尾（行业报备）：客户简称不为模型预测内部客户以及模型预测外部客户 + MAX
                condition.and("customer_short_name not in ('模型预测内部客户','模型预测外部客户')");
                break;
            case LONG_TAIL_INDUSTRY_FORECAST:
                // 中长尾（行业报备-预测）：客户简称不为模型预测内部客户以及模型预测外部客户 + "大客户与中长尾"
                condition.and("demand_source in ('大客户', '中长尾')");
                condition.and("customer_short_name not in ('模型预测内部客户','模型预测外部客户')");
                break;
            case LONG_TAIL_INDUSTRY_ORDER:
                // 中长尾（行业报备-订单）：客户简称不为模型预测内部客户以及模型预测外部客户 + "预约单"
                condition.and("demand_source = '预约单'");
                condition.and("customer_short_name not in ('模型预测内部客户','模型预测外部客户')");
                break;
            case LONG_TAIL_MODEL:
                // 中长尾（模型预测）：客户简称为模型预测内部客户以及模型预测外部客户 + "middleMonth"
                condition.and("begin_buy_date = ?", middleMonth);
                condition.and("customer_short_name in ('模型预测内部客户','模型预测外部客户')");
                break;
            case RESERVATION:
                // 预约单：
                // - 取 “预约单”
                // - 取 “MAX”
                condition.and("demand_source = '预约单'");
                condition.and("compare = 'MAX'");
                break;
        }

        List<AdsMckForecastSummaryDfDO> data = ckcldStdCrpDBHelper.getAll(AdsMckForecastSummaryDfDO.class, condition.getSQL(), condition.getParams());

        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            data = data.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }

        //事先对数据进行处理
        if (req.getDemandDate().isBefore(LocalDate.parse("2024-07-08", DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
            HashSet<String> headCustomer = new HashSet<>(getBigCustomerShortName());
            data.forEach(o -> o.setIsHead(headCustomer.contains(o.getCustomerShortName())? 1 : 0));
        }
        if (req.getType().equals(ForecastTypeEnum.RESERVATION.getType())) {
            return data;
        }
        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where status = ?",
                "PROCESS");
        //剔除大客户部分数据
        data = data.stream()
                .filter(v -> !v.getDemandSource().equals("大客户") || v.getDemandType().equals("RETURN") ||
                        (v.getBeginBuyDate().getYear() > pplVersion.getDemandBeginYear() ||
                                (v.getBeginBuyDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && v.getBeginBuyDate().getMonthValue() >= pplVersion.getDemandBeginMonth())))
                .collect(Collectors.toList());
        HashSet<String> bigCustomer = ForecastTypeEnum.getBigCustomer();
        //对数据进行处理
        if (bigCustomer.contains(req.getType())) {
            // 如果是头部客户
            //取is_head为1的数据
            data = data.stream().filter(o -> o.getIsHead() == 1).collect(Collectors.toList());
            //有取MAX逻辑的在这里取
            if (req.getType().equals(ForecastTypeEnum.BIG_CUSTOMER_MONTHLY.getType())
                    || req.getType().equals(ForecastTypeEnum.BIG_CUSTOMER_ELASTIC.getType())) {
                List<AdsMckForecastSummaryDfDO> maxList = data.stream()
                        .filter(o -> o.getCompare().equals("MAX")
                                && (o.getBeginBuyDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getBeginBuyDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getBeginBuyDate().getMonthValue() >= pplVersion.getDemandBeginMonth()))
                        .collect(Collectors.toList());
                //在ppl版本月以外的取订单和大客户
                List<AdsMckForecastSummaryDfDO> collectList = data.stream()
                        .filter(o -> !(o.getBeginBuyDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getBeginBuyDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getBeginBuyDate().getMonthValue() >= pplVersion.getDemandBeginMonth()) && (
                                o.getDemandSource().equals("预约单") || o.getDemandSource().equals("大客户"))).collect(
                                Collectors.toList());
                if (ListUtils.isNotEmpty(collectList)) {
                    maxList.addAll(collectList);
                }
                data = maxList;
            }
        } else {
            // 如果是中长尾
            //取is_head = 0的数据
            data = data.stream().filter(o -> o.getIsHead() == 0).collect(Collectors.toList());
            //模型预测做均摊
            if (req.getType().equals(ForecastTypeEnum.LONG_TAIL_MODEL.getType())) {
                if (ListUtils.isNotEmpty(data)) {
                    data.forEach(o -> {
                        // 均摊到周，除以当月天数，再 * 7
                        BigDecimal monthDays = BigDecimal.valueOf(LocalDate.parse(middleMonth).lengthOfMonth());
                        BigDecimal avgCores = BigDecimal.valueOf(o.getTotalCore()).multiply(BigDecimal.valueOf(7)).divide(monthDays,RoundingMode.HALF_UP);
                        o.setTotalCore(avgCores.intValue());
                    });
                }
            }else if (req.getType().equals(ForecastTypeEnum.LONG_TAIL_INDUSTRY.getType())) {
                List<AdsMckForecastSummaryDfDO> maxList = data.stream()
                        .filter(o -> o.getCompare().equals("MAX")
                                && (o.getBeginBuyYear() > pplVersion.getDemandBeginYear() ||
                                o.getBeginBuyYear().equals(pplVersion.getDemandBeginYear())
                                        && o.getBeginBuyDate().getMonth().getValue() >= pplVersion.getDemandBeginMonth()))
                        .collect(Collectors.toList());
                //在ppl版本月以外的取订单和大客户
                List<AdsMckForecastSummaryDfDO> collectList = data.stream()
                        .filter(o -> !(o.getBeginBuyYear() > pplVersion.getDemandBeginYear() ||
                                o.getBeginBuyYear().equals(pplVersion.getDemandBeginYear())
                                        && o.getBeginBuyDate().getMonth().getValue() >= pplVersion.getDemandBeginMonth()) && (
                                o.getDemandSource().equals("预约单") || o.getDemandSource().equals("大客户"))).collect(
                                Collectors.toList());
                if (ListUtils.isNotEmpty(collectList)) {
                    maxList.addAll(collectList);
                }
                data = maxList;
            }
        }
        return data;
    }

    @HiSpeedCache(expireSecond = 60,continueFetchSecond = 600)
    public List<String> getBigCustomerShortName() {
        List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
        return customerConfig.stream()
                .filter(item-> BooleanUtils.isTrue(item.getIsBigCustomer()))
                .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                .collect(Collectors.toList());
    }

    @Override
    public Object configureMckRestockManualConfig(ConfigureMckRestockManualConfigReq req) {
        //人工调整配置表每天会继承前一天的数据  但如果中间某一天的数据更新 就不会将数据复制到最新的那一天
        //因此在进行人工调整时，从起始日期到最新日期 都手动更新一下
        LocalDate endDate = DateUtils.yesterday();
        LocalDate startDate = LocalDate.parse(req.getDate());
        while(!startDate.isAfter(endDate)) {
            req.setDate(startDate.toString());
            configureMckRestockManualConfigByDate(req);
            startDate = startDate.plusDays(1);
        }
        return Result.success("success");
    }

    public void configureMckRestockManualConfigByDate(ConfigureMckRestockManualConfigReq req) {
        // 查找出对应的配置项，如果查不到则创建一个新的
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", req.getDate());
        condition.and("product_type = ?", "CVM");
        condition.and("instance_type = ?", req.getInstanceType());
        condition.and("zone_name = ?", req.getZoneName());
        condition.and("year = ?", req.getYear());
        condition.and("week = ?", req.getWeek());
        InventoryHealthMckRestockManualConfig configDo = demandDBHelper.getOne(InventoryHealthMckRestockManualConfig.class, condition.getSQL(), condition.getParams());

        if (configDo == null) {
            configDo = new InventoryHealthMckRestockManualConfig();
            configDo.setDate(req.getDate());
            configDo.setProductType("CVM");
            configDo.setInstanceType(req.getInstanceType());
            configDo.setZoneName(req.getZoneName());
            StaticZoneDO staticZoneDO = dictService.getStaticZoneInfoByName(configDo.getZoneName());

            if (staticZoneDO != null){
                configDo.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                configDo.setAreaName(staticZoneDO.getAreaName());
                configDo.setRegionName(staticZoneDO.getRegionName());
            }
            configDo.setYear(req.getYear());
            configDo.setWeek(req.getWeek());
        }

        // supplyToDownstreamDemand supplyCurrentOrder supplyManualConfig demandManualConfig safeInventoryManualConfig
        for (ConfigureMckRestockManualConfigReq.FieldItem field : req.getFields()) {
            BigDecimal value = field.getValue() == null ? BigDecimal.ZERO : field.getValue();
            String reason = field.getReason() == null ? "" : field.getReason();
            switch (field.getField()) {
                case "supplyToDownstreamDemand":
                    configDo.setSupplyToDownstreamDemand(value);
                    break;
                case "supplyCurrentOrder":
                    configDo.setSupplyCurrentOrder(value);
                    break;
                case "supplyManualConfig":
                    configDo.setSupplyManualConfig(value);
                    configDo.setSupplyManualConfigReason(reason);
                    break;
                case "demandManualConfig":
                    configDo.setDemandManualConfig(value);
                    configDo.setDemandManualConfigReason(reason);
                    break;
                case "safeInventoryManualConfig":
                    configDo.setSafeInventoryManualConfig(value);
                    configDo.setSafeInventoryManualConfigReason(reason);
                    break;
                default:
                    throw BizException.makeThrow("不支持更改字段：" + field.getField());
            }
        }

        demandDBHelper.insertOrUpdate(configDo);
    }

    @Override
    @TaskLog(taskName = "snapshotMckRestockManualConfig")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    public void snapshotMckRestockManualConfig(String date) {
        List<InventoryHealthMckRestockManualConfig> current = demandDBHelper.getAll(InventoryHealthMckRestockManualConfig.class,
                "where date = ?", date);

        // 确保幂等性
        if (current.size() > 0){
            demandDBHelper.delete(InventoryHealthMckRestockManualConfig.class, "where date = ?", date);
        }

        Date yesterday = DateUtils.addTime(DateUtils.parse(date), Calendar.DATE, -1);
        List<InventoryHealthMckRestockManualConfig> all = demandDBHelper.getAll(InventoryHealthMckRestockManualConfig.class,
                "where date = ?", DateUtils.formatDate(yesterday));
        // 把前一天的继承下来
        if (all.size() > 0) {
            List<InventoryHealthMckRestockManualConfig> result = Lang.list();
            ListUtils.forEach(all, o -> {
                InventoryHealthMckRestockManualConfig one = o.clone();
                one.setDate(date);
                result.add(one);
            });
            demandDBHelper.insertBatchWithoutReturnId(result);
        }
    }

    /**
     * 汇总页查看人工调整明细 实例类型 + 可用区 + 人工调整数值 + 原因
     */
    @Override
    public QueryManualConfigDetailResp queryManualConfigDetail(QueryManualConfigDetailReq req) {
        if (req.getIsCumulative() == null) {
            req.setIsCumulative(false);
        }
        List<QueryManualConfigDetailResp.Item> data = new ArrayList<>();
        //如果不需要查看累计需求
        if (!req.getIsCumulative()) {
            List<QueryManualConfigDetailResp.Item> items = queryManualConfigDetailSingle(req);
            if (ListUtils.isNotEmpty(items)) {
                data.addAll(items);
            }
        }else {
            Integer index = req.getWeekIndex();
            List<HolidayWeekInfoDTO> weeksByIndex = getWeeksByIndex(DateUtils.parseLocalDate(req.getDate()), index);
            for (HolidayWeekInfoDTO weekInfo : weeksByIndex) {
                req.setWeek(weekInfo.getWeek());
                req.setYear(weekInfo.getYear());
                List<QueryManualConfigDetailResp.Item> items = queryManualConfigDetailSingle(req);
                if (ListUtils.isNotEmpty(items)) {
                    data.addAll(items);
                }
            }
        }
        QueryManualConfigDetailResp resp = new QueryManualConfigDetailResp();
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            data = data.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        resp.setData(data);
        return resp;
    }

    public List<QueryManualConfigDetailResp.Item> queryManualConfigDetailSingle(QueryManualConfigDetailReq req) {
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", req.getDate());
        condition.and("year = ?", req.getYear());
        condition.and("week = ?", req.getWeek());
        condition.and("product_type = ?", "CVM");
        condition.and(req.genBasicCondition());
        List<InventoryHealthMckRestockManualConfig> configDo = demandDBHelper.getAll(InventoryHealthMckRestockManualConfig.class, condition.getSQL(), condition.getParams());
        List<QueryManualConfigDetailResp.Item> data;
        switch (req.getField()) {
            case "supplyManualConfig":
                configDo = configDo.stream().filter(o -> o.getSupplyManualConfig().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                data = configDo.stream().map(o -> {
                    QueryManualConfigDetailResp.Item item = new QueryManualConfigDetailResp.Item();
                    item.setInstanceType(o.getInstanceType());
                    item.setZoneName(o.getZoneName());
                    item.setValue(o.getSupplyManualConfig());
                    item.setReason(o.getSupplyManualConfigReason());
                    item.setYear(o.getYear());
                    item.setWeek(o.getWeek());
                    return item;
                }).collect(Collectors.toList());
                break;
            case "demandManualConfig":
                configDo = configDo.stream().filter(o -> o.getDemandManualConfig().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                data = configDo.stream().map(o -> {
                    QueryManualConfigDetailResp.Item item = new QueryManualConfigDetailResp.Item();
                    item.setInstanceType(o.getInstanceType());
                    item.setZoneName(o.getZoneName());
                    item.setValue(o.getDemandManualConfig());
                    item.setReason(o.getDemandManualConfigReason());
                    item.setYear(o.getYear());
                    item.setWeek(o.getWeek());
                    return item;
                }).collect(Collectors.toList());
                break;
            case "safeInventoryManualConfig":
                configDo = configDo.stream().filter(o -> o.getSafeInventoryManualConfig().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                data = configDo.stream().map(o -> {
                    QueryManualConfigDetailResp.Item item = new QueryManualConfigDetailResp.Item();
                    item.setInstanceType(o.getInstanceType());
                    item.setZoneName(o.getZoneName());
                    item.setValue(o.getSafeInventoryManualConfig());
                    item.setReason(o.getSafeInventoryManualConfigReason());
                    item.setYear(o.getYear());
                    item.setWeek(o.getWeek());
                    return item;
                }).collect(Collectors.toList());
                break;
            default:
                throw  BizException.makeThrow("不支持该字段" + req.getField());
        }
        return data;
    }


    /**
     * 缺口关联运营动作 以 设备类型+可用区维度+周维度展示
     */
    @Override
    public QueryOperationActionDetailResp queryOperationActionDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType) {
        //查看req，是否包含累计数据
        if (req.getIsCumulative() == null) {
            req.setIsCumulative(false);
        }
        Boolean isCumulative = req.getIsCumulative();
        List<QueryOperationActionDetailResp.Item> data = new ArrayList<>();
        if (isCumulative) {
            //如果包含累计，则获取T0-Tindex的数据
            Integer index = req.getWeekIndex();
            List<HolidayWeekInfoDTO> weeksByIndex = getWeeksByIndex(req.getDate(), index);
            weeksByIndex.sort(Comparator.comparing(HolidayWeekInfoDTO::getStartDate));
            String start = weeksByIndex.get(0).getStartDate();
            String end = weeksByIndex.get(weeksByIndex.size() - 1).getEndDate();
            req.setWeekStartDate(start);
            req.setWeekEndDate(end);
            data = getOperationActionDetail(req, allCvmType);
        } else {
            data = getOperationActionDetail(req, allCvmType);
        }
        QueryOperationActionDetailResp resp = new QueryOperationActionDetailResp();
        resp.setData(data);
        return resp;
    }


    public List<QueryOperationActionDetailResp.Item> getOperationActionDetail(QueryMckRestockDetailReq req, List<CvmType> allCvmType) {
        //获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/operation_action_detail.sql");
        Map<String, Object> param = new HashMap<>();
        param.put("statTime",req.getDate().toString());
        //添加start和end
        param.put("start", req.getWeekStartDate());
        param.put("end", req.getWeekEndDate());
        sql = sql.replace("${START_END}", "and quota_use_time between :start and :end");
        //建立一个境内-国内 境外-海外的映射
        Map<String, String> customTitle = new HashMap<>();
        customTitle.put("境内", "国内");
        customTitle.put("境外", "海外");
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            List<String> collect = req.getCustomhouseTitle().stream().map(o -> {
                String str = customTitle.get(o);
                if (str != null) {
                    return str;
                }
                return o;
            }).collect(Collectors.toList());
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "and area_type in (:area)");
            param.put("area", collect);
        } else {
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "");
        }
        List<OperationActionDetailDO> all = ckcubesDBHelper.getRaw(OperationActionDetailDO.class, sql, param);
        List<QueryOperationActionDetailResp.Item> data = new ArrayList<>();
        //设置数据并过滤
        Map<String, StaticZoneDO> zoneInfoMap = dictService.getCampus2ZoneInfoMap();
        List<String> zoneName = req.getZoneName();
        List<String> instanceList = req.getInstanceType();
        if (ListUtils.isEmpty(zoneName)) {
            zoneName = SpringUtil.getBean(OperationViewService2Impl.class).getZoneNamesByZoneCategory(req.getZoneCategory(),req.getDate().toString());
        }
        if (ListUtils.isEmpty(instanceList)) {
            instanceList = new ArrayList<>(SpringUtil.getBean(OperationViewService2Impl.class).getInstanceTypesByInstanceCategory(req.getInstanceTypeCategory(), req.getDate().toString(), req.getCustomhouseTitle()));
        }
        for (OperationActionDetailDO o : all) {
            QueryOperationActionDetailResp.Item item = new QueryOperationActionDetailResp.Item();
            //从campus中获取zone_name area_name regin_name
            StaticZoneDO staticZoneDO = zoneInfoMap.get(o.getCampus());
            DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
            Map<String, String> instanceMap = bean.getCsigDeviceTypeToInstanceTypeMap();
            if (staticZoneDO != null) {
                item.setZoneName(staticZoneDO.getZoneName());
                item.setAreaName(staticZoneDO.getAreaName());
                item.setRegionName(staticZoneDO.getRegionName());
            }

            //设置实例类型
            item.setDeviceType(o.getDeviceType());
            String instanceType = instanceMap.get(o.getDeviceType());
            if (instanceType != null) {
                item.setInstanceType(instanceType);
            }

            //设置时间
            item.setCreateTime(o.getCreateTime());
            item.setExpectDeliveryTime(o.getExpectDeliveryTime());
            item.setUseTime(o.getUseTime());

            //设置其余信息
            item.setQuotaId(o.getQuotaId());
            item.setIndustryDept(o.getIndustryDept());
            item.setCustomName(o.getCustomName());

            //设置核数
            item.setDemandCoreNum(o.getDemandCoreNum());
            item.setNoArrivalCoreNum(o.getNoArrivalCoreNum());

            //进行筛选
            if (isMatchFiler(zoneName, instanceList, req, item)) {
                data.add(item);
            }
        }

        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            data = data.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return data;
    }

    @Override
    public QueryTotalDetailSummaryResp queryTotalDetailSummary(QueryTotalDetailSummaryReq req) {
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        //依次获取所有的明细 然后聚合
        //1.在途到货 queryMckRestockReportSupplyDetail
        QueryMckRestockSupplyDetailResp supplyResp = queryMckRestockReportSupplyDetail(req, allCvmType);
        List<DwsInventoryHealthSupplySummaryDfDO> supplyData = supplyResp.getData();
        //2.需求数据 queryMckRestockReportForecastDetail
        List<QueryMckRestockForecastReq> newReqs = req.totalTransformToForecast(req);
        List<AdsMckForecastSummaryDfDO> forecastData = new ArrayList<>();
        List<Future<QueryMckRestockForecastDetailResp>> futures = new ArrayList<>();
        for (QueryMckRestockForecastReq newReq : newReqs) {
            futures.add(threadPool.submit(() -> queryMckRestockReportForecastDetail(newReq, allCvmType)));
        }
        try {
            for (Future<QueryMckRestockForecastDetailResp> future : futures) {
                QueryMckRestockForecastDetailResp forecasts = future.get();
                List<AdsMckForecastSummaryDfDO> data = forecasts.getData();
                if (ListUtils.isNotEmpty(data)) {
                    forecastData.addAll(data);
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            throw new BizException("线程池执行失败, message: " + e.getMessage());
        }
        //3.运营数据 queryOperationActionDetail
        QueryOperationActionDetailResp operationResp = queryOperationActionDetail(req, allCvmType);
        List<Item> operationData = operationResp.getData();
        //4.T0周之前的运营数据
        List<Item> zeroData = getOperationActionDetailBeforeZero(req, allCvmType);
        log.info("T0周之前的运营数据为： " + zeroData);
        //将四个数据合并
        Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> supplyMap = ListUtils.toMapList(supplyData,
                o -> {
            if (o.getIndustry().equals("中长尾")) {
                o.setIndustry("常规");
            } else if (o.getIndustry().equals("内部业务部") || o.getIndustry().equals("内领业务")) {
                o.setIndustry("内部业务");
            }
            return String.join("@", o.getIndustry(), o.getCustomerName(), o.getProductType());
                }, o -> o);
        Map<String, List<AdsMckForecastSummaryDfDO>> forecastMap = ListUtils.toMapList(forecastData,
                o -> {
            if (o.getIndustryDept().equals("中长尾")) {
                o.setIndustryDept("常规");
            } else if (o.getIndustryDept().equals("内部业务部") || o.getIndustryDept().equals("内领业务")) {
                o.setIndustryDept("内部业务");
            }
            return String.join("@", o.getIndustryDept(), o.getCustomerShortName(), o.getProduct());
                }, o -> o);
        Map<String, List<Item>> operationMap = ListUtils.toMapList(operationData,
                o -> {
                    if (o.getIndustryDept().equals("中长尾")) {
                        o.setIndustryDept("常规");
                    } else if (o.getIndustryDept().equals("内部业务部") || o.getIndustryDept().equals("内领业务")) {
                        o.setIndustryDept("内部业务");
                    }
                    return String.join("@", o.getIndustryDept(), o.getCustomName());
                }, o -> o);
        Map<String, List<Item>> zeroMap = ListUtils.toMapList(zeroData,
                o -> {
                    if (o.getIndustryDept().equals("中长尾")) {
                        o.setIndustryDept("常规");
                    } else if (o.getIndustryDept().equals("内部业务部") || o.getIndustryDept().equals("内领业务")) {
                        o.setIndustryDept("内部业务");
                    }
                    return String.join("@", o.getIndustryDept(), o.getCustomName());
                }, o -> o);
        //获取全量部门和客户简称
        Set<String> deptandName = new HashSet<>();
        if (ListUtils.isNotEmpty(supplyMap)) {
            deptandName.addAll(supplyMap.keySet());
        }
        if (ListUtils.isNotEmpty(forecastMap)) {
            deptandName.addAll(forecastMap.keySet());
        }
        if (ListUtils.isNotEmpty(operationMap)) {
            deptandName.addAll(operationMap.keySet());
        }
        if (ListUtils.isNotEmpty(zeroMap)) {
            deptandName.addAll(zeroMap.keySet());
        }

        List<QueryTotalDetailSummaryResp.Item> data = new ArrayList<>();
        for (String key : deptandName) {
            QueryTotalDetailSummaryResp.Item item = new QueryTotalDetailSummaryResp.Item();
            String[] split = key.split("@");
            item.setIndustryDept(split[0]);
            item.setCustomer(split[1]);
            if (split.length > 2) {
                item.setProduct(split[2]);
            }
            BigDecimal cumulativeSupply = NumberUtils.sum(supplyMap.get(key), o -> o.getCores());
            BigDecimal cumulativeForecast = NumberUtils.sum(forecastMap.get(key), o -> o.getTotalCore());
            BigDecimal cumulativeNoArrival = NumberUtils.sum(operationMap.get(key), o -> o.getNoArrivalCoreNum());
            BigDecimal cumulativePurchase = NumberUtils.sum(operationMap.get(key), o -> o.getDemandCoreNum());
            BigDecimal supplySubtractForecast = cumulativeSupply.subtract(cumulativeForecast);
            BigDecimal beforeZeroNoArrival = NumberUtils.sum(zeroMap.get(key), o -> o.getNoArrivalCoreNum());
            BigDecimal purchaseAndNoArrival = cumulativePurchase.add(beforeZeroNoArrival);
            item.setCumulativeSupply(cumulativeSupply);
            item.setCumulativeForecast(cumulativeForecast);
            item.setCumulativeNoArrival(cumulativeNoArrival);
            item.setCumulativeArrival(cumulativePurchase.subtract(cumulativeNoArrival));
            item.setCumulativePurchase(cumulativePurchase);
            item.setSupplySubtractForecast(supplySubtractForecast);
            item.setBeforeZeroNoArrival(beforeZeroNoArrival);
            item.setPurchaseAndNoArrival(purchaseAndNoArrival);
            data.add(item);
        }
        QueryTotalDetailSummaryResp resp = new QueryTotalDetailSummaryResp();
        resp.setData(data);
        return resp;
    }

    private List<Item> getOperationActionDetailBeforeZero(QueryTotalDetailSummaryReq req, List<CvmType> allCvmType) {
        //获取数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/operation_action_no_arrival.sql");
        Map<String, Object> param = new HashMap<>();
        param.put("statTime",req.getDate().toString());
        Integer index = req.getWeekIndex();
        List<HolidayWeekInfoDTO> weeksByIndex = getWeeksByIndex(req.getDate(), index);
        weeksByIndex.sort(Comparator.comparing(HolidayWeekInfoDTO::getStartDate));
        String start = weeksByIndex.get(0).getStartDate();
        param.put("start", start);
        sql = sql.replace("${START_END}", "and quota_use_time < :start");
        //建立一个境内-国内 境外-海外的映射
        Map<String, String> customTitle = new HashMap<>();
        customTitle.put("境内", "国内");
        customTitle.put("境外", "海外");
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            List<String> collect = req.getCustomhouseTitle().stream().map(o -> {
                String str = customTitle.get(o);
                if (str != null) {
                    return str;
                }
                return o;
            }).collect(Collectors.toList());
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "and area_type in (:area)");
            param.put("area", collect);
        } else {
            sql = sql.replace("${CUSTOMHOUSE_TITLE}", "");
        }
        List<OperationActionNoArrivalDetailDO> all = ckcubesDBHelper.getRaw(OperationActionNoArrivalDetailDO.class, sql, param);
        List<QueryOperationActionDetailResp.Item> data = new ArrayList<>();
        //设置数据并过滤
        Map<String, StaticZoneDO> zoneInfoMap = dictService.getCampus2ZoneInfoMap();
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, String> instanceMap = bean.getCsigDeviceTypeToInstanceTypeMap();
        List<String> zoneName = req.getZoneName();
        List<String> instanceList = req.getInstanceType();
        if (ListUtils.isEmpty(zoneName)) {
            zoneName = SpringUtil.getBean(OperationViewService2Impl.class).getZoneNamesByZoneCategory(req.getZoneCategory(),req.getDate().toString());
        }
        if (ListUtils.isEmpty(instanceList)) {
            instanceList = new ArrayList<>(SpringUtil.getBean(OperationViewService2Impl.class).getInstanceTypesByInstanceCategory(req.getInstanceTypeCategory(), req.getDate().toString(), req.getCustomhouseTitle()));
        }
        for (OperationActionNoArrivalDetailDO o : all) {
            QueryOperationActionDetailResp.Item item = new QueryOperationActionDetailResp.Item();
            //从campus中获取zone_name area_name regin_name
            StaticZoneDO staticZoneDO = zoneInfoMap.get(o.getCampus());
            if (staticZoneDO != null) {
                item.setZoneName(staticZoneDO.getZoneName());
                item.setAreaName(staticZoneDO.getAreaName());
                item.setRegionName(staticZoneDO.getRegionName());
            }

            //设置实例类型
            item.setDeviceType(o.getDeviceType());
            String instanceType = instanceMap.get(o.getDeviceType());
            if (instanceType != null) {
                item.setInstanceType(instanceType);
            }

            //设置时间
            item.setCreateTime(o.getCreateTime());
            item.setExpectDeliveryTime(o.getExpectDeliveryTime());
            item.setUseTime(o.getUseTime());

            //设置其余信息
            item.setQuotaId(o.getQuotaId());
            item.setIndustryDept(o.getIndustryDept());
            item.setCustomName(o.getCustomName());

            //设置核数
            item.setNoArrivalCoreNum(o.getNoArrivalCoreNum());
            //进行筛选
            if (isMatchFiler(zoneName, instanceList, req, item)) {
                data.add(item);
            }
        }

        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            data = data.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return data;
    }

    private boolean isMatchFiler(List<String> zoneName, List<String> instanceList, QueryMckRestockDetailReq req, QueryOperationActionDetailResp.Item item) {
        //查看区域和地域是否满足需求
        if (ListUtils.isNotEmpty(req.getRegionName()))  {
            if (!req.getRegionName().contains(item.getRegionName())) {
                return false;
            }
        }
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            if (!req.getAreaName().contains(item.getAreaName())) {
                return false;
            }
        }

        //查看实例类型与可用区是否满足
        if (!zoneName.contains(item.getZoneName())) {
            return false;
        }
        if (!instanceList.contains(item.getInstanceType())) {
            return false;
        }
        return true;
    }

    private void insertOrUpdateBeginInventory(
            Map<String, List<CommonRestockDataItemDO>> actualInvDOSWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap
    ) {
        // 期初库存，对于 W0，期初库存 = 当周周一的实际库存
        if (holidayWeekInfoDTO.getWeekNFromNow() == 0) {
            // 生成第一周的期初库存数据、期末库存数据等等
            String key = getKey("CVM", instanceType, zoneName, holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
            List<CommonRestockDataItemDO> actualInvDOList = actualInvDOSWeekMap.get(key);

            if (ListUtils.isNotEmpty(actualInvDOList)) {
                CommonRestockDataItemDO actualInvDO = actualInvDOList.get(0);
                QueryMckRestockResp.Item item = QueryMckRestockResp.Item.from(actualInvDO, holidayWeekInfoDTO);
                // 第一周的期初库存
                item.setBeginInventory(NumberUtils.sum(actualInvDOList, o -> o.getCores()));
                // 更新结果
                weekRespMap.put(key, item);
            } else {
                // 期初不存在时为 null，前端处理展示
                QueryMckRestockResp.Item item = QueryMckRestockResp.Item.from("CVM", instanceType, zoneName, holidayWeekInfoDTO);
                item.setBeginInventory(null);
                weekRespMap.put(key, item);
            }
        } else {
            QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);

            // 对于 W1-W13，期初库存 = 上一周的期末库存
            Date startDate = DateUtils.parse(holidayWeekInfoDTO.getStartDate());
            Date before = DateUtils.addTime(startDate, Calendar.DATE, -1);
            ResPlanHolidayWeekDO beforeWeek = dictService.getHolidayWeekInfoByDate(DateUtils.formatDate(before));
            int lastYear = beforeWeek.getYear();
            int lastWeek = beforeWeek.getWeek();
            String lastWeekKey = getKey("CVM", instanceType, zoneName, lastYear, lastWeek);
            QueryMckRestockResp.Item lastWeeKItem = weekRespMap.get(lastWeekKey);

            if (lastWeeKItem != null) {
                item.setBeginInventory(lastWeeKItem.getEndInventory());
            }
        }
    }

    private void insertOrUpdateSupply(
            Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> supplySummaryDfDOSWeekMap,
            Map<String, List<InventoryHealthMckRestockManualConfig>> manualConfigDOSWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        String key = this.getKey(item.getProductType(), item.getInstanceType(), item.getZoneName(), holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        // 直接取W0-W13
        List<DwsInventoryHealthSupplySummaryDfDO> supplySummaryDOList = supplySummaryDfDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(supplySummaryDOList)) {
            item.setSupplyOnTheWay(NumberUtils.sum(supplySummaryDOList, o -> o.getCores()));
        }

        // 更新三个人工调整信息
        List<InventoryHealthMckRestockManualConfig> manualConfigList = manualConfigDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(manualConfigList)) {
            item.setSupplyCurrentOrder(NumberUtils.sum(manualConfigList, o -> o.getSupplyCurrentOrder()));
            item.setSupplyToDownstreamDemand(NumberUtils.sum(manualConfigList, o -> o.getSupplyToDownstreamDemand()));
            item.setSupplyManualConfig(NumberUtils.sum(manualConfigList, o -> o.getSupplyManualConfig()));
        }

        // 供应汇总
        BigDecimal supplyOnTheWay = item.getSupplyOnTheWay() == null ? BigDecimal.ZERO : item.getSupplyOnTheWay();
        BigDecimal currentOrder = item.getSupplyCurrentOrder() == null ? BigDecimal.ZERO : item.getSupplyCurrentOrder();
        BigDecimal toDownStreamDemand = item.getSupplyToDownstreamDemand() == null ? BigDecimal.ZERO : item.getSupplyToDownstreamDemand();
        BigDecimal manualConfig = item.getSupplyManualConfig() == null ? BigDecimal.ZERO : item.getSupplyManualConfig();
        item.setSupplyTotal(supplyOnTheWay.add(currentOrder).add(toDownStreamDemand).add(manualConfig));
    }

    private void insertOrUpdateDemand(
            Map<String, List<CommonRestockForecastDataItemDO>> mckForecastSummaryDfDOSWeekMap,
            Map<String, List<InventoryHealthMckRestockManualConfig>> manualConfigDOSWeekMap,
            Map<String, List<CommonRestockDataItemDO>> historyPeakWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            String demandSelect,
            Map<String, QueryMckRestockResp.Item> weekRespMap,
            PplVersionDO pplVersion
    ) {
        // 需求信息里面有周信息，直接取 W0-W13
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        String key = this.getKey(item.getProductType(), item.getInstanceType(), item.getZoneName(), holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        List<CommonRestockForecastDataItemDO> mckForecastSummaryDOList = mckForecastSummaryDfDOSWeekMap.get(key);

        if (ListUtils.isNotEmpty(mckForecastSummaryDOList)) {
            //先把大客户的非退回且在ppl版本之外的给剔除
            mckForecastSummaryDOList = mckForecastSummaryDOList.stream()
                    .filter(v -> !v.getDemandSource().equals("大客户") || v.getDemandType().equals("RETURN") ||
                            (v.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                                    (v.getDate().getYear() == pplVersion.getDemandBeginYear()
                                            && v.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth())))
                    .collect(Collectors.toList());
            // 1.头部客户（包年包月）：
            // 先筛选出 头部客户 + "非弹性" 条件1
            List<CommonRestockForecastDataItemDO> bigCustomerMonthlyList = mckForecastSummaryDOList.stream().
                    filter(o -> o.getIsHead() == 1 && !"ELASTIC".equals(o.getDemandType())).collect(Collectors.toList());
            //头部客户（包年包月）：在条件1的基础上添加需求来源为demandSelect
            if (demandSelect.equals("MAX")) {
                //MAX逻辑， 分两部分
                // 在ppl版本月以内的 取MAX
                List<CommonRestockForecastDataItemDO> maxList = bigCustomerMonthlyList.stream()
                        .filter(o -> o.getCompare().equals("MAX")
                                && (o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()))
                        .collect(Collectors.toList());
                //在ppl版本月以外的取订单和大客户
                List<CommonRestockForecastDataItemDO> collectList = bigCustomerMonthlyList.stream()
                        .filter(o -> !(o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()) && (
                                o.getDemandSource().equals("预约单") || o.getDemandSource().equals("大客户"))).collect(
                                Collectors.toList());
                if (ListUtils.isNotEmpty(collectList)) {
                    maxList.addAll(collectList);
                }
                item.setDemandBigCustomerMonthly(NumberUtils.sum(maxList, o -> o.getCores()));
            }else {
                item.setDemandBigCustomerMonthly(NumberUtils.sum(bigCustomerMonthlyList.stream().filter(o -> o.getDemandSource().equals(demandSelect)).collect(
                        Collectors.toList()), o -> o.getCores()));
            }
            //头部客户（包年包月-预测）：在条件1的基础上添加需求来源为大客户和中长尾
            item.setDemandBigCustomerMonthlyForecast(NumberUtils.sum(bigCustomerMonthlyList.stream().filter(o -> o.getDemandSource().equals("大客户") || o.getDemandSource().equals("中长尾")).collect(
                    Collectors.toList()), o -> o.getCores()));
            //头部客户（包年包月-订单）: 在条件1的基础上添加需求来源为预约单
            item.setDemandBigCustomerMonthlyOrder(NumberUtils.sum(bigCustomerMonthlyList.stream().filter(o -> o.getDemandSource().equals("预约单")).collect(
                    Collectors.toList()), o -> o.getCores()));
            // 头部客户（弹性）：筛选 "头部客户" + "弹性" + MAX
            List<CommonRestockForecastDataItemDO> bigCustomerYearlyElasticList = mckForecastSummaryDOList.stream().
                    filter(o -> "ELASTIC".equals(o.getDemandType()) && o.getIsHead() == 1).collect(Collectors.toList());
            //MAX分两个部分
            List<CommonRestockForecastDataItemDO> maxList = bigCustomerYearlyElasticList.stream()
                    .filter(o -> o.getCompare().equals("MAX")
                            && (o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                            o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                    && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()))
                    .collect(Collectors.toList());
            //在ppl版本月以外的取订单和大客户
            List<CommonRestockForecastDataItemDO> collectList = bigCustomerYearlyElasticList.stream()
                    .filter(o -> !(o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                            o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                    && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()) && (
                            o.getDemandSource().equals("预约单") || o.getDemandSource().equals("大客户"))).collect(
                            Collectors.toList());
            if (ListUtils.isNotEmpty(collectList)) {
                maxList.addAll(collectList);
            }
            item.setDemandBigCustomerElastic(NumberUtils.sum(maxList, o -> o.getCores()));
        }

        // 过去 12 周平均周峰净增：取当周的上一周的 12 周峰净增均值，所有未来周该值都相同
        List<CommonRestockDataItemDO> historyPeakDOList = historyPeakWeekMap.get(StringTools.join("@", item.getProductType(), item.getInstanceType(), item.getZoneName()));
        if (ListUtils.isNotEmpty(historyPeakDOList)) {
            item.setDemandWeekPeakAvg12(NumberUtils.sum(historyPeakDOList, o -> o.getCores()));
        }

        // 中长尾：两部分
        //1.模型预测 均摊
        // - 取 客户类型非头部 客户简称为模型预测内外客户
        // - 取计划时间所在月的 15 号拿到当月的中长尾预测
        // - 均摊到周：月预测量 / 当月天数，然后再除以 7，如果跨月则按照跨月的天数均摊，例如前一个月 3 天，后一个月四天，那么就均摊到前一个月 * 3 + 均摊后一个月 * 4
        String middleMonth = LocalDate.of(holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getMonth(), 15).toString();
        ResPlanHolidayWeekDO middleMonthWeek = dictService.getHolidayWeekInfoByDate(middleMonth);
        String mediumLongTailKey = this.getKey(item.getProductType(), item.getInstanceType(), item.getZoneName(), middleMonthWeek.getYear(), middleMonthWeek.getWeek());
        List<CommonRestockForecastDataItemDO> mediumLongTailList = mckForecastSummaryDfDOSWeekMap.get(mediumLongTailKey);
        if (ListUtils.isNotEmpty(mediumLongTailList)) {
            mediumLongTailList = mediumLongTailList.stream().filter(o -> (o.getCustomerShortName().equals("模型预测内部客户") || o.getCustomerShortName().equals("模型预测外部客户")) && o.getIsHead() == 0).collect(
                    Collectors.toList());
            item.setDemandMediumLongTailModel(NumberUtils.sum(mediumLongTailList, o -> {
                // 均摊到周，除以当月天数，再 * 7
                BigDecimal monthDays = BigDecimal.valueOf(LocalDate.parse(middleMonth).lengthOfMonth());
                return o.getCores().multiply(BigDecimal.valueOf(7)).divide(monthDays, RoundingMode.HALF_UP);
            }));
        }
        //2.行业报备 取当周的数据
        // 客户分类非头部 客户简称非模型预测内外客户
        if (ListUtils.isNotEmpty(mckForecastSummaryDOList)) {
            mckForecastSummaryDOList = mckForecastSummaryDOList.stream().filter(o -> !(o.getCustomerShortName().equals("模型预测内部客户") || o.getCustomerShortName().equals("模型预测外部客户")) && o.getIsHead() == 0).collect(
                    Collectors.toList());
            //2.1 行业报备 需求来源取demandSelect
            if (demandSelect.equals("MAX")) {
                //MAX分两个部分
                List<CommonRestockForecastDataItemDO> maxList = mckForecastSummaryDOList.stream()
                        .filter(o -> o.getCompare().equals("MAX")
                                && (o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()))
                        .collect(Collectors.toList());
                //在ppl版本月以外的取订单和大客户
                List<CommonRestockForecastDataItemDO> collectList = mckForecastSummaryDOList.stream()
                        .filter(o -> !(o.getDate().getYear() > pplVersion.getDemandBeginYear() ||
                                o.getDate().getYear() == (pplVersion.getDemandBeginYear())
                                        && o.getDate().getMonthValue() >= pplVersion.getDemandBeginMonth()) && (
                                o.getDemandSource().equals("预约单") || o.getDemandSource().equals("大客户"))).collect(
                                Collectors.toList());
                if (ListUtils.isNotEmpty(collectList)) {
                    maxList.addAll(collectList);
                }
                item.setDemandMediumLongTailIndustry(NumberUtils.sum(maxList, CommonRestockDataItemDO::getCores));
            }else {
                item.setDemandMediumLongTailIndustry(NumberUtils.sum(mckForecastSummaryDOList.stream().filter(o -> o.getDemandSource().equals(demandSelect)).collect(
                        Collectors.toList()), CommonRestockDataItemDO::getCores));
            }
            //2.2 行业报备（预测）需求来源取大客户和中长尾
            item.setDemandMediumLongTailIndustryForecast(NumberUtils.sum(mckForecastSummaryDOList.stream().filter(o -> o.getDemandSource().equals("大客户") || o.getDemandSource().equals("中长尾")).collect(
                    Collectors.toList()), CommonRestockDataItemDO::getCores));
            //2.3 行业报备（订单）需求来源取预约单
            item.setDemandMediumLongTailIndustryOrder(NumberUtils.sum(mckForecastSummaryDOList.stream().filter(o -> o.getDemandSource().equals("预约单")).collect(
                    Collectors.toList()), CommonRestockDataItemDO::getCores));
        }

        // 更新人工调整信息
        List<InventoryHealthMckRestockManualConfig> manualConfigList = manualConfigDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(manualConfigList)) {
            item.setDemandManualConfig(NumberUtils.sum(manualConfigList, o -> o.getDemandManualConfig()));
        }

        // 汇总 = 大客户 + 中长尾（行业报备）+中长尾（模型预测）+ 人工调整
        BigDecimal bigCustomerMonthly = item.getDemandBigCustomerMonthly() == null ? BigDecimal.ZERO : item.getDemandBigCustomerMonthly();
        BigDecimal bigCustomerElastic = item.getDemandBigCustomerElastic() == null ? BigDecimal.ZERO : item.getDemandBigCustomerElastic();
        BigDecimal mediumLongTailIndustry = item.getDemandMediumLongTailIndustry() == null ? BigDecimal.ZERO : item.getDemandMediumLongTailIndustry();
        BigDecimal mediumLongTailModel = item.getDemandMediumLongTailModel() == null ? BigDecimal.ZERO : item.getDemandMediumLongTailModel();
        BigDecimal manualConfig = item.getDemandManualConfig() == null ? BigDecimal.ZERO : item.getDemandManualConfig();
        item.setDemandTotal(bigCustomerMonthly.add(bigCustomerElastic).add(mediumLongTailIndustry).add(mediumLongTailModel).add(manualConfig));
    }

    private void insertOrUpdateEndInventory(
            Map<String, List<DwsInventoryHealthMckForecastMonthlySafeDfDO>> safeMonthlyDfDOWeekMap,
            Map<String, List<CommonRestockDataItemDO>> actualInvDOSWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap
    ) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        // 期末库存 = 期初库存 + 供应 - 需求
        BigDecimal beginInv = item.getBeginInventory() == null ? BigDecimal.ZERO : item.getBeginInventory();
        BigDecimal supplyTotal = item.getSupplyTotal() == null ? BigDecimal.ZERO : item.getSupplyTotal();
        BigDecimal demandTotal = item.getDemandTotal() == null ? BigDecimal.ZERO : item.getDemandTotal();
        item.setEndInventory(beginInv.add(supplyTotal).subtract(demandTotal));

        // 周转库存 = (需求汇总 + 过去12周周峰平均 * 12)  / 13
        BigDecimal demandWeekPeakAvg12 = item.getDemandWeekPeakAvg12() == null ? BigDecimal.ZERO : item.getDemandWeekPeakAvg12();
        item.setTurnoverInv(demandWeekPeakAvg12.multiply(BigDecimal.valueOf(12)).add(demandTotal).divide(BigDecimal.valueOf(13), RoundingMode.HALF_UP));

        // 最新库存：从计划日期开始，能够看到的所有实际库存
        String key = this.getKey(item.getProductType(), item.getInstanceType(), item.getZoneName(), holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        List<CommonRestockDataItemDO> actualInvDOList = actualInvDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(actualInvDOList)) {
            item.setCurrentWeekLatestInventory(NumberUtils.sum(actualInvDOList, o -> o.getCores()));
        }
        // 安全库存周转周数：取预测安全库存表
        List<DwsInventoryHealthMckForecastMonthlySafeDfDO> safeDOList = safeMonthlyDfDOWeekMap.get(key);
        if (ListUtils.isNotEmpty(safeDOList)) {
            item.setTurnoverWeekNum(NumberUtils.avg(safeDOList.stream().map(o -> o.getTurnoverWeekNum()).collect(Collectors.toList()), 6));
        }
    }

    private void insertOrUpdateSafeInventory(
            Map<String, List<DwsBufferSafeInventoryDfDO>> bufferSafeInventoryDOSWeekMap,
            Map<String, List<InventoryHealthMckRestockManualConfig>> manualConfigDOSWeekMap,
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap
    ) {
        QueryMckRestockResp.Item item = this.getOrInsertItem(holidayWeekInfoDTO, instanceType, zoneName, weekRespMap);
        // 预测包月安全库存 = 预测周转库存 * 周转周数
        BigDecimal turnoverInv = item.getTurnoverInv() == null ? BigDecimal.ZERO : item.getTurnoverInv();
        BigDecimal turnoverWeekNum = item.getTurnoverWeekNum() == null ? BigDecimal.ZERO : item.getTurnoverWeekNum();
        item.setMonthlySafeInventory(turnoverWeekNum.multiply(turnoverInv));
        // 弹性备货配额，每周都一样，等于 W0
        String key = StringTools.join("@", item.getProductType(), item.getInstanceType(), item.getZoneName());
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDOList = bufferSafeInventoryDOSWeekMap.get(key);
        if (ListUtils.isNotEmpty(bufferSafeInventoryDOList)) {
            item.setBufferSafeInventory(NumberUtils.sum(bufferSafeInventoryDOList, o -> o.getMckBufferSafetyInv()));
        }
        // 人工调整
        String manualConfigKey = getKey(item.getProductType(), item.getInstanceType(), item.getZoneName(), holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        List<InventoryHealthMckRestockManualConfig> manualConfigList = manualConfigDOSWeekMap.get(manualConfigKey);
        if (ListUtils.isNotEmpty(manualConfigList)) {
            item.setSafeInventoryManualConfig(NumberUtils.sum(manualConfigList, o -> o.getSafeInventoryManualConfig()));
        }

        // 安全库存汇总 = 安全库存 + 弹性 + 人工调整
        BigDecimal monthlySafeInventory = item.getMonthlySafeInventory() == null ? BigDecimal.ZERO : item.getMonthlySafeInventory();
        BigDecimal bufferSafeInventory = item.getBufferSafeInventory() == null ? BigDecimal.ZERO : item.getBufferSafeInventory();
        BigDecimal safeInventoryManualConfig = item.getSafeInventoryManualConfig() == null ? BigDecimal.ZERO : item.getSafeInventoryManualConfig();
        item.setSafeInventoryTotal(monthlySafeInventory.add(bufferSafeInventory).add(safeInventoryManualConfig));

        // 整体缺口 = 期末库存 - 安全库存总和
        BigDecimal endInventory = item.getEndInventory() == null ? BigDecimal.ZERO : item.getEndInventory();
        BigDecimal safeInventoryTotal = item.getSafeInventoryTotal() == null ? BigDecimal.ZERO : item.getSafeInventoryTotal();
        item.setTotalGap(endInventory.subtract(safeInventoryTotal));
    }

    private QueryMckRestockResp.Item getOrInsertItem(
            HolidayWeekInfoDTO holidayWeekInfoDTO,
            String instanceType,
            String zoneName,
            Map<String, QueryMckRestockResp.Item> weekRespMap
    ) {
        // 当前仅支持 CVM
        String key = getKey("CVM", instanceType, zoneName, holidayWeekInfoDTO.getYear(), holidayWeekInfoDTO.getWeek());
        QueryMckRestockResp.Item item = weekRespMap.get(key);

        if (item == null) {
            item = QueryMckRestockResp.Item.from("CVM", instanceType, zoneName, holidayWeekInfoDTO);
            weekRespMap.put(key, item);
        }

        return item;
    }

    private String getKey(String productType, String instanceType, String zoneName, int year, int week) {
        return StringTools.join("@", productType, instanceType, zoneName, year, week);
    }

    private List<HolidayWeekInfoDTO> getWeeks(QueryMckRestockReq req) {
        String statTime = DateUtils.formatDate(req.getDate());
        List<HolidayWeekInfoDTO> holidayWeekInfo = ListUtils.newList();
        // 当周也算
        ResPlanHolidayWeekDO curWeek = dictService.getHolidayWeekInfoByDate(statTime);
        HolidayWeekInfoDTO curWeekDTO = new HolidayWeekInfoDTO();
        curWeekDTO.setYear(curWeek.getYear());
        curWeekDTO.setMonth(curWeek.getMonth());
        curWeekDTO.setWeek(curWeek.getWeek());
        curWeekDTO.setStartDate(curWeek.getStart());
        curWeekDTO.setEndDate(curWeek.getEnd());
        curWeekDTO.setWeekNFromNow(0);
        holidayWeekInfo.add(curWeekDTO);
        List<HolidayWeekInfoDTO> holidayWeekInfoDTOS = inventoryHealthDictService.getHolidayWeekInfoBase(req.getDate(), 13);
        holidayWeekInfo.addAll(holidayWeekInfoDTOS);

        return holidayWeekInfo;
    }

    private WhereSQL getInventoryCondition(QueryMckRestockReq req) {
        ResPlanHolidayWeekDO startHolidayWeekDO = dictService.getHolidayWeekInfoByDate(req.getDate().toString());
        ResPlanHolidayWeekDO yesterdayHolidayWeekDO = dictService.getHolidayWeekInfoByDate(DateUtils.yesterday().toString());

        List<String> mondays = ListUtils.newList();
        LocalDate curMonday = LocalDate.parse(startHolidayWeekDO.getStart());
        LocalDate yesterdayMonday = LocalDate.parse(yesterdayHolidayWeekDO.getStart());
        while (curMonday.isBefore(yesterdayMonday) || curMonday.equals(yesterdayMonday)) {
            mondays.add(curMonday.toString());
            curMonday = curMonday.plusWeeks(1L);
        }

        // 取所有能查到的周一的库存
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time in (?)", mondays);
        condition.and("product_type = ?", "CVM");
        condition.and("supply_type = ?", "库存");
        condition.and(req.genBasicCondition());
        condition.and(req.genActualInvCondition());

        return condition;
    }

    /**
     * 按照机型 + 可用区维度 + 周维度，取从计划日期当周开始，每周周一的实际库存
     */
    private Map<String, List<CommonRestockDataItemDO>> getActualInvByWeek(QueryMckRestockReq req, List<CvmType> allCvmType) {
        // 1. 取实际库存，先取计划开始日期所在周，然后取到当前周为止的所有周一的实际库存
        WhereSQL condition = getInventoryCondition(req);

        List<String> groupByFields = this.getGroupByDimension(req);
        String groupBy = "stat_time, product_type" + groupByFields.get(0);
        String fields = "stat_time, product_type" + groupByFields.get(1);

        condition.addGroupBy(groupBy);
        fields += ", sum(cores) cores";

        String sql = "select " + fields + " from cloud_demand.dws_inventory_health_supply_summary_df" + condition.getSQL();

        List<CommonRestockDataItemDO.StatTimeItemDO> all = ckcldDBHelper.getRaw(CommonRestockDataItemDO.StatTimeItemDO.class, sql, condition.getParams());
        //根据机型族过滤数据
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        // 根据 statTime 按照 机型 + 可用区维度 + 周维度 分组
        return ListUtils.toMapList(
                all.stream().map(o -> CommonRestockDataItemDO.from(o)).collect(Collectors.toList()),
                o -> this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getYear(), o.getWeek()),
                o -> o
        );
    }

    /**
     * 按照机型 + 可用区维度，取计划日期的上一周周峰数据
     */
    private Map<String, List<CommonRestockDataItemDO>> getHistoryPeakByWeek(QueryMckRestockReq req, List<CvmType> allCvmType) {
        // 取上周周峰
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", req.getDate().toString());
        condition.and("week_index = ?", -1);
        condition.and("customer_custom_group = ?", "ALL");
        condition.and("exclude_uin_list = ?", "(空值)");
        condition.and("product_type = ?", "CVM");
        condition.and(req.genBasicCondition());

        List<String> groupByFields = this.getGroupByDimension(req);
        String groupBy = "product_type" + groupByFields.get(0);
        // 周峰不需要周维度，所以置0
        String fields = "product_type, 0 year, 0 week" + groupByFields.get(1);
        fields += ", sum(week_peak_logic_num_avg_12) cores";

        condition.addGroupBy(groupBy);
        String sql = "select " + fields + " from cloud_demand.dws_inventory_health_weekly_scale_df" + condition.getSQL();

        List<CommonRestockDataItemDO> all = ckcldDBHelper.getRaw(CommonRestockDataItemDO.class, sql, condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }

        return ListUtils.toMapList(all, o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()), o -> o);
    }

    private WhereSQL getSupplyCondition(QueryMckRestockReq req) {
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", req.getDate().toString());
        condition.and("product_type = ?", "CVM");
        condition.and("supply_type = ?", "采购");
        condition.and("produce_status <> ? and produce_status <> ?", "完成", "需求单已作废");
        condition.and(req.genBasicCondition());
        return condition;
    }

    /**
     * 按照机型 + 可用区维度 + 周维度，取计划日期的供应汇总表
     */
    private Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> getSupplySummary(QueryMckRestockReq req, List<CvmType> allCvmType) {
        return getSupplySummaryDetail(req, allCvmType);
    }

    /**
     *按照机型 + 可用区维度 + 周维度 + 取值条件聚合供应
     */
    private Map<String, List<DwsInventoryHealthSupplySummaryDfDO>> getSupplySummaryDetail(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = this.getSupplyCondition(req);
        //1.查出明细数据
        List<DwsInventoryHealthSupplySummaryDfDO> all = ckcldDBHelper.getAll(DwsInventoryHealthSupplySummaryDfDO.class, condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        List<HolidayWeekInfoDTO> weeks = getWeeks(req);
        weeks.sort(Comparator.comparingInt(HolidayWeekInfoDTO::getWeekNFromNow));
        List<ResPlanHolidayWeekDO> allWeek = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        //明细数据中存在以前未处理的year month week为0的数据，需要单独处理一下
        all.forEach(o ->{
            if (o.getPromiseDeliveryYear() == 0 && o.getPromiseDeliveryWeek() == 0) {
                //按照现有的supply汇总表取数逻辑再次获取一下
                if (o.getPromiseDeliveryTime() != null) {
                    try {
                        ResPlanHolidayWeekDO curWeek = SpringUtil.getBean(DictServiceImpl.class).getHolidayWeekInfoByDate(o.getPromiseDeliveryTime());
                        o.setPromiseDeliveryYear(curWeek.getYear());
                        o.setPromiseDeliveryMonth(curWeek.getMonth());
                        o.setPromiseDeliveryWeek(curWeek.getWeek());
                        //如果报错 说明无货期 统一放到13周
                    }catch (Exception e) {
                        o.setPromiseDeliveryYear(weeks.get(weeks.size() - 1).getYear());
                        o.setPromiseDeliveryMonth(weeks.get(weeks.size() - 1).getMonth());
                        o.setPromiseDeliveryWeek(weeks.get(weeks.size() - 1).getWeek());
                    }
                }
            }
        });
        //2.根据是否勾选全量数据进行处理
        List<DwsInventoryHealthSupplySummaryDfDO> result = all;
        //不勾选，过滤掉(1)有预期到货时间且落在W13后且期望日期落在W13后的
        // (2)无预期时间且期望到货时间落在W13后的
        if (!req.getIsContainFullSupply()) {
            result = all.stream().filter(o ->{
                HolidayWeekInfoDTO W13 = weeks.get(weeks.size() - 1);
                boolean illegal = isDateIllegal(o.getPromiseDeliveryTime(), allWeek);
                //预期到货时间落在W13后且期望到货时间落在W13内的，将预计到货时间设置为期望到货时间
                if (!illegal && o.getPromiseDeliveryTime().compareTo(W13.getEndDate()) > 0 && o.getExpectDeliveryDate().compareTo(W13.getEndDate()) < 0) {
                    o.setPromiseDeliveryTime(o.getExpectDeliveryDate());
                    o.setPromiseDeliveryWeek(o.getExpectDeliveryWeek());
                    o.setPromiseDeliveryMonth(o.getPromiseDeliveryMonth());
                    o.setPromiseDeliveryYear(o.getExpectDeliveryYear());
                }
                return !((!illegal && o.getPromiseDeliveryTime().compareTo(W13.getEndDate()) > 0 && o.getExpectDeliveryDate().compareTo(W13.getEndDate()) > 0)||
                        (illegal && o.getExpectDeliveryDate().compareTo(W13.getEndDate()) > 0));
            }).collect(Collectors.toList());
        }
        result.forEach(o->{
            String delivery = o.getPromiseDeliveryTime();
            //先将W0以前的库存全都规划到W0内
            if (!isDateIllegal(delivery, allWeek) && delivery.compareTo(weeks.get(0).getStartDate()) < 0) {
                o.setPromiseDeliveryYear(weeks.get(0).getYear());
                o.setPromiseDeliveryMonth(weeks.get(0).getMonth());
                o.setPromiseDeliveryWeek(weeks.get(0).getWeek());
            }
            //再将W13之后的都放进W13
            if (!isDateIllegal(delivery, allWeek) && delivery.compareTo(weeks.get(weeks.size() - 1).getEndDate()) > 0) {
                o.setPromiseDeliveryYear(weeks.get(weeks.size() - 1).getYear());
                o.setPromiseDeliveryMonth(weeks.get(weeks.size() - 1).getMonth());
                o.setPromiseDeliveryWeek(weeks.get(weeks.size() - 1).getWeek());
            }

        });
        return ListUtils.toMapList(result,
                o ->this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getPromiseDeliveryYear(), o.getPromiseDeliveryWeek()),
                o ->o);
    }

    private boolean isDateIllegal(String dateStr, List<ResPlanHolidayWeekDO> all) {
        List<ResPlanHolidayWeekDO> filter =
                ListUtils.filter(all,
                        o -> o != null && o.getStart().compareTo(dateStr) <= 0 && o.getEnd().compareTo(dateStr) >= 0);
        return filter.size() != 1;
    }

    /**
     * 按照机型 + 可用区维度 + 周维度，取计划日期的人工调整
     */
    private Map<String, List<InventoryHealthMckRestockManualConfig>> getInventoryHealthMckRestockManualConfig(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = new WhereSQL();
        condition.and("date = ?", req.getDate().toString());
        condition.and(req.genBasicCondition());
        List<InventoryHealthMckRestockManualConfig> all = demandDBHelper.getAll(InventoryHealthMckRestockManualConfig.class, condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return ListUtils.toMapList(
                all.stream().map(o -> {
                    List<String> instanceTypeAndZoneName = this.getGroupByInstanceTypeAndZoneName(req, o.getInstanceType(), o.getZoneName());
                    o.setInstanceType(instanceTypeAndZoneName.get(0));
                    o.setZoneName(instanceTypeAndZoneName.get(1));
                    return o;
                }).collect(Collectors.toList()),
                o -> this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getYear(), o.getWeek()),
                o -> o
        );
    }

    private WhereSQL getForecastCondition(QueryMckRestockReq req) {
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", req.getDemandDate().toString());
        condition.and("product not in ('GPU(裸金属&CVM)','裸金属')");
        condition.and(req.genBasicCondition());
        return condition;
    }

    /**
     * 按照机型 + 可用区 + 周维度，取计划日期的需求汇总
     */
    private Map<String, List<CommonRestockForecastDataItemDO>> getMckForecastSummaryDfDOS(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = getForecastCondition(req);

        List<String> groupByFields = this.getGroupByDimension(req);
        String groupBy = "demand_source, demand_type, compare, begin_buy_year, begin_buy_date, begin_buy_week, is_spike, is_head, customer_short_name, region_name" + groupByFields.get(0);
        String fields = "'CVM' product_type, demand_source, demand_type, compare, begin_buy_year year, begin_buy_date date, begin_buy_week week, is_spike, is_head, customer_short_name, region_name" + groupByFields.get(1);
        fields += ", sum(total_core) cores";

        condition.addGroupBy(groupBy);
        String sql = "select " + fields + " from std_crp.ads_mck_forecast_summary_df" + condition.getSQL();

        List<CommonRestockForecastDataItemDO> all = ckcldStdCrpDBHelper.getRaw(CommonRestockForecastDataItemDO.class, sql, condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }

        LocalDate date = LocalDate.parse("2024-07-08", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        //2024-07-08之前的数据没有进行头部分类 需要额外进行处理
        if (req.getDemandDate().isBefore(date)) {
            HashSet<String> headCustomer = new HashSet<>(getBigCustomerShortName());
            all.forEach(o -> {
                o.setIsHead(headCustomer.contains(o.getCustomerShortName())? 1 : 0);
            });
        }
        List<StaticZoneDO> zoneDOList = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, String> zoneToRegion = ListUtils.toMap(zoneDOList, StaticZoneDO::getZoneName, StaticZoneDO::getRegionName);
        all = all.stream().filter(o -> zoneToRegion.get(o.getZoneName()).equals(o.getRegionName())).collect(Collectors.toList());
        return ListUtils.toMapList(all, o -> this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getYear(), o.getWeek()), o -> o);
    }

    /**
     * 按照机型 + 可用区 + 周维度，取计划日期的预测安全库存
     */
    private Map<String, List<DwsInventoryHealthMckForecastMonthlySafeDfDO>> getMckForecastMonthlySafeInvDfDOS(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time = ?", req.getDate().toString());
        condition.and(req.genBasicCondition());

        List<DwsInventoryHealthMckForecastMonthlySafeDfDO> all = ckcldDBHelper.getAll(DwsInventoryHealthMckForecastMonthlySafeDfDO.class, condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return ListUtils.toMapList(
                all.stream().map(o -> {
                    List<String> instanceTypeAndZoneName = this.getGroupByInstanceTypeAndZoneName(req, o.getInstanceType(), o.getZoneName());
                    o.setInstanceType(instanceTypeAndZoneName.get(0));
                    o.setZoneName(instanceTypeAndZoneName.get(1));
                    return o;
                }).collect(Collectors.toList()),
                o -> this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getHolidayYear(), o.getHolidayWeek()),
                o -> o
        );
    }

    /**
     * 按照机型 + 可用区 维度，取计划日期的 MCK 弹性备货配额,每周一刷新
     */
    private Map<String, List<DwsBufferSafeInventoryDfDO>> getBufferSafeInventoryDfDOS(QueryMckRestockReq req, List<CvmType> allCvmType) {
        WhereSQL condition = new WhereSQL();
        LocalDate date = req.getDate();
        String curWeekMonday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        condition.and("stat_time = ?", curWeekMonday);
        condition.and(req.genBasicCondition());
        List<DwsBufferSafeInventoryDfDO> all = ckcldDBHelper.getAll(DwsBufferSafeInventoryDfDO.class, condition.getSQL(), condition.getParams());
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        return ListUtils.toMapList(
                all.stream().map(o -> {
                    List<String> instanceTypeAndZoneName = this.getGroupByInstanceTypeAndZoneName(req, o.getInstanceType(), o.getZoneName());
                    o.setInstanceType(instanceTypeAndZoneName.get(0));
                    o.setZoneName(instanceTypeAndZoneName.get(1));
                    return o;
                }).collect(Collectors.toList()),
                o -> StringTools.join("@", o.getProductType(), o.getInstanceType(), o.getZoneName()),
                o -> o
        );
    }

    private List<String> getGroupByDimension(QueryMckRestockReq req) {
        String fields = "";
        String groupBy = "";

        if (ListUtils.isNotEmpty(req.getGroupByDimension())) {
            if (req.getGroupByDimension().contains("实例类型")) {
                fields += ", 'ALL' instance_type_rename";
            } else {
                groupBy += ", instance_type";
                fields += ", instance_type instance_type_rename";
            }
            if (req.getGroupByDimension().contains("可用区")) {
                fields += ", 'ALL' zone_name_rename";
            } else {
                groupBy += ", zone_name";
                fields += ", zone_name zone_name_rename";
            }
        } else {
            groupBy += ", instance_type, zone_name";
            fields += ", instance_type instance_type_rename, zone_name zone_name_rename";
        }

        return ListUtils.newList(groupBy, fields);
    }

    private List<String> getGroupByInstanceTypeAndZoneName(QueryMckRestockReq req, String instanceType, String zoneName) {
        if (ListUtils.isNotEmpty(req.getGroupByDimension())) {
            if (req.getGroupByDimension().contains("实例类型")) {
                instanceType = "ALL";
            }
            if (req.getGroupByDimension().contains("可用区")) {
                zoneName = "ALL";
            }
        }

        return ListUtils.newList(instanceType, zoneName);
    }

    private List<QueryMckRestockResp.Item> mergeResultsByDimension(List<QueryMckRestockResp.Item> weekRespItems, QueryMckRestockReq req) {
        //1.先将weekRespItems中的instance_type与zone_name替换 在按照product_type + instance_type + zone_name + week做键进行分类
        Map<String, List<QueryMckRestockResp.Item>> tempResult = ListUtils.toMapList(
                weekRespItems.stream().map(o -> {
                    List<String> instanceTypeAndZoneName = this.getGroupByInstanceTypeAndZoneName(req, o.getInstanceType(), o.getZoneName());
                    o.setInstanceType(instanceTypeAndZoneName.get(0));
                    o.setZoneName(instanceTypeAndZoneName.get(1));
                    return o;
                }).collect(Collectors.toList()), o -> this.getKey(o.getProductType(), o.getInstanceType(), o.getZoneName(), o.getYear(), o.getWeek()), o -> o
        );
        //2.再将同类的item进行合并生成新的item
        List<QueryMckRestockResp.Item> result = new ArrayList<>();
        for (Map.Entry<String, List<QueryMckRestockResp.Item>> entry : tempResult.entrySet()) {
            List<QueryMckRestockResp.Item> tempItems = entry.getValue();
            QueryMckRestockResp.Item item = tempItems.get(0);
            if(item.getZoneName().equals("ALL")) {
                item.setCustomhouseTitle(null);
                item.setAreaName(null);
                item.setRegionName(null);
            }

            //将人工调整原因置为null
            item.setSafeInventoryManualConfigReason(null);
            item.setSupplyManualConfigReason(null);
            item.setDemandManualConfigReason(null);
            //2.1 设置总的统计数
            item.setBeginInventory(NumberUtils.sum(tempItems, o -> o.getBeginInventory()));
            item.setSupplyTotal(NumberUtils.sum(tempItems, o -> o.getSupplyTotal()));
            item.setDemandTotal(NumberUtils.sum(tempItems, o -> o.getDemandTotal()));
            item.setEndInventory(NumberUtils.sum(tempItems, o -> o.getEndInventory()));
            item.setSafeInventoryTotal(NumberUtils.sum(tempItems, o -> o.getSafeInventoryTotal()));
            item.setTotalGap(NumberUtils.sum(tempItems, o -> o.getTotalGap()));
            //2.2设置明细数
            //2.2.1 供应明细
            if (tempItems.stream().anyMatch(o -> o.getSupplyOnTheWay() != null)) {
                item.setSupplyOnTheWay(NumberUtils.sum(tempItems, o -> o.getSupplyOnTheWay()));
            }else {
                item.setSupplyOnTheWay(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getSupplyCurrentOrder() != null)) {
                item.setSupplyCurrentOrder(NumberUtils.sum(tempItems, o -> o.getSupplyCurrentOrder()));
            }else {
                item.setSupplyCurrentOrder(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getSupplyManualConfig() != null)) {
                item.setSupplyManualConfig(NumberUtils.sum(tempItems, o -> o.getSupplyManualConfig()));
            }else {
                item.setSupplyManualConfig(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getSupplyToDownstreamDemand() != null)) {
                item.setSupplyToDownstreamDemand(NumberUtils.sum(tempItems, o -> o.getSupplyToDownstreamDemand()));
            }else {
                item.setSupplyToDownstreamDemand(null);
            }
            //2.2.2 需求明细
            if (tempItems.stream().anyMatch(o -> o.getDemandBigCustomerMonthly() != null)) {
                item.setDemandBigCustomerMonthly(NumberUtils.sum(tempItems, o -> o.getDemandBigCustomerMonthly()));
            }else {
                item.setDemandBigCustomerMonthly(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandBigCustomerMonthlyForecast() != null)) {
                item.setDemandBigCustomerMonthlyForecast(NumberUtils.sum(tempItems, o -> o.getDemandBigCustomerMonthlyForecast()));
            }else {
                item.setDemandBigCustomerMonthlyForecast(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandBigCustomerMonthlyOrder() != null)) {
                item.setDemandBigCustomerMonthlyOrder(NumberUtils.sum(tempItems, o -> o.getDemandBigCustomerMonthlyOrder()));
            }else {
                item.setDemandBigCustomerMonthlyOrder(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandBigCustomerElastic() != null)) {
                item.setDemandBigCustomerElastic(NumberUtils.sum(tempItems, o -> o.getDemandBigCustomerElastic()));
            }else {
                item.setDemandBigCustomerElastic(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandMediumLongTailIndustry() != null)) {
                item.setDemandMediumLongTailIndustry(NumberUtils.sum(tempItems, o -> o.getDemandMediumLongTailIndustry()));
            }else {
                item.setDemandMediumLongTailIndustry(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandMediumLongTailIndustryForecast() != null)) {
                item.setDemandMediumLongTailIndustryForecast(NumberUtils.sum(tempItems, o -> o.getDemandMediumLongTailIndustryForecast()));
            }else {
                item.setDemandMediumLongTailIndustryForecast(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandMediumLongTailIndustryOrder() != null)) {
                item.setDemandMediumLongTailIndustryOrder(NumberUtils.sum(tempItems, o -> o.getDemandMediumLongTailIndustryOrder()));
            }else {
                item.setDemandMediumLongTailIndustryOrder(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandMediumLongTailModel() != null)) {
                item.setDemandMediumLongTailModel(NumberUtils.sum(tempItems, o -> o.getDemandMediumLongTailModel()));
            }else {
                item.setDemandMediumLongTailModel(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandReservation() != null)) {
                item.setDemandReservation(NumberUtils.sum(tempItems, o -> o.getDemandReservation()));
            }else {
                item.setDemandReservation(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandWeekPeakAvg12() != null)) {
                item.setDemandWeekPeakAvg12(NumberUtils.sum(tempItems, o -> o.getDemandWeekPeakAvg12()));
            }else {
                item.setDemandWeekPeakAvg12(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getDemandManualConfig() != null)) {
                item.setDemandManualConfig(NumberUtils.sum(tempItems, o -> o.getDemandManualConfig()));
            }else {
                item.setDemandManualConfig(null);
            }

            //2.2.3 期末库存明细
            if (tempItems.stream().anyMatch(o -> o.getTurnoverInv() != null)) {
                item.setTurnoverInv(NumberUtils.sum(tempItems, o -> o.getTurnoverInv()));
            }else {
                item.setTurnoverInv(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getCurrentWeekLatestInventory() != null)) {
                item.setCurrentWeekLatestInventory(NumberUtils.sum(tempItems, o -> o.getCurrentWeekLatestInventory()));
            }else {
                item.setCurrentWeekLatestInventory(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getTurnoverWeekNum() != null)) {
                item.setTurnoverWeekNum(NumberUtils.avg(tempItems.stream().map(o -> o.getTurnoverWeekNum()).collect(Collectors.toList()), 6));
            }else {
                item.setTurnoverWeekNum(null);
            }

            //2.2.4 安全库存
            if (tempItems.stream().anyMatch(o -> o.getMonthlySafeInventory() != null)) {
                item.setMonthlySafeInventory(NumberUtils.sum(tempItems, o -> o.getMonthlySafeInventory()));
            }else {
                item.setMonthlySafeInventory(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getBufferSafeInventory() != null)) {
                item.setBufferSafeInventory(NumberUtils.sum(tempItems, o -> o.getBufferSafeInventory()));
            }else {
                item.setBufferSafeInventory(null);
            }
            if (tempItems.stream().anyMatch(o -> o.getSafeInventoryManualConfig() != null)) {
                item.setSafeInventoryManualConfig(NumberUtils.sum(tempItems, o -> o.getSafeInventoryManualConfig()));
            }else {
                item.setSafeInventoryManualConfig(null);
            }

            //合并是否包含运营动作
            if (tempItems.stream().anyMatch(QueryMckRestockResp.Item::getIsContainData)) {
                item.setIsContainData(true);
            }else {
                item.setIsContainData(false);
            }

            result.add(item);

        }

        return result;
    }

    @Data
    public static class OperationActionNoArrivalDetailDO {
        /**
         * 设备类型
         */
        @Column("device_type")
        private String deviceType;
        /**
         * 行业部门
         */
        @Column("xy_industry")
        private String industryDept;
        /**
         * 区域
         */
        @Column("quota_campus_name")
        private String campus;
        /**
         * 客户名称
         */
        @Column("xy_customer_name")
        private String customName;
        /**
         * cloud提单时间
         */
        @Column("quota_create_time")
        private String createTime;
        /**
         * Q单号
         */
        @Column("quota_id")
        private String quotaId;
        /**
         * 预计交付时间
         */
        @Column("sla_date_expect")
        private String expectDeliveryTime;
        /**
         * 期望交付时间
         */
        @Column("quota_use_time")
        private String useTime;

        /**
         * 未到货核心数
         */
        @Column("no_arrival_num")
        private BigDecimal noArrivalCoreNum;

    }

    @Data
    @ToString
    @Table("ppl_version")
    public static class PplVersionDO extends BaseDO {
        /** 版本编码<br/>Column: [version_code] */
        @Column(value = "version_code")
        private String versionCode;

        /** 版本名称<br/>Column: [version_name] */
        @Column(value = "version_name")
        private String versionName;

        /** 需求开始年<br/>Column: [demand_begin_year] */
        @Column(value = "demand_begin_year")
        private Integer demandBeginYear;

        /** 需求开始月<br/>Column: [demand_begin_month] */
        @Column(value = "demand_begin_month")
        private Integer demandBeginMonth;

        /** 需求结束年<br/>Column: [demand_end_year] */
        @Column(value = "demand_end_year")
        private Integer demandEndYear;

        /** 需求结束月<br/>Column: [demand_end_month] */
        @Column(value = "demand_end_month")
        private Integer demandEndMonth;

        @Column(value = "deadline")
        private Date deadline;

        /** 版本类型，周版本月版本<br/>Column: [version_type]
         * */
        @Column(value = "version_type")
        private String versionType;

        /** 状态<br/>Column: [status]
         * */
        @Column(value = "status")
        private String status;

        /** 描述<br/>Column: [note] */
        @Column(value = "note")
        private String note;

        /** 创建人<br/>Column: [creator] */
        @Column(value = "creator")
        private String creator;

        /** 启动审批的时间<br/>Column: [start_audit_time] */
        @Column(value = "start_audit_time")
        private Date startAuditTime;

        /** 结束审批的时间<br/>Column: [end_audit_time] */
        @Column(value = "end_audit_time")
        private Date endAuditTime;
    }
}

