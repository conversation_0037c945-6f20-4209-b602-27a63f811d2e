package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.BigDataHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.EKSDataHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.big_data_actual.EKSDataHealthActualReq;
import java.util.List;

public interface BigDataHealthActualService {

    List<BigDataHealthActualData> queryBigDataHealthActualReport(BigDataHealthActualReq req);

    List<EKSDataHealthActualData> queryEKSDataHealthTrendReport(EKSDataHealthActualReq req);

    List<String> queryBigDataInstance(String productType);

    List<String> getBigDataProductPermission(String user);

    List<BigDataHealthTrendData> queryBigDataHealthTrendReport(BigDataHealthTrendReq req);

}
