package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class UpdateChangeResultReq {

    @NotNull(message = "更新id不能为空")
    private Long id;

    private String startYearMonth;

    private String endYearMonth;

    @NotBlank(message = "版本数据变动原因不能空")
    @Length(max = 4096, message = "版本数据变动原因字符长度不能超过4096")
    private String changeResult;

}
