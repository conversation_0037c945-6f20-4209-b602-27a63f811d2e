package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/23 14:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryTargetVO {
    private String key;

    /**
     * 库存目标
     */
    private BigDecimal inventoryTarget;

    /**
     * 库存阈值
     */
    private BigDecimal inventoryThreshold;

    public InventoryTargetVO(String key, BigDecimal inventoryTarget) {
        this.key = key;
        this.inventoryTarget = inventoryTarget;
    }
}
