package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.IInventoryTargetGroupKey;
import cloud.demand.lab.modules.order.service.filler.AreaNameFiller;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
public class CbsSafeStockByZoneVO implements ZoneCategoryFiller, CountryNameFiller, ZoneInfoFiller, IInventoryTargetGroupKey {

    @Column(value = "add_date")
    private String addDate;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneCategory;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "safe_stock")
    private BigDecimal safeStock;

    /**
     * 库存阈值
     */
    private BigDecimal stockThreshold;

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillZone(String zone) {

    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.customhouseTitle = customhouseTitle;
    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideZoneName() {
        return this.zoneName;
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }

    public void fillRegionName(String regionName) {
        this.regionName = regionName;
    }

    @Override
    public String getInventoryTargetGroupKey() {
        return StringUtils.joinWith("@", this.getAddDate(), this.getCustomhouseTitle(), this.getCountryName(), this.getAreaName(), this.getRegionName(),
                this.getZoneName(), this.getZoneCategory(), this.getVolumeType());
    }
}
