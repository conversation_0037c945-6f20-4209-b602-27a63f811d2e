package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplyAndDemandVersionChangeResultDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyAndDemandVersionChangeResultVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandVersionChangeResultService;
import cloud.demand.lab.modules.operation_view.util.DimJoinUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/15 13:12
 */
@Service
public class SupplyAndDemandVersionChangeResultServiceImpl implements SupplyAndDemandVersionChangeResultService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DictService dictService;

    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Override
    public List<SupplyAndDemandVersionChangeResultVO> queryChangeResult(QueryChangeResultReq req) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" product_category in (?) ",req.getProductCategory());
        whereSQL.and(" start_version = ? ",req.getStartVersion());
        whereSQL.and(" end_version = ? ",req.getEndVersion());
        whereSQL.andIf(!StringUtils.isEmpty(req.getStartYearMonth())," start_year_month <= ? or start_year_month = ''",req.getStartYearMonth());
        whereSQL.andIf(!StringUtils.isEmpty(req.getEndYearMonth())," end_year_month >= ? or end_year_month = ''",req.getEndYearMonth());
        whereSQL.and(" data_type in (?) ",req.getDataType());

        // 按照key进行排序（这里类型联合索引，不过左右乱序匹配都可以，但不能跨dim匹配）
        Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimValue());
        whereSQL.andIf(!dim.isEmpty()," dim_key like concat('%',?,'%') ", DimJoinUtil.join(dim.keySet()));
        whereSQL.andIf(!dim.isEmpty()," dim_value like concat('%',?,'%') ", DimJoinUtil.join(dim.values()));
        // 查询条件匹配（转md5精准匹配，为null数据库默认存(空值)）
        whereSQL.and( " query_md5 = ? ", DimJoinUtil.md5(DimJoinUtil.json(req.getQueryData())));

        whereSQL.addOrderBy("update_time");
        List<SupplyAndDemandVersionChangeResultDO> dataList = demandDBHelper.getAll(SupplyAndDemandVersionChangeResultDO.class,whereSQL.getSQL(),whereSQL.getParams());
        return ListUtils.transform(dataList,SupplyAndDemandVersionChangeResultVO::transform);
    }

    @Override
    public void createChangeResult(CreateChangeResultReq req) {
        SupplyAndDemandVersionChangeResultDO data = new SupplyAndDemandVersionChangeResultDO();
        data.setProductCategory(req.getProductCategory());
        data.setChangeResult(req.getChangeResult());
        data.setStartVersion(req.getStartVersion());
        data.setEndVersion(req.getEndVersion());
        data.setStartYearMonth(req.getStartYearMonth());
        data.setEndYearMonth(req.getEndYearMonth());
        data.setDataType(req.getDataType());
        // 按key排序后分割且前后都要有@
        Map<String, String> dim = DimJoinUtil.getDim(req.getDimKey(), req.getDimValue());
        fillUpperLevel(dim);
        data.setDimKey(DimJoinUtil.join(dim.keySet()));
        data.setDimValue(DimJoinUtil.join(dim.values()));

        // 设置查询参数(json用于查询返回给前端，md5用于精准匹配)
        data.setQueryJson(DimJoinUtil.json(req.getQueryData()));
        data.setQueryMd5(DimJoinUtil.md5(data.getQueryJson()));
        demandDBHelper.insert(data);
    }

    @Override
    public void updateChangeResult(UpdateChangeResultReq req) {
        SupplyAndDemandVersionChangeResultDO one = demandDBHelper.getOne(SupplyAndDemandVersionChangeResultDO.class, " where id = ? ", req.getId());
        if (Objects.isNull(one)) {
            throw new BizException("变化原因不存在，id：" + req.getId());
        }
        one.setStartYearMonth(req.getStartYearMonth());
        one.setEndYearMonth(req.getEndYearMonth());
        one.setChangeResult(req.getChangeResult());
        demandDBHelper.update(one);
    }

    @Override
    public void deleteChangeResult(List<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            demandDBHelper.delete(SupplyAndDemandVersionChangeResultDO.class, "where id in (?)", ids);
        }
    }

    private void fillUpperLevel(Map<String, String> dim){
        List<StaticZoneDO>  staticZoneDOList = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, String> regionName2CountryMap = dictService.getRegionName2CountryMapping();
        if(dim.containsKey("zoneName")){
            Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap =
                    demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()))
                            .stream().collect(Collectors.toMap(InventoryHealthMainZoneNameConfigDO::getZoneName, Function.identity(),(k1, k2) -> k1));
            Map<String,StaticZoneDO> staticZoneDOMap = ListUtils.toMap(staticZoneDOList,StaticZoneDO::getZoneName, Function.identity());
            if(staticZoneDOMap.containsKey(dim.get("zoneName"))){
                dim.put("regionName",staticZoneDOMap.get(dim.get("zoneName")).getRegionName());
                dim.put("areaName",staticZoneDOMap.get(dim.get("zoneName")).getAreaName());
                dim.put("countryName",regionName2CountryMap.get(dim.get("regionName")));
                dim.put("customhouseTitle",staticZoneDOMap.get(dim.get("zoneName")).getCustomhouseTitle());
            }
            if(zoneConfigMap.containsKey(dim.get("zoneName"))){
                dim.put("zoneCategory",zoneConfigMap.get(dim.get("zoneName")).getTypeName());
            }
        }
        if(dim.containsKey("regionName")){
            Map<String,StaticZoneDO> staticZoneDOMap = ListUtils.toMap(staticZoneDOList,StaticZoneDO::getRegionName, Function.identity());
            if(staticZoneDOMap.containsKey(dim.get("regionName"))){
                dim.put("areaName",staticZoneDOMap.get(dim.get("regionName")).getAreaName());
                dim.put("countryName",regionName2CountryMap.get(dim.get("regionName")));
                dim.put("customhouseTitle",staticZoneDOMap.get(dim.get("regionName")).getCustomhouseTitle());
            }
        }
        if(dim.containsKey("areaName")){
            Map<String,StaticZoneDO> staticZoneDOMap = ListUtils.toMap(staticZoneDOList,StaticZoneDO::getAreaName, Function.identity());
            if(staticZoneDOMap.containsKey(dim.get("areaName"))){
                dim.put("customhouseTitle",staticZoneDOMap.get(dim.get("areaName")).getCustomhouseTitle());
            }
            ProductDemandDataDfDO demandDataDfDO = ckstdcrpDBHelper.getOne(ProductDemandDataDfDO.class," where area_name = ?",dim.get("areaName"));
            if(Objects.nonNull(demandDataDfDO)){
                dim.put("countryName",demandDataDfDO.getCountryName());
            }
        }
        if(dim.containsKey("instanceType")){
            Map<String, String> map = dictService.queryInstanceTypeToGroup();
            if(map.containsKey(dim.get("instanceType"))){
                dim.put("instanceGroup",map.get(dim.get("instanceType")));
            }
        }
    }
}
