package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/31 13:42
 */
@Data
public class ExportDemandItemVO {

    private String yearMonth;

    private String beginBuyDate;

    private String endBuyDate;

    private String dataSource;


    private String billNumber;

    private String industryDept;

    private String warZone;

    private String commonCustomerShortName;

    private String customerUin;

    private String projectName;

    private String demandScene;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private String demandType;

    private String volumeType;

    private String diskType;

    private String projectType;

    private String dbStorageType;

    private String dbDeployType;

    private String dbFrameworkType;

    private BigDecimal startAmount1;

    private BigDecimal endAmount1;

    private BigDecimal diffAmount1;

    private BigDecimal startAmount2;

    private BigDecimal endAmount2;

    private BigDecimal diffAmount2;

    private BigDecimal startAmount3;

    private BigDecimal endAmount3;

    private BigDecimal diffAmount3;



    public static List<ExportDemandItemVO> builder(List<DemandReportDetailVO> dataList, String startStatTime, String endStatTime) {
        List<ExportDemandItemVO> result = ListUtils.newArrayList();
        Map<String, List<DemandReportDetailVO>> groupMap = dataList.stream().collect(Collectors.groupingBy(item -> getGroupKey(item)));
        for (Map.Entry<String, List<DemandReportDetailVO>> entry : groupMap.entrySet()) {
            ExportDemandItemVO item = new ExportDemandItemVO();
            BeanUtils.copyProperties(entry.getValue().get(0), item);
            for (DemandReportDetailVO detailItem : entry.getValue()) {
                if (StringUtils.equals(detailItem.getStatTime(), startStatTime)) {
                    item.setStartAmount1(detailItem.getTotalNum());
                    item.setStartAmount2(detailItem.getTotalAmount());
                }
                if (StringUtils.equals(detailItem.getStatTime(), endStatTime)) {
                    item.setEndAmount1(detailItem.getTotalNum());
                    item.setEndAmount2(detailItem.getTotalAmount());
                }
                item.setDiffAmount1(SoeCommonUtils.sub(item.getEndAmount1(), item.getStartAmount1()));
                item.setDiffAmount2(SoeCommonUtils.sub(item.getEndAmount2(), item.getStartAmount2()));
            }
            result.add(item);
        }
        return result;
    }

    public static List<ExportDemandItemVO> dbBuilder(List<DemandReportDBDetailVO> dataList, String startStatTime, String endStatTime) {
        List<ExportDemandItemVO> result = ListUtils.newArrayList();
        Map<String, List<DemandReportDBDetailVO>> groupMap = dataList.stream().collect(Collectors.groupingBy(item -> getGroupKey(item)));
        for (Map.Entry<String, List<DemandReportDBDetailVO>> entry : groupMap.entrySet()) {
            ExportDemandItemVO item = new ExportDemandItemVO();
            BeanUtils.copyProperties(entry.getValue().get(0), item);
            for (DemandReportDBDetailVO detailItem : entry.getValue()) {
                if (StringUtils.equals(detailItem.getStatTime(), startStatTime)) {
                    item.setStartAmount1(detailItem.getTotalNum());
                    item.setStartAmount2(detailItem.getTotalMemory());
                    item.setStartAmount3(detailItem.getTotalStorage());
                }
                if (StringUtils.equals(detailItem.getStatTime(), endStatTime)) {
                    item.setEndAmount1(detailItem.getTotalNum());
                    item.setEndAmount2(detailItem.getTotalMemory());
                    item.setEndAmount3(detailItem.getTotalStorage());
                }
                item.setDiffAmount1(SoeCommonUtils.sub(item.getEndAmount1(), item.getStartAmount1()));
                item.setDiffAmount2(SoeCommonUtils.sub(item.getEndAmount2(), item.getStartAmount2()));
                item.setDiffAmount3(SoeCommonUtils.sub(item.getEndAmount3(), item.getStartAmount3()));
            }
            result.add(item);
        }
        return result;
    }

    public static String getGroupKey(DemandReportDetailVO data) {
        return StringUtils.joinWith("@", data.getYearMonth(), data.getBeginBuyDate(), data.getBeginBuyDate(), data.getDataSource(), data.getBillNumber(), data.getIndustryDept(),
                data.getWarZone(), data.getCommonCustomerShortName(), data.getCustomerUin(), data.getProjectName(), data.getDemandScene(),
                data.getZoneCategory(), data.getInstanceCategory(), data.getCustomhouseTitle(), data.getCountryName(), data.getAreaName(),
                data.getRegionName(), data.getZoneName(), data.getInstanceGroup(), data.getInstanceType(), data.getVolumeType(),data.getProjectType(), data.getDiskType(), data.getDemandType(),
                data.getDbStorageType(), data.getDbDeployType(), data.getDbFrameworkType());
    }
}
