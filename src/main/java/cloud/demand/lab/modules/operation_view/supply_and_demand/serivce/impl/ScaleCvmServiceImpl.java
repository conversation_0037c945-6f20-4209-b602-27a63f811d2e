package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.yunti.BasObsCloudCvmTypeDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DayCvmScaleReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DayCvmScaleVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandCommonReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ScaleService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:37
 */
@Service
public class ScaleCvmServiceImpl implements ScaleService {

    @Resource
    private DBHelper obsDBHelper;
    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DictService dictService;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CVM.getName();
    }

    @Override
    public List<DemandReportDetailVO> queryCurScaleData(DemandCommonReq req) {
        return ListUtils.newArrayList();
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req) {
        return queryChangeScaleDataFromLastMonth(req, null, null);
    }

    @Override
    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq commonReq, List<String> notOtherCustomerUin, List<String> notOtherCommonCustomerShortName) {
        DayCvmScaleReq req = DayCvmScaleReq.transform(commonReq, notOtherCustomerUin, notOtherCommonCustomerShortName);

        WhereBuilder whereBuilder = new WhereBuilder(req, ProductDemandDataDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        whereSQL.and(" product in (?) ", ListUtils.newArrayList("CVM"));
        whereSQL.and(" app_role not in (?) ", ListUtils.newArrayList("LH"));

        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_day_cvm_scale.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());
        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());

        List<TxyRegionInfoDTO> allRegionInfoList = dictService.getAllTxyRegionInfo();
        sql = SimpleSqlBuilder.doReplace(sql, "country_name", getCountryNameSelectCase(allRegionInfoList));
        sql = SimpleSqlBuilder.doReplace(sql, "area_name", getAreaNameSelectCase(allRegionInfoList));

        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        sql = SimpleSqlBuilder.doReplace(sql, "zone_category", getZoneCategorySelectCase(zoneConfigList));

        List<InventoryHealthMainInstanceTypeConfigDO> instanceConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ? and product = 'CVM' ", DateUtils.formatDate(DateUtils.yesterday()));
        sql = SimpleSqlBuilder.doReplace(sql, "instance_category", getInstanceCategorySelectCase(instanceConfigList));

        List<BasObsCloudCvmTypeDO> cvmTypeList = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class, "group by CvmInstanceTypeCode");
        sql = SimpleSqlBuilder.doReplace(sql, "instance_group", getInstanceGroupSelectCase(cvmTypeList));

        sql = SimpleSqlBuilder.doReplace(sql, "un_customer_short_name", getUinOrCommonShortNameSelectCase(req.getNotOtherCommonCustomerShortName(), "un_customer_short_name", "'其他客户'"));
        sql = SimpleSqlBuilder.doReplace(sql, "uin", getUinOrCommonShortNameSelectCase(req.getNotOtherCustomerUin(), "uin", "0"));

        List<String> fieldNames = Arrays.stream(DayCvmScaleVO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), req.getDims());
        List<DayCvmScaleVO> dataList = ckcldStdCrpDBHelper.getRaw(DayCvmScaleVO.class, sql, whereSQL.getParams());

        return ListUtils.transform(dataList, DemandReportDetailVO::transform);
    }

}
