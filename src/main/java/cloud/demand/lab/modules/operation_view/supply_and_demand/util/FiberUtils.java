package cloud.demand.lab.modules.operation_view.supply_and_demand.util;

import erp.base.fiber.support.dispatcher.FiberTaskExecutor;
import lombok.Getter;
import report.utils.utils.SecurityFiberSupplier;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/** FiberTaskExecutor.submit()增强
 * 可主动捕获异常
 * */
public class FiberUtils {

    /** FiberTask池 */
    private static final FiberTaskExecutor executor = FiberTaskExecutor.newDefaultExecutor();

    public static Executor getExecutor() {
        return getExecutor(null, null);
    }

    public static Executor getExecutor(FiberTaskExecutor executor) {
        return getExecutor(null, executor);
    }

    public static Executor getExecutor(Integer count) {
        return getExecutor(count, null);
    }

    /**
     * @param count 线程数据
     * @param executor 异步线程池执行器
     * @return 执行器
     */
    public static Executor getExecutor(Integer count, FiberTaskExecutor executor) {
        return new Executor(executor == null ? FiberUtils.executor : executor, count == null ? null : new CountDownLatch(count));
    }


    @Getter
    public static class Executor {
        /** FiberTask池 */
        private final FiberTaskExecutor fiberTaskExecutor;

        /** 是否有异常 */
        private final AtomicBoolean hasError = new AtomicBoolean(false);

        /** 异常列表 */
        private List<Exception> exceptionList;

        /** 门栓 */
        private final CountDownLatch countDownLatch;

        public Executor(FiberTaskExecutor fiberTaskExecutor) {
            this(fiberTaskExecutor, null);
        }

        /**
         * @param fiberTaskExecutor FiberTask池，不指定就是默认的
         * @param countDownLatch 门栓
         */
        public Executor(FiberTaskExecutor fiberTaskExecutor, CountDownLatch countDownLatch) {
            this.fiberTaskExecutor = fiberTaskExecutor;
            this.countDownLatch = countDownLatch;
        }

        /**
         * 提交异步任务
         * @param runnable 任务
         */
        public void submit(Runnable runnable) {
            fiberTaskExecutor.submit(new SecurityFiberSupplier() {
                @Override
                public void consume() {
                    try {
                        runnable.run();
                    } catch (Exception e) {
                        // 失败设置异常状态
                        hasError.set(true);
                        // 添加异常到集合
                        addError(e);
                    } finally {
                        // 门栓减一
                        if (countDownLatch != null) {
                            countDownLatch.countDown();
                        }
                    }
                }
            });
        }

        /** 是否有异常 */
        public boolean hasError() {
            return hasError.get();
        }


        /**
         * 超时返回 false，否则 true
         *
         * @param timeout 超时时间
         * @param unit 单位
         * @return
         */
        public boolean await(long timeout, TimeUnit unit) {
            if (countDownLatch != null) {
                // 有门栓
                try {
                    // timeout <= 0 一直等待（不限制超时时间）
                    if (timeout <= 0) {
                        countDownLatch.await();
                    } else {
                        // 限制超时时间
                        if (!countDownLatch.await(timeout, unit)) {
                            return false;
                        }
                    }
                } catch (InterruptedException e) {
                    return false;
                }
            }
            return true;
        }

        /**
         * 添加异常
         * @param e 异常
         */
        public synchronized void addError(Exception e) {
            if (e == null) {
                return;
            }
            if (exceptionList == null) {
                exceptionList = new ArrayList<>();
            }
            exceptionList.add(e);
        }
    }
}
