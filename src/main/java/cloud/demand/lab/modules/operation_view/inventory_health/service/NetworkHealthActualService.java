package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendReq;
import java.util.List;

public interface NetworkHealthActualService {


    List<NetworkHealthActualTrendData> queryNetworkHealthActualTrendReport(NetworkHealthActualTrendReq req);

}
