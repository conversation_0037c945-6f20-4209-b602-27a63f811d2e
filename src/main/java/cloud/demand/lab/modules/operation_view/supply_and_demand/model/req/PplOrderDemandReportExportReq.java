package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;



import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class PplOrderDemandReportExportReq extends DemandCommonReq {

    @NotBlank(message = "开始版本不能为空")
    private String startStatTime;

    @NotBlank(message = "结束版本不能为空")
    private String endStatTime;

    private String startYearMonth;

    private String endYearMonth;

    private List<String> dims = ListUtils.newArrayList("billNumber","industryDept","warZone","commonCustomerShortName","customerUin",
            "projectName","demandScene","customhouseTitle","countryName","areaName","regionName",
            "zoneCategory","zoneName","instanceCategory","instanceGroup","instanceType");

    private boolean forAdjust = false;//是否校准导出
}
