package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.AreaNameFiller;
import cloud.demand.lab.modules.order.service.filler.CountryNameFiller;
import cloud.demand.lab.modules.order.service.filler.InnerFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.InstanceGroupFiller;
import cloud.demand.lab.modules.order.service.filler.MainZoneFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;

@Data
@ToString
@Table("ppl_and_order_demand_data_df")
public class ProductDemandDataDfDO implements CountryNameFiller, InstanceGroupFiller, ZoneCategoryFiller, InstanceCategoryFiller, AreaNameFiller, InnerFiller, MainZoneFiller {

    /**
     * 分区键，代表数据版本<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 年月 <br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private String yearMonth;

    /**
     * 年月类型 <br/>Column: [month_type]
     */
    @Column(value = "month_type")
    private String monthType = "当月";// 年月类型: 1. 当月 2. 跨月

    /**
     * 产品大类，例如CVM、CBS、数据库、GPU<br/>Column: [product]
     */
    @Column(value = "product_category")
    private String productCategory;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product", insertValueScript = "'(空值)'")
    private String product;

    private String billNumber;

    private String dataType;

    /**
     * 订单号<br/>Column: [order_number]
     */
    @Column(value = "order_number", insertValueScript = "'(空值)'")
    private String orderNumber;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order", insertValueScript = "'(空值)'")
    private String pplOrder;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept", insertValueScript = "'(空值)'")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone", insertValueScript = "'(空值)'")
    private String warZone;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name", insertValueScript = "'(空值)'")
    private String customerShortName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin", insertValueScript = "'(空值)'")
    private String customerUin;

    /**
     * 通用客户简称<br/>Column: [common_customer_short_name]
     */
    @Column(value = "common_customer_short_name", insertValueScript = "'(空值)'")
    private String commonCustomerShortName;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name", insertValueScript = "'(空值)'")
    private String projectName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene", insertValueScript = "'(空值)'")
    private String demandScene;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title", insertValueScript = "'(空值)'")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    @Column(value = "country_name", insertValueScript = "'(空值)'")
    private String countryName;

    /**
     * 区域名称<br/>Column: [area_name]
     */
    @Column(value = "area_name", insertValueScript = "'(空值)'")
    private String areaName;

    /**
     * 地域编码<br/>Column: [region]
     */
    @Column(value = "region", insertValueScript = "'(空值)'")
    private String region;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name", insertValueScript = "'(空值)'")
    private String regionName;

    /**
     * 可用区类型<br/>Column: [zone_category]
     */
    @Column(value = "zone_category", insertValueScript = "'(空值)'")
    private String zoneCategory;

    /**
     * 可用区编码<br/>Column: [zone]
     */
    @Column(value = "zone", insertValueScript = "'(空值)'")
    private String zone;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name", insertValueScript = "'(空值)'")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_category]
     */
    @Column(value = "instance_category", insertValueScript = "'(空值)'")
    private String instanceCategory;

    /**
     * 实例族<br/>Column: [instance_group]
     */
    @Column(value = "instance_group", insertValueScript = "'(空值)'")
    private String instanceGroup;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type", insertValueScript = "'(空值)'")
    private String instanceType;

    /**
     * ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status]
     */
    @Column(value = "status", insertValueScript = "'(空值)'")
    private String status;

    /**
     * 数据源: 1.ORDER（订单数据） 2.VERSION（版本数据） 3.EXTEND（继承数据）<br/>Column: [data_source]
     */
    @Column(value = "data_source", insertValueScript = "'(空值)'")
    private String dataSource;

    /**
     * 订单来源<br/>Column: [order_source]
     */
    @Column(value = "order_source", insertValueScript = "'(空值)'")
    private String orderSource;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type", insertValueScript = "'(空值)'")
    private String demandType;

    /**
     * 开始购买时间<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date", insertValueScript = "'(空值)'")
    private String beginBuyDate;

    /**
     * 结束购买时间<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date", insertValueScript = "'(空值)'")
    private String endBuyDate;

    /**
     * 需求口径：原始需求、人工校准、系统校准
     */
    @Column(value = "demand_caliber", insertValueScript = "'(空值)'")
    private String demandCaliber = "共识需求";

    /**
     * '需求台数'<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private BigDecimal instanceNum = BigDecimal.ZERO;

    /**
     * 需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private BigDecimal totalCore = BigDecimal.ZERO;

    /**
     * 购买核心数<br/>Column: [buy_total_core]
     */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalCore = BigDecimal.ZERO;

    /**
     * 待购买核心数<br/>Column: [wait_buy_total_core]
     */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore = BigDecimal.ZERO;


    /**
     * 云盘类型：高性能/SSD,product_category=CBS有值
     */
    @Column(value = "volume_type", insertValueScript = "'(空值)'")
    private String volumeType;

    /**
     * 硬盘类型：系统盘/数据盘,product_category=CBS有值
     */
    @Column(value = "disk_type", insertValueScript = "'(空值)'")
    private String diskType;

    /**
     * 内部规则：is_inner=1&行业部门=内部业务部,product_category=CBS有值
     */
    @Column(value = "is_inner", insertValueScript = "'-1'")
    private Integer isInner;

    /**
     * 硬盘数,product_category=CBS有值
     */
    @Column(value = "disk_num")
    private BigDecimal diskNum = BigDecimal.ZERO;

    /**
     * 硬盘大小：GB,product_category=CBS有值
     */
    @Column(value = "disk_storage")
    private BigDecimal diskStorage = BigDecimal.ZERO;

    /**
     * 已购买磁盘大小<br/>Column: [buy_disk_storage]
     */
    @Column(value = "buy_disk_storage")
    private BigDecimal buyDiskStorage = BigDecimal.ZERO;

    /**
     * 待购买磁盘大小<br/>Column: [wait_buy_disk_storage]
     */
    @Column(value = "wait_buy_disk_storage")
    private BigDecimal waitBuyDiskStorage = BigDecimal.ZERO;

    @Column(value = "project_type", insertValueScript = "'(空值)'")
    private String projectType;

    @Column(value = "db_storage_type", insertValueScript = "'(空值)'")
    private String dbStorageType;

    @Column(value = "db_deploy_type", insertValueScript = "'(空值)'")
    private String dbDeployType;

    @Column(value = "db_framework_type", insertValueScript = "'(空值)'")
    private String dbFrameworkType;

    @Column(value = "db_memory")
    private BigDecimal dbMemory = BigDecimal.ZERO;

    @Column(value = "buy_db_memory")
    private BigDecimal buyDbMemory = BigDecimal.ZERO;

    @Column(value = "wait_buy_db_memory")
    private BigDecimal waitBuyDbMemory = BigDecimal.ZERO;

    /**
     * 弹性类型，一次性、日弹性、周弹性、月弹性
     */
    @Column(value = "elastic_type", insertValueScript = "'(空值)'")
    private String elasticType;

    /** 卡型/gpu类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type", insertValueScript = "'(空值)'")
    private String gpuType;

    /** GPU总卡数<br/>Column: [total_gpu_num] */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum = BigDecimal.ZERO;

    /** 购买GPU卡数<br/> */
    @Column(value = "buy_total_gpu_num")
    private BigDecimal buyTotalGpuNum = BigDecimal.ZERO;

    /** 待购买GPU卡数<br/> */
    @Column(value = "wait_buy_total_gpu_num")
    private BigDecimal waitBuyTotalGpuNum = BigDecimal.ZERO;

    private BigDecimal dbNum = BigDecimal.ZERO;

    @Override
    public String provideRegion() {
        return null;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillZone(String zone) {
        if (StringUtils.equals(this.zoneName, "随机可用区") || StringUtils.equals(this.zoneName, Constant.EMPTY_STR)
                || (!StringUtils.equals(this.getRegionName(),"南京") &&  StringUtils.equals(this.zoneName, "南京三区"))) {
            this.zone = zone;
        }
    }

    @Override
    public void fillZoneName(String zoneName) {
        if (StringUtils.equals(this.zoneName, "随机可用区") || StringUtils.equals(this.zoneName, Constant.EMPTY_STR) ||
                (!StringUtils.equals(this.getRegionName(),"南京") &&  StringUtils.equals(this.zoneName, "南京三区"))) {
            this.zoneName = zoneName;
        }
    }

    @Override
    public void fillIsMainZone(boolean isMainZone) {

    }

    @Override
    public void fillCountryName(String countryName) {
        this.countryName = countryName;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public String provideCustomhouseTitle() {
        return this.customhouseTitle;
    }

    @Override
    public String provideZoneName() {
        return this.zoneName;
    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }

    @Override
    public void fillInstanceCategory(String instanceCategory) {
        this.instanceCategory = instanceCategory;
    }

    @Override
    public void fillInstanceGroup(String instanceGroup) {
        this.instanceGroup = instanceGroup;
    }

    @Override
    public String provideUin() {
        return customerUin;
    }

    @Override
    public String provideIndustryDept() {
        return industryDept;
    }

    @Override
    public void fillIsInner(Integer isInner) {
        this.isInner = isInner;
    }

    public String getBillNumber() {
        if (StringUtils.isNotBlank(this.billNumber)) {
            return this.billNumber;
        }
        if (StringUtils.equals(this.dataSource, "订单")) {
            return this.orderNumber;
        } else {
            return this.pplOrder;
        }
    }

    public void fillDbNum(Map<String, BigDecimal> memoryMap) {
        if(StringUtils.equals(this.getProductCategory(),ProductCategoryEnum.DB.getName())) {

            BigDecimal singleMemory = memoryMap.getOrDefault(this.getProduct(), BigDecimal.ZERO);
            if (singleMemory.compareTo(BigDecimal.ZERO) == 0) {
                this.dbNum = BigDecimal.ZERO;
            }
            //dbNum = dbMemory/singleMemory
            this.dbNum = SoeCommonUtils.divide(this.getDbMemory(),singleMemory);
        }else {
            this.dbNum = BigDecimal.ZERO;
        }
    }

    public static Function<ProductDemandDataDfDO, BigDecimal> totalAmountTargetGetter(String dataSource, String orderRange, String unit, String productCategory) {
        Function<ProductDemandDataDfDO, BigDecimal> getter = null;
        UnitEnum unitEnum = UnitEnum.getByName(unit);
        switch (unitEnum) {
            case CORE:
                if ((!StringUtils.equals(dataSource, "订单")) || StringUtils.isEmpty(orderRange)) {
                    getter = ProductDemandDataDfDO::getTotalCore;
                    break;
                }
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyTotalCore;
                } else if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_BUY)) {
                    getter = ProductDemandDataDfDO::getBuyTotalCore;
                } else {
                    getter = ProductDemandDataDfDO::getTotalCore;
                }
                break;
            case STORAGE:
                if ((!StringUtils.equals(dataSource, "订单")) || StringUtils.isEmpty(orderRange)) {
                    getter = ProductDemandDataDfDO::getDiskStorage;
                    break;
                }
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyDiskStorage;
                } else if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_BUY)) {
                    getter = ProductDemandDataDfDO::getBuyDiskStorage;
                } else {
                    getter = ProductDemandDataDfDO::getDiskStorage;
                }
                break;
            case MEMORY:
                if ((!StringUtils.equals(dataSource, "订单")) || StringUtils.isEmpty(orderRange)) {
                    getter = ProductDemandDataDfDO::getDbMemory;
                    break;
                }
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyDbMemory;
                } else if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_BUY)) {
                    getter = ProductDemandDataDfDO::getBuyDbMemory;
                } else {
                    getter = ProductDemandDataDfDO::getDbMemory;
                }
                break;
            case NUM:
                getter = numTargetGetter(productCategory);
                break;
            default:
                throw new BizException("不支持的单位");
        }
        return getter;
    }

    public static Function<ProductDemandDataDfDO, BigDecimal> waitOrBuyAmountTargetGetter(String orderRange, String unit, String productCategory) {
        Function<ProductDemandDataDfDO, BigDecimal> getter = null;
        UnitEnum unitEnum = UnitEnum.getByName(unit);
        switch (unitEnum) {
            case CORE:
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyTotalCore;
                } else {
                    getter = ProductDemandDataDfDO::getBuyTotalCore;
                }
                break;
            case STORAGE:
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyDiskStorage;
                } else {
                    getter = ProductDemandDataDfDO::getBuyDiskStorage;
                }
                break;
            case MEMORY:
                if (StringUtils.equals(orderRange, Constant.ORDER_RANGE_WAIT_BUY)) {
                    getter = ProductDemandDataDfDO::getWaitBuyDbMemory;
                } else {
                    getter = ProductDemandDataDfDO::getBuyDbMemory;
                }
                break;
            case NUM:
                getter = numTargetGetter(productCategory);
                break;
            default:
                throw new BizException("不支持的单位");
        }
        return getter;
    }

    public static Function<ProductDemandDataDfDO, BigDecimal> numTargetGetter(String productCategory) {
        Function<ProductDemandDataDfDO, BigDecimal> getter = null;
        ProductCategoryEnum categoryEnum = ProductCategoryEnum.getByName(productCategory);
        switch (categoryEnum) {
            case CVM:
                getter = ProductDemandDataDfDO::getInstanceNum;
                break;
            case DB:
                getter = ProductDemandDataDfDO::getDbNum;
                break;
            case CBS:
                getter = ProductDemandDataDfDO::getDiskNum;
                break;
            default:
                throw new BizException("不支持的单位");
        }
        return getter;
    }

}