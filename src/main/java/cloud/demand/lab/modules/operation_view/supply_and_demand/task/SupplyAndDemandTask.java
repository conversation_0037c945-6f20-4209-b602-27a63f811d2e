package cloud.demand.lab.modules.operation_view.supply_and_demand.task;

import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandGenService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import task.run.exporter.anno.TaskRunLog;

@Slf4j
@Service
public class SupplyAndDemandTask {

    @Resource
    SupplyAndDemandGenService supplyAndDemandGenService;

    @Scheduled(cron = "0 55 8 * * ?")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "ORDER", nameScript = "'genSupplyOnTheWayDataForCVM'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genSupplyOnTheWayDataForCVM() {
        String statTime = DateUtils.formatDate(DateUtils.today().minusDays(1));
        supplyAndDemandGenService.genSupplyOnTheWayData(statTime);
    }

    @Scheduled(cron = "0 10 14 * * ?")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    public void syncDemandData() {

    }

    @Scheduled(cron = "0 0 14 * * ?")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "ORDER", nameScript = "'syncPplJoinOrderNewestData'", keyScript = "'java.time.LocalDate.now()'")
    public void syncPplJoinOrderNewestData() {
        supplyAndDemandGenService.syncPplJoinOrderNewestData(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE), false);
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.CBS.getName(),
                ProductCategoryEnum.DB.getName());
        supplyAndDemandGenService.syncDemandData(LocalDate.now(), productCategory);
    }

    @Scheduled(cron = "0 15 9 * * ?")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "ORDER", nameScript = "'genInventoryData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genInventoryData() {
        supplyAndDemandGenService.syncInventoryData(LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

    @Scheduled(cron = "0 0 8 * * ?")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "ORDER", nameScript = "'genInventoryTargetData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genInventoryTargetData() {
        supplyAndDemandGenService.genInventoryTargetData(LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

    @Scheduled(cron = "0 0 7 * * ?")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "ORDER", nameScript = "'genDbSaleScaleData'", keyScript = "'java.time.LocalDate.now().minusDays(1)'")
    public void genDbSaleScaleData() {
        supplyAndDemandGenService.genDbSaleScaleData(LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    @TaskRunLog(namespace = "ORDER", nameScript = "'syncCvmGpuDemandData'", keyScript = "''")
    @Synchronized(waitLockMillisecond = 1800, throwExceptionIfNotGetLock = false)
    public void syncCvmGpuDemandData() {
        supplyAndDemandGenService.syncCvmDemandData(DateUtils.today(), false);
        supplyAndDemandGenService.syncGpuDemandData(DateUtils.today(), false);
    }

}
