package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.common.entity.BaseUserDO;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 产品供应看板原因分析
 */
@Data
@ToString
@Table("supply_and_demand_version_change_result")
public class SupplyAndDemandVersionChangeResultDO extends BaseUserDO {

    @Column(value = "product_category")
    private String productCategory;

    /** 开始版本<br/>Column: [start_version] */
    @Column(value = "start_version")
    private String startVersion;

    /** 结束版本<br/>Column: [end_version] */
    @Column(value = "end_version")
    private String endVersion;

    /** 开始月份<br/>Column: [start_year_month] */
    @Column(value = "start_year_month")
    private String startYearMonth;

    /** 结束月份<br/>Column: [end_year_month] */
    @Column(value = "end_year_month")
    private String endYearMonth;

    /** 数据类型:采购-supply、需求-demand<br/>Column: [data_type] */
    @Column(value = "data_type")
    private String dataType;

    /** 维度值集合,用@分割,开头结尾都有@<br/>Column: [dim_key] */
    @Column(value = "dim_key")
    private String dimKey;

    /** 维度值集合，用@分割,开头结尾都有@<br/>Column: [dim_value] */
    @Column(value = "dim_value")
    private String dimValue;

    /** 查询md5值<br/>Column: [query_md5] */
    @Column(value = "query_md5")
    private String queryMd5;

    /** 查询json<br/>Column: [query_json] */
    @Column(value = "query_json")
    private String queryJson;

    /** 版本数据变动原因<br/>Column: [change_result] */
    @Column(value = "change_result")
    private String changeResult;

}