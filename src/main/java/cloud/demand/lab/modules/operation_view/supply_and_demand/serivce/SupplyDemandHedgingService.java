package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingOverviewResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingResp;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/26 14:00
 */
public interface SupplyDemandHedgingService {

    List<SupplyDemandHedgingResp> queryReport(SupplyDemandHedgingReq req);

    List<String> queryParams(SupplyDemandParamTypeReq req);

    ResponseEntity<InputStreamResource> exportInventory(SupplyDemandHedgingReq req);

    List<SupplyDemandHedgingOverviewResp> queryInventoryOverview(SupplyDemandHedgingReq req);
}
