package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.apache.commons.lang3.StringUtils;

/**
 * 设备类型内存策略表
 */
@Data
@ToString
@Table("bas_device_memory")
public class BasDeviceMemoryDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /**
     * 规划产品<br/>Column: [plan_product_name]
     */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 设备型号<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 内存（PB)<br/>Column: [memory] */
    @Column(value = "memory")
    private BigDecimal memory;

    public BigDecimal getTbSaleMemory() {
        return this.getMemory();
    }

    public BigDecimal getGbSaleMemory() {
        return SoeCommonUtils.multiply(this.getTbSaleMemory(), new BigDecimal("1024"));
    }
}