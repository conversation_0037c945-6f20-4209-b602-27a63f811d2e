package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.PplAndOrderAdjustVersionDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.VersionData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.VersionReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasVersionDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/13 11:20
 */
@Service
public class SupplyAndDemandDictServiceImpl implements SupplyAndDemandDictService {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public boolean doDefinitive(VersionReq req) {
        //'demand','supply','inventory'
        List<BasVersionDO> list = demandDBHelper.getAll(BasVersionDO.class, " where version_code in (?) and type in ('demand','supply','inventory') and product_category = ? ",
                req.getVersionCode(), req.getProductCategory());
        if (ListUtils.isEmpty(list)) {
            return false;
        }
        for (BasVersionDO versionDO : list) {
            versionDO.setDefinitive(BooleanUtils.isNotFalse(req.getDefinitive()));
            demandDBHelper.update(versionDO);
            PplAndOrderAdjustVersionDO adjustVersionDO = demandDBHelper.getOne(PplAndOrderAdjustVersionDO.class, " where version_code = ? and product_category = ? ", versionDO.getVersionCode(), versionDO.getProductCategory());
            if (Objects.nonNull(adjustVersionDO)) {
                adjustVersionDO.setDefinitive(BooleanUtils.isNotFalse(req.getDefinitive()));
                demandDBHelper.update(adjustVersionDO);
            }
        }
        return true;
    }

    @Override
    public boolean checkDefinitive(String versionCode, String type, String productCategory) {
        BasVersionDO versionDO = getOne(versionCode, type, productCategory);
        if (Objects.isNull(versionDO)) {
            return false;
        }
        return versionDO.isDefinitive();
    }

    @Override
    public void insert(String versionCode, String startYearMonth, String endYearMonth, String type, String productCategory) {
        BasVersionDO versionDO = getOne(versionCode, type, productCategory);
        if (Objects.nonNull(versionDO)) {
            if (StringUtils.compare(versionDO.getStartMonth(), startYearMonth) > 0) {
                versionDO.setStartMonth(startYearMonth);
            }
            if (StringUtils.compare(versionDO.getEndMonth(), endYearMonth) < 0) {
                versionDO.setEndMonth(endYearMonth);
            }
            demandDBHelper.update(versionDO);
            return;
        }
        versionDO = new BasVersionDO();
        versionDO.setVersionCode(versionCode);
        versionDO.setStartMonth(startYearMonth);
        versionDO.setEndMonth(endYearMonth);
        versionDO.setType(type);
        versionDO.setProductCategory(productCategory);
        demandDBHelper.insert(versionDO);
    }

    @Override
    public List<VersionData> getAllVersion(VersionReq req) {
        List<BasVersionDO> ret = ListUtils.newArrayList();
        if (Boolean.TRUE.equals(req.getDefinitive())) {
            ret.addAll(demandDBHelper.getAll(BasVersionDO.class, " where definitive = 1 and type = ? and product_category = ? order by version_code desc ", req.getType(), req.getProductCategory()));
        } else {
            ret.addAll(demandDBHelper.getAll(BasVersionDO.class, " where type = ? and product_category = ? order by version_code desc ", req.getType(), req.getProductCategory()));
        }
        return ListUtils.transform(ret, VersionData::transform);
    }

    private BasVersionDO getOne(String versionCode, String type, String productCategory) {
        return demandDBHelper.getOne(BasVersionDO.class, " where version_code=? and type = ? and product_category = ? ", versionCode, type, productCategory);
    }

    @Override
    public <T> void addUnclassified(T entity) {
        List<String> unclassified = ListUtils.newArrayList("未分类", "未知", Constant.EMPTY_VALUE_STR, "");
        Class clazz = entity.getClass();
        while (clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (List.class.isAssignableFrom(field.getType())) {
                    try {
                        Object value = field.get(entity);
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        List<String> list = (List) value;
                        if (ListUtils.isNotEmpty(list) && ListUtils.contains(list, item -> unclassified.contains(item))) {
                            list.addAll(unclassified);
                            list = list.stream().distinct().collect(Collectors.toList());
                            field.set(entity, list);
                        }
                    } catch (IllegalAccessException | IllegalArgumentException e) {
                        throw new BizException("反射异常");
                    }

                }
            }
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 1. 如果本周有定稿的版本，则返回定稿的版本
     * 2. 如果本周没有定稿的版本，则返回小于等于当前版本的最大定稿版本
     * @param req
     * @return
     */
    @Override
    public String getDefinitiveVersion(VersionReq req) {
        String versionCode = req.getVersionCode();
        if(checkDefinitive(versionCode, req.getType(), req.getProductCategory())){
            return versionCode;
        }
        // 获取本周日（结束日期）
        LocalDate endOfWeek =  LocalDate.parse(versionCode, DateTimeFormatter.ISO_LOCAL_DATE).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        return demandDBHelper.getRawOne(String.class, "select version_code from bas_supply_and_demand_version " +
                "where definitive = 1 and type = ? and product_category = ? and version_code <= ? " +
                "order by version_code desc limit 1", req.getType(), req.getProductCategory(), endOfWeek.format(DateTimeFormatter.ISO_LOCAL_DATE));
    }
}
