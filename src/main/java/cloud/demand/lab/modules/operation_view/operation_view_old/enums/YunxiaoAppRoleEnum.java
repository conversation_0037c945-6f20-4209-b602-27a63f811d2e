package cloud.demand.lab.modules.operation_view.operation_view_old.enums;

import com.pugwoo.wooutils.string.StringTools;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum YunxiaoAppRoleEnum {

    CVM("CVM", "CVM"),

    EMR("EMR", "EMR"),

    EKS("EKS", "EKS"),
    ;

    final private String code;
    final private String name;

    YunxiaoAppRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunxiaoAppRoleEnum getByCode(String code) {
        for (YunxiaoAppRoleEnum e : YunxiaoAppRoleEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoAppRoleEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }


}