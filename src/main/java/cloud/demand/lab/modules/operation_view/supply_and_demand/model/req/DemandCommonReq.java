package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DemandCommonReq {

    private List<String> statTimeList;

    private List<String> demandType;


    private String productCategory = "CVM";

    private List<String> product;

    private List<String> industryDept;

    private List<String> warZone;

    private List<String> commonCustomerShortName;

    private List<String> customerUin;

    private List<String> zoneCategory;

    private List<String> instanceCategory;

    private List<String> instanceGroup;

    private List<String> instanceType;

    private List<String> customhouseTitle;

    private List<String> countryName;

    private List<String> areaName;

    private List<String> regionName;

    private List<String> zoneName;


    private List<String> projectName;

    private List<String> demandScene;

    private String orderRange;

    private List<String> volumeType;//云盘类型：SSD、高性能

    private List<String> diskType;//磁盘类型：系统盘、数据盘

    private List<String> dbStorageType;//数据库存储类型：通用版、云盘版

    private List<String> dbDeployType;//数据库部署类型：通用型、独享型

    private List<String> dbFrameworkType;//数据库框架类型

    private List<String> isInner;//内外部：0-外部 1-内部

    private List<String> dims;

    private String demandCaliber = "人工校准";//人工校准、共识需求

    private String queryRange = "服务用量";//计费用量、服务用量

    private boolean needScale = true;

    private boolean excludeFinishDemand = true;

    @NotBlank(message = "单位不能为空")
    @Pattern(regexp = "台|逻辑核|卡|逻辑容量|逻辑内存量",message = "单位只能为台|逻辑核|卡|逻辑容量|逻辑内存量")
    private String unit = "逻辑核";

    private List<String> projectType;

    private boolean excel = false;

    public List<String> getScaleStatTime(){
        return ListUtils.newArrayList();
    }

}
