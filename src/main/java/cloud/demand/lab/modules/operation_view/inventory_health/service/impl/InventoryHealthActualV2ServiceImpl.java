package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.CBSDeliveryData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.InstanceTypeCombineDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.LeisureBusyDataDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.QueryInvDetailTypesReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ServiceLevelDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ServiceLevelDataDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ServiceLevelSummaryDataDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualRangeReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualRangeResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp.Item;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.QueryServiceLevelReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.yunxiao.YunxiaoGridItemVO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ActualDeliverySlaDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.ActualDeliverySlaDetailDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CBSDeliverySlaDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthMonthDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsInventoryHealthWeekDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsLeisureAndBusySoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryReasonDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryDisassembleService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthActualV2Service;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.OutsideHealthService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryTurnoverServiceImpl.ActualInventoryDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.ActualInventoryListReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.impl.OperationViewServiceImpl;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.base.Splitter;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Array;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class InventoryHealthActualV2ServiceImpl implements InventoryHealthActualV2Service {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    RedisHelper redisHelper;
    @Resource
    InventoryHealthDictService inventoryHealthDictService;
    @Resource
    DBHelper ckcubesDBHelper;
    @Resource
    OutsideHealthService outsideService;

    @Resource
    OperationViewService2Impl operationViewService2;

    @Resource
    DictService dictService;

    @Resource
    CvmPlanService cvmPlanService;

    @Resource
    OutsideViewOldService outsideViewOldService;

    @Resource
    InventoryDisassembleService inventoryDisassembleService;

    private final ExecutorService threadPool = Executors.newWorkStealingPool();

    private final ExecutorService newThreadPool = Executors.newFixedThreadPool(10);

    @Override
    public InventoryHealthActualResp queryInventoryHealthActualV2(InventoryHealthActualReq req) {
        if (req.getSoldType() == null) {
            req.setSoldType("全时段");
        }
        switch (req.getTimeDimension()) {
            case "日切片":
                return queryInventoryHealthActualByDay(req);
            case "周日均":
                return queryInventoryHealthActualByWeekAvg(req);
            case "月日均":
                return queryInventoryHealthActualByMonthAvg(req);
            default:
                throw BizException.makeThrow("选择的时间维度必须是日切片、周日均、月日均三者之一");
        }
    }

    @Override
    public FileNameAndBytesDTO exportDeliveryDetailExcel(TrendGraphReq req) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/delivery-detail.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        TrendGraphServiceImpl trendGraphService = SpringUtil.getBean(TrendGraphServiceImpl.class);
        String instanceTypeName = trendGraphService.getInstanceTypeNameForExport(req);
        String zoneName = trendGraphService.getZoneNameForExport(req);
        String date = req.getDate().toString();

        String fileNamePrefix = "库存健康-交付明细(" + instanceTypeName + "-" + zoneName + "-" + date + ")";
        // 从数据库查询需要的数据
        List<String> campus = trendGraphService.getCampusByZoneName(req.getZoneName());
        List<String> device = trendGraphService.getDeviceTypeByInstanceType(req.getInstanceType());
        // 获取节假周数据
        //  1、获取历史指定时间区间范围的节假周信息
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(req.getDate(), -13);
        //  获取第一周周一，作为beginDate
        String beginDate = holidayWeekInfo.get(0).getStartDate();
        //  获取最后一周周日，作为endDate
        String lastDate = holidayWeekInfo.get(holidayWeekInfo.size() - 1).getEndDate();

        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/demand_market_delivery_sla_detail_excel.sql");
        List<ActualDeliverySlaDetailDO> data = ckcubesDBHelper.getRaw(ActualDeliverySlaDetailDO.class, sql, date, beginDate, lastDate, campus, device);
        // 填充可用区，机型数据
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, StaticZoneDO> campus2ZoneInfMap = dictService.getCampus2ZoneInfoMap();
        Map<String, String> deviceType2InstanceTypeMap = dictService.getCsigDeviceTypeToInstanceTypeMap();
        OperationViewServiceImpl operationViewService = SpringUtil.getBean(OperationViewServiceImpl.class);

        data.stream().forEach(item -> {
            StaticZoneDO zoneInfo = campus2ZoneInfMap.get(item.getCampus());

            if (zoneInfo != null) {
                item.setRegionType(zoneInfo.getCustomhouseTitle());
                item.setAreaName(zoneInfo.getAreaName());
                item.setRegionName(zoneInfo.getRegionName());
                item.setZoneName(zoneInfo.getZoneName());
            }

            if (item.getXyCreateTime().contains("1970-01-01")) {
                item.setXyApprovalDays(BigDecimal.ZERO);
            }

            item.setInstanceType(deviceType2InstanceTypeMap.get(item.getQuotaDeviceClass()));
            item.setSla(operationViewService.getSLA(item.getQuotaDeviceClass(), item.getRegionType().equals("境内")));

            // 设置 SLA
            if (item.getDeliveryStatus().equals("如期交付")) {
                item.setErpDeliveryDays(NumberUtils.min(item.getDeliveryDays(), BigDecimal.valueOf(item.getSla())));
            } else {
                item.setErpDeliveryDays(item.getDeliveryDays());
            }

            item.setTotalDeliveryDays(item.getErpDeliveryDays().add(item.getXyApprovalDays()));
        });

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    /**
     * CBS交付周期
     */
    @Override
    public List<CBSDeliveryData> getAllCBSDeliveryData(String statTime, int spanNum) {
        List<CBSDeliveryData> result = new ArrayList<>();
        LocalDate date = DateUtils.parseLocalDate(statTime);
        List<HolidayWeekInfoDTO> holidayWeekInfo = inventoryHealthDictService.getHolidayWeekInfoBase(date, spanNum);
        if (ListUtils.isEmpty(holidayWeekInfo)) {
            return result;
        }
        //  获取第一周周一，作为beginDate
        String beginDate = holidayWeekInfo.get(0).getStartDate();
        //  获取最后一周周日，作为endDate
        String lastDate = holidayWeekInfo.get(holidayWeekInfo.size() - 1).getEndDate();

        // 获取交付周期数据
        String sql = ORMUtils.getSql("/sql/operation_view/inventory_health/safety_inventory/cbs_delivery_sla_data.sql");
        List<CBSDeliverySlaDO> deliverySlaDOS = ckcubesDBHelper.getRaw(CBSDeliverySlaDO.class, sql, statTime, beginDate, lastDate);
        Map<String, StaticZoneDO> campus2ZoneInfoMap = dictService.getCampus2ZoneInfoMap();
        Map<String, String> zoneToCountryMap = dictService.getZoneToCountryMap();
        result = deliverySlaDOS.stream().map(o -> {
            CBSDeliveryData data = o.transform();
            data.setStatTime(statTime);
            // 设置节假周信息
            LocalDateTime erqActualDate = o.getErpActualDate();
            for (HolidayWeekInfoDTO info : holidayWeekInfo) {
                LocalDateTime weekStartDate = DateUtils.parseLocalDateTime(info.getStartDate());
                LocalDateTime weekEndDate = DateUtils.parseLocalDateTime(info.getEndDate());

                if ((erqActualDate.isAfter(weekStartDate) || erqActualDate.isEqual(weekStartDate)) && (erqActualDate.isBefore(weekEndDate) || erqActualDate.isEqual(weekEndDate))) {
                    data.setHolidayYear(info.getYear());
                    data.setHolidayMonth(info.getMonth());
                    data.setHolidayWeek(info.getWeek());
                    data.setHolidayWeekStartDate(info.getStartDate());
                    data.setHolidayWeekEndDate(info.getEndDate());
                    data.setWeekIndex(info.getWeekNFromNow());
                    break;
                }
            }

            //设置volumeType
            if (data.getDeviceFamily() != null) {
                if (data.getDeviceFamily().equals("云下存储CBS SSD") || data.getDeviceFamily().equals("云下存储CBS极速")) {
                    data.setVolumeType("SSD");
                }else if (data.getDeviceFamily().equals("云下存储CBS高效")) {
                    data.setDeviceFamily("高性能");
                }
            }
            //设置地理信息
            String campus = data.getCampus();
            StaticZoneDO zoneInfo = campus2ZoneInfoMap.get(campus);

            if (zoneInfo != null) {
                data.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
                data.setAreaName(zoneInfo.getAreaName());
                data.setRegionName(zoneInfo.getRegionName());
                data.setZoneName(zoneInfo.getZoneName());
            }
            if (StringUtils.isNotBlank(data.getZoneName())) {
                data.setCountry(zoneToCountryMap.get(data.getZoneName()));
            }
            if (data.getDeliveryStatus().equals("如期交付")) {
                int configuredSla = SpringUtil.getBean(OperationViewServiceImpl.class).getSLA(data.getDeviceType(), data.getCustomhouseTitle().equals("境内"));
                data.setDeliveryDays(NumberUtils.min(data.getDeliveryDays(), BigDecimal.valueOf(configuredSla)));
                data.setSla(data.getDeliveryDays().add(data.getXyApprovalDays()));
            }
            return data;
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<String> queryInvDetailTypes(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct inv_detail_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        if (ListUtils.isNotEmpty(req.getLineType())) {
            whereContent.addAnd("line_type in (?)", req.getLineType());
        }

        if (ListUtils.isNotEmpty(req.getMaterialType())) {
            whereContent.addAnd("material_type in (?)", req.getMaterialType());
        }

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    @Override
    public List<String> queryLineType(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct line_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    @Override
    public List<String> queryMaterialType(QueryInvDetailTypesReq req) {
        String statTime = req.getDate() == null ? DateUtils.yesterday().toString() : req.getDate();
        String sql = "select distinct material_type from cloud_demand.dws_actual_inventory_df";

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);

        if (ListUtils.isNotEmpty(req.getLineType())) {
            whereContent.addAnd("line_type in (?)", req.getLineType());
        }

        sql = sql + whereContent.getSql();

        return ckcldDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

    @Override
    public InventoryHealthActualRangeResp queryFullExcelData(InventoryHealthActualRangeReq req) {
        Date startDate = DateUtils.parse(req.getStartDate());
        Date endDate = DateUtils.parse(req.getEndDate());
        List<Item> data = new ArrayList<>();
        while(!startDate.after(endDate)) {
            String date = DateUtils.formatDate(startDate);
            req.setDate(date);
            InventoryHealthActualResp tempResp = getActualAndSafeInvByDay(req);
            List<InventoryHealthActualResp.Item> respData = tempResp.getData();
            if (ListUtils.isNotEmpty(respData)) {
                respData.forEach(o -> o.setDate(date));
                data.addAll(respData);
            }
            startDate = DateUtils.addTime(startDate, Calendar.DATE, 1);
        }
        InventoryHealthActualRangeResp resp = new InventoryHealthActualRangeResp();
        resp.setData(data);
        return resp;
    }

    @Override
    public List<ServiceLevelDataDTO> queryServiceLevelData(QueryServiceLevelReq req) {
        Set<String> countries = new HashSet<>();
        boolean containChina = true;
        if (ListUtils.isNotEmpty(req.getCountry())) {
            countries.addAll(req.getCountry());
            if (!countries.contains("中国内地")) {
                containChina = false;
            }
        }
        WhereSQL apiCondition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            apiCondition.and("instance_family in (?)", req.getInstanceType());
        }
        apiCondition.and("version = ?", req.getStatDate().replace("-", ""));
        List<ServiceLevelDTO> apiData = ckcldDBHelper.getAll(ServiceLevelDTO.class, apiCondition.getSQL(),
                apiCondition.getParams());
        Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> ret = new HashMap<>();
        for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
            String zone = entry.getKey();
            String region = entry.getValue();
            SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
            if (regionDO != null) {
                ret.put(zone, regionDO.getCountryName());
            }
        }
        apiData.forEach(o -> o.setCountry(ret.get(o.getZoneName())));
        apiData = apiData.stream().filter(o -> !StringUtils.isBlank(o.getCountry())).collect(Collectors.toList());
        if (!countries.isEmpty()) {
            apiData = apiData.stream().filter(o -> countries.contains(o.getCountry()) &&
                    !o.getCountry().equals("中国内地")).collect(Collectors.toList());
        }else {
            apiData = apiData.stream().filter(o -> !o.getCountry().equals("中国内地")).collect(Collectors.toList());
        }
        Map<String, List<ServiceLevelDTO>> apiList = ListUtils.toMapList(apiData,
                o -> String.join("@", o.getCountry(), o.getInstanceFamily()), o -> o);
        List<ServiceLevelDataDTO> result = new ArrayList<>();
        if (req.getSoldType().equals("全时段")) {
            for (Entry<String, List<ServiceLevelDTO>> entry : apiList.entrySet()) {
                ServiceLevelDataDTO item = new ServiceLevelDataDTO();
                List<ServiceLevelDTO> value = entry.getValue();
                item.setCountry(value.get(0).getCountry());
                item.setInstanceType(value.get(0).getInstanceFamily());
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(value, ServiceLevelDTO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(value, ServiceLevelDTO::getApiSucTotal);
                Map<String, List<ServiceLevelDTO>> valueMap = ListUtils.toMapList(value,
                        ServiceLevelDTO::getZoneName, o -> o);
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                for (Entry<String, List<ServiceLevelDTO>> valueEntry : valueMap.entrySet()) {
                    soldTotal = soldTotal.add(valueEntry.getValue().get(0).getSoldTotal() == null ?
                            BigDecimal.ZERO : valueEntry.getValue().get(0).getSoldTotal());
                    soldOut = soldOut.add(valueEntry.getValue().get(0).getSoldOutTotal() == null ?
                            BigDecimal.ZERO : valueEntry.getValue().get(0).getSoldOutTotal());
                }
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldOutTotal(soldOut);
                item.setSoldTotal(soldTotal);
                item.setServiceLevel(slap);
                result.add(item);
            }
        }else {
            WhereSQL condition = new WhereSQL();
            if (ListUtils.isNotEmpty(req.getInstanceType())) {
                condition.and("instance_type in (?)", req.getInstanceType());
            }
            condition.and("imp_date = ?", req.getStatDate().replace("-", ""));
            List<LeisureBusyDataDTO> soldData = ckcldDBHelper.getAll(LeisureBusyDataDTO.class,
                    condition.getSQL(),
                    condition.getParams());
            soldData.forEach(o -> o.setCountry(ret.get(o.getZoneName())));
            if (!countries.isEmpty()) {
                soldData = soldData.stream().filter(o -> countries.contains(o.getCountry()) && !o.getCountry().equals("中国内地")).collect(Collectors.toList());
            }else {
                soldData = soldData.stream().filter(o -> !o.getCountry().equals("中国内地")).collect(Collectors.toList());
            }
            Map<String, List<LeisureBusyDataDTO>> soldList = ListUtils.toMapList(soldData,
                    o -> String.join("@", o.getCountry(), o.getInstanceType()), o -> o);
            Set<String> totalKeys = new HashSet<>();
            if (ListUtils.isNotEmpty(apiList.keySet())) {
                totalKeys.addAll(apiList.keySet());
            }
            if (ListUtils.isNotEmpty(soldList.keySet())) {
                totalKeys.addAll(soldList.keySet());
            }
            for (String key : totalKeys) {
                List<ServiceLevelDTO> api = apiList.get(key);
                List<LeisureBusyDataDTO> sold = soldList.get(key);
                ServiceLevelDataDTO item = new ServiceLevelDataDTO();
                if (ListUtils.isNotEmpty(api)) {
                    item.setInstanceType(api.get(0).getInstanceFamily());
                    item.setCountry(api.get(0).getCountry());
                }else if (ListUtils.isNotEmpty(sold)) {
                    item.setInstanceType(sold.get(0).getInstanceType());
                    item.setCountry(sold.get(0).getCountry());
                }
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal apiTotal = NumberUtils.sum(api, ServiceLevelDTO::getApiTotal);
                BigDecimal apiSuc = NumberUtils.sum(api, ServiceLevelDTO::getApiSucTotal);
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                if (req.getSoldType().equals("闲时")) {
                    soldOut = NumberUtils.sum(sold, LeisureBusyDataDTO::getLeisureSoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, LeisureBusyDataDTO::getLeisureSoldTotal);
                }else if (req.getSoldType().equals("忙时")) {
                    soldOut = NumberUtils.sum(sold, LeisureBusyDataDTO::getBusySoldOutTotal);
                    soldTotal = NumberUtils.sum(sold, LeisureBusyDataDTO::getBusySoldTotal);
                }
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldTotal(soldTotal);
                item.setSoldOutTotal(soldOut);
                result.add(item);
            }
        }

        if (containChina) {
            InventoryHealthActualReq req1 = new InventoryHealthActualReq();
            req1.setInstanceType(req.getInstanceType());
            req1.setSoldType(req.getSoldType());
            req1.setTimeDimension("日切片");
            req1.setDate(req.getStatDate());
            req1.setCustomhouseTitle(Arrays.asList("境内"));
            req1.setCustomerCustomGroup("ALL");
            req1.setIsIncludeReserved(false);
            req1.setIsIgnoreReserved(false);
            InventoryHealthActualResp resp = queryInventoryHealthActualV2(req1);
            List<Item> data = resp.getData();
            Map<String, List<Item>> mapList = ListUtils.toMapList(data, o -> o.getInstanceType(), o -> o);
            for (Entry<String, List<Item>> entry : mapList.entrySet()) {
                List<Item> value = entry.getValue();
                ServiceLevelDataDTO item = new ServiceLevelDataDTO();
                item.setInstanceType(entry.getKey());
                item.setCountry("中国内地");
                BigDecimal slap = BigDecimal.valueOf(0.7);
                BigDecimal soldRt = BigDecimal.ZERO;
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                BigDecimal apiTotal = BigDecimal.ZERO;
                BigDecimal apiSuc = BigDecimal.ZERO;
                for (Item temp : value) {
                    List<DwsCloudServerLevelDO> actualSla = temp.getActualSla();
                    if (ListUtils.isNotEmpty(actualSla)) {
                        soldTotal = soldTotal.add(actualSla.get(0).getSoldTotal());
                        soldOut = soldOut.add(actualSla.get(0).getSoldOutTotal());
                        apiTotal = apiTotal.add(NumberUtils.sum(actualSla, DwsCloudServerLevelDO::getApiTotal));
                        apiSuc = apiSuc.add(NumberUtils.sum(actualSla, DwsCloudServerLevelDO::getApiSucTotal));
                    }
                }
                if (apiTotal.intValue() > 0) {
                    slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.7));
                }
                if (soldTotal.intValue() > 0) {
                    soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
                }
                slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
                item.setServiceLevel(slap);
                item.setApiTotal(apiTotal);
                item.setApiSucTotal(apiSuc);
                item.setSoldTotal(soldTotal);
                item.setSoldOutTotal(soldOut);
                result.add(item);
            }
        }
        return result;
    }

    @Override
    public List<ServiceLevelSummaryDataDTO> queryServiceLevelSummaryData(QueryServiceLevelReq req) {
        List<ServiceLevelSummaryDataDTO> result = new ArrayList<>();
        List<ServiceLevelDataDTO> originData = queryServiceLevelData(req);
        //按照国家聚合
        Map<String, List<ServiceLevelDataDTO>> zoneMap = ListUtils.toMapList(originData,
                ServiceLevelDataDTO::getCountry, o -> o);
        for (Entry<String, List<ServiceLevelDataDTO>> entry : zoneMap.entrySet()) {
            List<ServiceLevelDataDTO> value = entry.getValue();
            ServiceLevelSummaryDataDTO item = new ServiceLevelSummaryDataDTO();
            item.setSummaryType("country");
            item.setSummaryTag(entry.getKey());
            setInfoToServiceLevel(item, value);
            result.add(item);
        }

        //按照机型聚合
        Map<String, List<ServiceLevelDataDTO>> instanceData = ListUtils.toMapList(originData,
                ServiceLevelDataDTO::getInstanceType, o -> o);
        for (Entry<String, List<ServiceLevelDataDTO>> entry : instanceData.entrySet()) {
            List<ServiceLevelDataDTO> value = entry.getValue();
            ServiceLevelSummaryDataDTO item = new ServiceLevelSummaryDataDTO();
            item.setSummaryType("instanceType");
            item.setSummaryTag(entry.getKey());
            setInfoToServiceLevel(item, value);
            result.add(item);
        }
        //所有数据聚合
        ServiceLevelSummaryDataDTO item = new ServiceLevelSummaryDataDTO();
        item.setSummaryType("all");
        item.setSummaryTag("all");
        setInfoToServiceLevel(item, originData);
        result.add(item);
        return result;
    }

    @Override
    public InventoryHealthTrendResp queryInventoryHealthTrend(InventoryHealthTrendReq req) {
        //转化时间范围，提供每个单位的第一天
        Map<String, String> dateMap = transformRangeToMap(req.getTimeDimension(), req.getStart(), req.getEnd());
        //从原有库存健康页面获取数据
        log.info("method:queryInventoryHealthTrend 开始获取库存健康数据");
        List<Item> items = getBasicInventoryHealthData(req, dateMap);
        log.info("method:queryInventoryHealthTrend  获取的数据 item为：" + items);
        //通过机型族过滤数据
        //根据机型族过滤数据
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            items = items.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        //根据国家过滤数据
        if (ListUtils.isNotEmpty(req.getCountry())) {
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
            Map<String, String> ret = new HashMap<>();
            for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
                String zone = entry.getKey();
                String region = entry.getValue();
                SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
                if (regionDO != null) {
                    ret.put(zone, regionDO.getCountryName());
                }
            }
            items = items.stream().filter(o -> {
                String country = ret.get(o.getZoneName());
                if (country == null) {
                    if (o.getCustomhouseTitle().equals("境内")) {
                        country = "中国内地";
                    }
                }
                if (country != null) {
                    return req.getCountry().contains(country);
                }
                return false;
            }).collect(
                    Collectors.toList());
        }
        log.info("method:queryInventoryHealthTrend  过滤数据后, item为" + items);
        //处理原有数据，生成库存健康趋势数据
        InventoryHealthTrendResp resp = new InventoryHealthTrendResp();
        Map<String, List<Item>> itemMap = new HashMap<>();
        switch(req.getExpandField()) {
            case "zoneName":
                itemMap = ListUtils.toMapList(items, o -> String.join("@", o.getInstanceType(), o.getZoneName(),o.getDate()), o-> o);
                break;
            case "areaName":
                itemMap = ListUtils.toMapList(items, o -> String.join("@", o.getInstanceType(), o.getAreaName(), o.getDate()), o-> o);
                break;
            case "regionName":
                itemMap = ListUtils.toMapList(items, o -> String.join("@", o.getInstanceType(), o.getRegionName(), o.getDate()), o -> o);
                break;
        }
        List<InventoryHealthTrendResp.Item> resultItems = new ArrayList<>();
        for (Entry<String, List<Item>> entry : itemMap.entrySet()) {
            List<Item> value = entry.getValue();
            InventoryHealthTrendResp.Item item = new InventoryHealthTrendResp.Item();
            String[] split = entry.getKey().split("@");
            if (split.length > 2) {
                item.setExtendField(split[1]);
                item.setDate(split[2]);
            }
            item.setInstanceType(value.get(0).getInstanceType());
            item.setDeliveryAvg(NumberUtils.avg(value.stream().map(Item::getDeliveryAvg).collect(Collectors.toList()), 2));
            item.setServiceLevel(NumberUtils.avg(value.stream().map(Item::getServiceLevel).collect(Collectors.toList()), 4));
            item.setBufferServiceLevel(NumberUtils.avg(value.stream().map(Item::getBufferServiceLevel).collect(Collectors.toList()), 4));
            item.setServiceLevelFactor(NumberUtils.avg(value.stream().map(Item::getServiceLevelFactor).collect(Collectors.toList()), 4));
            item.setBufferServiveLevelFactor(NumberUtils.avg(value.stream().map(Item::getBufferServiveLevelFactor).collect(Collectors.toList()), 4));
            item.setPrePaidSafetyInventoryCore(NumberUtils.sum(value, Item::getPrePaidSafetyInventoryCore).intValue());
            item.setBufferAverageCore(NumberUtils.sum(value, Item::getBufferAverageCore).intValue());
            item.setBufferSafetyInventoryCore(NumberUtils.sum(value, Item::getBufferSafetyInventoryCore));
            item.setSafeInvManualConfig(NumberUtils.sum(value, Item::getSafeInvManualConfig));
            item.setSafetyInventoryCore(NumberUtils.sum(value, Item::getSafetyInventoryCore).intValue());
            item.setTurnoverInv(NumberUtils.sum(value, Item::getTurnoverInv));
            item.setTurnoverWeekReservedAvgCore(NumberUtils.sum(value, Item::getTurnoverWeekReservedAvgCore));
            item.setTurnoverWeekPeakCore(NumberUtils.sum(value, Item::getTurnoverWeekPeakCore));
            item.setActualInventoryCore(NumberUtils.sum(value, Item::getActualInventoryCore).intValue());
            item.setPreDeductInventoryCore(NumberUtils.sum(value, Item::getPreDeductInventoryCore).intValue());
            if (item.getSafetyInventoryCore() + item.getTurnoverInv().intValue() == 0) {
                item.setHealthRatio(BigDecimal.ZERO);
            }else {
                item.setHealthRatio(NumberUtils.divide(item.getActualInventoryCore(), item.getSafetyInventoryCore() + item.getTurnoverInv().intValue(),2));
            }
            List<DwsCloudServerLevelDO> actualSla = new ArrayList<>();
            for (Item v : value) {
                if (ListUtils.isNotEmpty(v.getActualSla())) {
                    actualSla.addAll(v.getActualSla());
                }
            }
            item.setActualSla(actualSla);
            item.setBufferAverageStartDate(value.get(0).getBufferAverageStartDate());
            item.setBufferAverageEndDate(value.get(0).getBufferAverageEndDate());
            resultItems.add(item);
        }
        resp.setData(resultItems);
        List<String> invDetailType = req.getInvDetailType();
        if (ListUtils.isEmpty(invDetailType) || invDetailType.contains("用户预扣")) {
            resp.getData().forEach(o -> o.setIsContainPreDeduct(true));
        }else {
            resp.getData().forEach(o -> o.setIsContainPreDeduct(false));
        }
        log.info("method:queryInventoryHealthTrend  最终结果为：" + resp);
        return resp;
    }

    private List<Item> getBasicInventoryHealthData(InventoryHealthTrendReq req, Map<String, String> dateMap) {
        List<Item> result = new ArrayList<>();
        switch (req.getTimeDimension()) {
            case "day":
                result = queryInventoryHealthTrendByDay(req, dateMap);
                break;
            case "week":
                result = queryInventoryHealthTrendByWeek(req, dateMap);
                break;
            case "month":
                result = queryInventoryHealthTrendByMonth(req, dateMap);
                break;
        }
        if (ListUtils.isEmpty(req.getInstanceType())) {
            List<Item> tempResult = new ArrayList<>();
            Map<String, List<Item>> mapList = ListUtils.toMapList(result,
                    o -> String.join("@", o.getDate(), o.getZoneName()), o -> o);
            for (Entry<String, List<Item>> entry : mapList.entrySet()) {
                Item item = new Item();
                List<Item> value = entry.getValue();
                item.setInstanceType(null);
                item.setDate(value.get(0).getDate());
                item.setZoneName(value.get(0).getZoneName());
                item.setAreaName(value.get(0).getAreaName());
                item.setRegionName(value.get(0).getRegionName());
                item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
                item.setDeliveryAvg(NumberUtils.avg(value.stream().map(Item::getDeliveryAvg).collect(Collectors.toList()), 2));
                item.setServiceLevel(NumberUtils.avg(value.stream().map(Item::getServiceLevel).collect(Collectors.toList()), 4));
                item.setBufferServiceLevel(NumberUtils.avg(value.stream().map(Item::getBufferServiceLevel).collect(Collectors.toList()), 4));
                item.setServiceLevelFactor(NumberUtils.avg(value.stream().map(Item::getServiceLevelFactor).collect(Collectors.toList()), 4));
                item.setBufferServiveLevelFactor(NumberUtils.avg(value.stream().map(Item::getBufferServiveLevelFactor).collect(Collectors.toList()), 4));
                item.setPrePaidSafetyInventoryCore(NumberUtils.sum(value, Item::getPrePaidSafetyInventoryCore).intValue());
                item.setBufferAverageCore(NumberUtils.sum(value, Item::getBufferAverageCore).intValue());
                item.setBufferSafetyInventoryCore(NumberUtils.sum(value, Item::getBufferSafetyInventoryCore));
                item.setSafeInvManualConfig(NumberUtils.sum(value, Item::getSafeInvManualConfig));
                item.setSafetyInventoryCore(NumberUtils.sum(value, Item::getSafetyInventoryCore).intValue());
                item.setTurnoverInv(NumberUtils.sum(value, Item::getTurnoverInv));
                item.setTurnoverWeekReservedAvgCore(NumberUtils.sum(value, Item::getTurnoverWeekReservedAvgCore));
                item.setTurnoverWeekPeakCore(NumberUtils.sum(value, Item::getTurnoverWeekPeakCore));
                item.setActualInventoryCore(NumberUtils.sum(value, Item::getActualInventoryCore).intValue());
                item.setPreDeductInventoryCore(NumberUtils.sum(value, Item::getPreDeductInventoryCore).intValue());
                if (item.getSafetyInventoryCore() + item.getTurnoverInv().intValue() == 0) {
                    item.setHealthRatio(BigDecimal.ZERO);
                }else {
                    item.setHealthRatio(NumberUtils.divide(item.getActualInventoryCore(), item.getSafetyInventoryCore() + item.getTurnoverInv().intValue(),2));
                }
                List<DwsCloudServerLevelDO> actualSla = new ArrayList<>();
                for (Item v : value) {
                    if (ListUtils.isNotEmpty(v.getActualSla())) {
                        actualSla.addAll(v.getActualSla());
                    }
                }
                item.setActualSla(actualSla);
                item.setBufferAverageStartDate(value.get(0).getBufferAverageStartDate());
                item.setBufferAverageEndDate(value.get(0).getBufferAverageEndDate());
                tempResult.add(item);
            }
            return tempResult;
        }

        return result;
    }

    private List<Item> queryInventoryHealthTrendByMonth(InventoryHealthTrendReq req, Map<String, String> dateMap) {
        List<Future<List<Item>>> futures = new ArrayList<>();
        for (Entry<String, String> entry : dateMap.entrySet()) {
            String start = entry.getValue();
            LocalDate yesterday = LocalDate.now().minusDays(1);
            LocalDate nextMonth = DateUtils.parseLocalDate(start).plusMonths(1);
            LocalDate endDate = LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), 1).minusDays(1);
            if (endDate.isAfter(yesterday)) {
                endDate = yesterday;
            }
            String end = DateUtils.formatDate(endDate);
            Future<List<Item>> submit = threadPool.submit(
                    () -> queryInventoryHealthTrendByMonthSingle(req, start, end, entry.getKey()));
            futures.add(submit);
        }
        List<Item> result = new ArrayList<>();
        try {
            for (Future<List<Item>> future : futures) {
                List<Item> items = future.get();
                if (ListUtils.isNotEmpty(items)) {
                    result.addAll(items);
                }
            }
        } catch (InterruptedException  | ExecutionException e) {
            throw new RuntimeException("线程池执行失败, message : " + e.getMessage());
        }
        return result;
    }

    public List<Item> queryInventoryHealthTrendByMonthSingle(InventoryHealthTrendReq req, String start, String end, String yearMonth) {
        WhereSQL condition = req.genCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCondition = bean.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        WhereSQL healthCondition = condition.copy();
        healthCondition.and("year_month = ?", yearMonth);
        healthCondition.and("is_include_reserved = ?", !req.getIsIncludeReserved() ? 0 : 1);
        healthCondition.and("customer_custom_group = ?", req.getCustomerCustomGroup());
        List<DwsInventoryHealthMonthDataDO> all = ckcldDBHelper.getAll(DwsInventoryHealthMonthDataDO.class,
                healthCondition.getSQL(), healthCondition.getParams());
        Set<String> zoneNames = new HashSet<>();
        Set<String> ins = new HashSet<>();
        for (DwsInventoryHealthMonthDataDO temp : all) {
            zoneNames.add(temp.getZoneName());
            ins.add(temp.getInstanceType());
        }
        Map<String, DwsInventoryHealthMonthDataDO> healthMap = ListUtils.toMap(all,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(), o.getYearMonth()), o -> o);
        //实际库存
        if(ListUtils.isNotEmpty(req.getLineType())) {
            condition.and("line_type in (?)", req.getLineType());
        }
        if (ListUtils.isNotEmpty(req.getMaterialType())) {
            condition.and("material_type in (?)", req.getMaterialType());
        }
        if (ListUtils.isNotEmpty(req.getInvDetailType())) {
            condition.and("inv_detail_type in (?)", req.getInvDetailType());
        }
        condition.and("stat_time between ? and ?", start, end);
        String field = "select stat_time, zone_name, instance_type, inv_detail_type, sum(actual_inv) as sum";
        String groupBy = "stat_time, zone_name, instance_type, inv_detail_type";
        condition.addGroupBy(groupBy);
        String sql = field + " from dws_actual_inventory_df" + condition.getSQL();
        List<ActualInventoryDO> actAll = ckcldDBHelper.getRaw(ActualInventoryDO.class, sql,
                condition.getParams());
        List<ActualInventoryDO> deductAll = actAll.stream().filter(o -> o.getInvDetailType().equals("用户预扣"))
                .collect(Collectors.toList());
        Map<String, List<ActualInventoryDO>> actMap = ListUtils.toMapList(actAll,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(),
                        changeDateToYearMonth(o.getStatTime())), o -> o);
        Map<String, List<ActualInventoryDO>> deductMap = ListUtils.toMapList(deductAll,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(),
                        changeDateToYearMonth(o.getStatTime())), o -> o);
        // sla
        String start1 = DateUtils.format(DateUtils.parse(start), "yyyyMMdd");
        String end1 = DateUtils.format(DateUtils.parseLocalDate(end), "yyyyMMdd");
        List<DwsCloudServerLevelDO> slaAll = new ArrayList<>();
        if (ListUtils.isNotEmpty(all)) {
            WhereSQL slaCondition = new WhereSQL();
            slaCondition.and("version between ? and ?", start1, end1);
            if (ListUtils.isNotEmpty(zoneNames)) {
                slaCondition.and("zone_name in (?)", new ArrayList<>(zoneNames));
            }
            if (ListUtils.isNotEmpty(ins)) {
                slaCondition.and("instance_family in (?)", new ArrayList<>(ins));
            }
            slaAll = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, slaCondition.getSQL(), slaCondition.getParams());
            if (!req.getSoldType().equals("全时段")) {
                WhereSQL soldCondition = new WhereSQL();
                soldCondition.and("imp_date between ? and ?", start1, end1);
                if (ListUtils.isNotEmpty(zoneNames)) {
                    soldCondition.and("zone_name in (?)", new ArrayList<>(zoneNames));
                }
                if (ListUtils.isNotEmpty(ins)) {
                    soldCondition.and("instance_type in (?)", new ArrayList<>(ins));
                }
                List<DwsLeisureAndBusySoldOutDataDfDO> soldAll = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class, soldCondition.getSQL(), soldCondition.getParams());
                Map<String, List<DwsCloudServerLevelDO>> slaMap = ListUtils.toMapList(slaAll,
                        o -> String.join("@", o.getZoneName(), o.getInstanceFamily(), o.getVersion()), o -> o);
                Map<String, DwsLeisureAndBusySoldOutDataDfDO> soldMap = ListUtils.toMap(soldAll,
                        o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getImpDate()), o -> o);
                Set<String> serviceSet = new HashSet<>();
                if (ListUtils.isNotEmpty(slaMap.keySet())) {
                    serviceSet.addAll(slaMap.keySet());
                }
                if (ListUtils.isNotEmpty(soldMap.keySet())) {
                    serviceSet.addAll(soldMap.keySet());
                }
                List<DwsCloudServerLevelDO> temp = new ArrayList<>();
                if (req.getSoldType().equals("忙时")) {
                    for (String key : serviceSet) {
                        List<DwsCloudServerLevelDO> dwsCloudServerLevelDOS = slaMap.get(key);
                        DwsLeisureAndBusySoldOutDataDfDO soldOut = soldMap.get(key);
                        if (ListUtils.isEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                            dwsCloudServerLevelDO.setVersion(soldOut.getImpDate());
                            dwsCloudServerLevelDO.setInstanceFamily(soldOut.getInstanceType());
                            dwsCloudServerLevelDO.setZoneName(soldOut.getZoneName());
                            dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setCustomerType("(空值)");
                            dwsCloudServerLevelDO.setSoldTotal(soldOut.getBusySoldTotal());
                            dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                            temp.add(dwsCloudServerLevelDO);
                        }else if (ListUtils.isNotEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            for (DwsCloudServerLevelDO service : dwsCloudServerLevelDOS) {
                                service.setSoldTotal(soldOut.getBusySoldTotal());
                                service.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                            }
                        }
                    }
                } else if (req.getSoldType().equals("闲时")) {
                    for (String key : serviceSet) {
                        List<DwsCloudServerLevelDO> dwsCloudServerLevelDOS = slaMap.get(key);
                        DwsLeisureAndBusySoldOutDataDfDO soldOut = soldMap.get(key);
                        if (ListUtils.isEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                            dwsCloudServerLevelDO.setVersion(soldOut.getImpDate());
                            dwsCloudServerLevelDO.setInstanceFamily(soldOut.getInstanceType());
                            dwsCloudServerLevelDO.setZoneName(soldOut.getZoneName());
                            dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setCustomerType("(空值)");
                            dwsCloudServerLevelDO.setSoldTotal(soldOut.getLeisureSoldTotal());
                            dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                            temp.add(dwsCloudServerLevelDO);
                        }else if (ListUtils.isNotEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            for (DwsCloudServerLevelDO service : dwsCloudServerLevelDOS) {
                                service.setSoldTotal(soldOut.getLeisureSoldTotal());
                                service.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                            }
                        }
                    }
                }
                if (ListUtils.isNotEmpty(temp)) {
                    slaAll.addAll(temp);
                }
            }
        }
        //将实际库存，sla以及对应的库存健康数据聚合
        Map<String, List<DwsCloudServerLevelDO>> slaMap = ListUtils.toMapList(slaAll,
                o -> String.join("@", o.getInstanceFamily(), o.getZoneName(),
                        changeDateToYearMonth(o.getVersion())), o -> o);
        List<Item> result = new ArrayList<>();
        for (String key : healthMap.keySet()) {
            DwsInventoryHealthMonthDataDO health = healthMap.get(key);
            if (health == null) {
                continue;
            }
            List<DwsCloudServerLevelDO> sla = slaMap.get(key);
            List<ActualInventoryDO> actualInventoryDOS = actMap.get(key);
            List<BigDecimal> actual = new ArrayList<>();
            if (ListUtils.isNotEmpty(actualInventoryDOS)) {
                actual = actualInventoryDOS.stream().map(ActualInventoryDO::getActualInv)
                        .collect(Collectors.toList());
            }
            List<BigDecimal> preDeduct = new ArrayList<>();
            List<ActualInventoryDO> deductInventory = deductMap.get(key);
            if (ListUtils.isNotEmpty(deductInventory)) {
                preDeduct = deductInventory.stream().map(ActualInventoryDO::getActualInv)
                        .collect(Collectors.toList());
            }
            Item item = new Item();
            item.setActualSla(sla);
            String[] split = key.split("@");
            if (split.length > 2) {
                item.setInstanceType(split[0]);
                item.setZoneName(split[1]);
                item.setDate(split[2]);
            }
            item.setAreaName(health.getAreaName());
            item.setRegionName(health.getRegionName());
            item.setCustomhouseTitle(health.getCustomhouseTitle());
            YearMonth parse = YearMonth.parse(health.getYearMonth());
            LocalDate startTime = parse.atDay(1);
            LocalDate endTime = parse.atEndOfMonth();
            LocalDate yest = LocalDate.now().minusDays(1);
            if (endTime.isAfter(yest)) {
                endTime = yest;
            }
            int count = 0;
            while(!startTime.isAfter(endTime)) {
                count++;
                startTime = startTime.plusDays(1);
            }
            if (count != 0) {
                item.setActualInventoryCore(NumberUtils.sum(actual).divide(BigDecimal.valueOf(count), 2,
                        RoundingMode.HALF_UP).intValue());
                item.setPreDeductInventoryCore(NumberUtils.sum(preDeduct).divide(BigDecimal.valueOf(count), 2,
                        RoundingMode.HALF_UP).intValue());
            }else {
                item.setActualInventoryCore(0);
                item.setPreDeductInventoryCore(0);
            }
            item.setDeliveryAvg(health.getDeliveryAvg());
            item.setServiceLevel(health.getServiceLevel());
            item.setBufferServiceLevel(health.getBufferServiceLevel());
            item.setServiceLevelFactor(health.getServiceLevelFactor());
            item.setBufferServiveLevelFactor(health.getBufferServiceLevelFactor());
            item.setPrePaidSafetyInventoryCore(health.getPrePaidSafetyInventoryCore());
            item.setBufferAverageCore(health.getBufferAverageCore());
            item.setBufferSafetyInventoryCore(health.getBufferSafetyInventoryCore());
            item.setSafeInvManualConfig(health.getSafeInvManualConfig());
            item.setSafetyInventoryCore(health.getSafetyInventoryCore());
            item.setTurnoverInv(health.getTurnoverInv());
            item.setTurnoverWeekReservedAvgCore(health.getTurnoverWeekReservedAvgCore());
            item.setTurnoverWeekPeakCore(health.getTurnoverWeekPeakCore());
            if (!DateUtils.formatDate(health.getBufferAvgStartDate()).equals("1997-01-01")) {
                item.setBufferAverageStartDate(DateUtils.formatDate(health.getBufferAvgStartDate()));
            }
            if (!DateUtils.formatDate(health.getBufferAvgEndDate()).equals("1997-01-01")) {
                item.setBufferAverageEndDate(DateUtils.formatDate(health.getBufferAvgEndDate()));
            }
            result.add(item);
        }
        return result;
    }

    private List<Item>  queryInventoryHealthTrendByWeek(InventoryHealthTrendReq req, Map<String, String> dateMap) {
        WhereSQL condition = req.genCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL categoryCondition = bean.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        condition.and(categoryCondition);
        WhereSQL healthCondition = condition.copy();
        healthCondition.and("year_week in (?)", dateMap.keySet());
        healthCondition.and("is_include_reserved = ?", !req.getIsIncludeReserved() ? 0 : 1);
        healthCondition.and("customer_custom_group = ?", req.getCustomerCustomGroup());
        List<DwsInventoryHealthWeekDataDO> all = ckcldDBHelper.getAll(DwsInventoryHealthWeekDataDO.class,
                healthCondition.getSQL(), healthCondition.getParams());
        Set<String> zoneNames = new HashSet<>();
        Set<String> ins = new HashSet<>();
        for (DwsInventoryHealthWeekDataDO temp : all) {
            zoneNames.add(temp.getZoneName());
            ins.add(temp.getInstanceType());
        }
        Map<String, DwsInventoryHealthWeekDataDO> healthMap = ListUtils.toMap(all,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(), o.getYearWeek()), o -> o);
        List<String> collect = new ArrayList<>(dateMap.values());
        collect.sort(String::compareTo);
        String start = collect.get(0);
        DictServiceImpl dictBean = SpringUtil.getBean(DictServiceImpl.class);
        ResPlanHolidayWeekDO aDo = dictBean.getHolidayWeekInfoByDate(collect.get(collect.size() - 1));
        LocalDate endDate = DateUtils.parseLocalDate(aDo.getEnd());
        LocalDate yesterday = LocalDate.now().minusDays(1);
        if (endDate.isAfter(yesterday)) {
            endDate = yesterday;
        }
        String end = DateUtils.formatDate(endDate);
        //实际库存
        if(ListUtils.isNotEmpty(req.getLineType())) {
            condition.and("line_type in (?)", req.getLineType());
        }
        if (ListUtils.isNotEmpty(req.getMaterialType())) {
            condition.and("material_type in (?)", req.getMaterialType());
        }
        if (ListUtils.isNotEmpty(req.getInvDetailType())) {
            condition.and("inv_detail_type in (?)", req.getInvDetailType());
        }
        condition.and("stat_time between ? and ?", start, end);
        String field = "select stat_time, zone_name, instance_type, inv_detail_type, sum(actual_inv) as sum";
        String groupBy = "stat_time, zone_name, instance_type, inv_detail_type";
        condition.addGroupBy(groupBy);
        String sql = field + " from dws_actual_inventory_df" + condition.getSQL();
        List<ActualInventoryDO> actAll = ckcldDBHelper.getRaw(ActualInventoryDO.class, sql,
                condition.getParams());
        List<ActualInventoryDO> deductAll = actAll.stream().filter(o -> o.getInvDetailType().equals("用户预扣"))
                .collect(Collectors.toList());
        List<ResPlanHolidayWeekDO> weekInfo = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        Map<String, List<ActualInventoryDO>> actMap = ListUtils.toMapList(actAll,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(),
                        changeDateToYearWeek(o.getStatTime(), weekInfo)), o -> o);
        Map<String, List<ActualInventoryDO>> deductMap = ListUtils.toMapList(deductAll,
                o -> String.join("@", o.getInstanceType(), o.getZoneName(),
                        changeDateToYearWeek(o.getStatTime(), weekInfo)), o -> o);
        // sla
        start = DateUtils.format(DateUtils.parse(collect.get(0)), "yyyyMMdd");
        end = DateUtils.format(endDate, "yyyyMMdd");
        List<DwsCloudServerLevelDO> slaAll = new ArrayList<>();
        WhereSQL slaCondition = new WhereSQL();
        slaCondition.and("version between ? and ?", start, end);
        if (ListUtils.isNotEmpty(zoneNames)) {
            slaCondition.and("zone_name in (?)", new ArrayList<>(zoneNames));
        }
        if (ListUtils.isNotEmpty(ins)) {
            slaCondition.and("instance_family in (?)", new ArrayList<>(ins));
        }
        if (ListUtils.isNotEmpty(all)) {
            slaAll = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, slaCondition.getSQL(), slaCondition.getParams());
            if (!req.getSoldType().equals("全时段")) {
                WhereSQL soldCondition = new WhereSQL();
                soldCondition.and("imp_date between ? and ?", start, end);
                if (ListUtils.isNotEmpty(zoneNames)) {
                    soldCondition.and("zone_name in (?)", new ArrayList<>(zoneNames));
                }
                if (ListUtils.isNotEmpty(ins)) {
                    soldCondition.and("instance_type in (?)", new ArrayList<>(ins));
                }
                List<DwsLeisureAndBusySoldOutDataDfDO> soldAll = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class, soldCondition.getSQL(), soldCondition.getParams());
                Map<String, List<DwsCloudServerLevelDO>> slaMap = ListUtils.toMapList(slaAll,
                        o -> String.join("@", o.getZoneName(), o.getInstanceFamily(), o.getVersion()), o -> o);
                Map<String, DwsLeisureAndBusySoldOutDataDfDO> soldMap = ListUtils.toMap(soldAll,
                        o -> String.join("@", o.getZoneName(), o.getInstanceType(), o.getImpDate()), o -> o);
                Set<String> serviceSet = new HashSet<>();
                if (ListUtils.isNotEmpty(slaMap.keySet())) {
                    serviceSet.addAll(slaMap.keySet());
                }
                if (ListUtils.isNotEmpty(soldMap.keySet())) {
                    serviceSet.addAll(soldMap.keySet());
                }
                List<DwsCloudServerLevelDO> temp = new ArrayList<>();
                if (req.getSoldType().equals("忙时")) {
                    for (String key : serviceSet) {
                        List<DwsCloudServerLevelDO> dwsCloudServerLevelDOS = slaMap.get(key);
                        DwsLeisureAndBusySoldOutDataDfDO soldOut = soldMap.get(key);
                        if (ListUtils.isEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                            dwsCloudServerLevelDO.setVersion(soldOut.getImpDate());
                            dwsCloudServerLevelDO.setInstanceFamily(soldOut.getInstanceType());
                            dwsCloudServerLevelDO.setZoneName(soldOut.getZoneName());
                            dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setCustomerType("(空值)");
                            dwsCloudServerLevelDO.setSoldTotal(soldOut.getBusySoldTotal());
                            dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                            temp.add(dwsCloudServerLevelDO);
                        }else if (ListUtils.isNotEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            for (DwsCloudServerLevelDO service : dwsCloudServerLevelDOS) {
                                service.setSoldTotal(soldOut.getBusySoldTotal());
                                service.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                            }
                        }
                    }
                } else if (req.getSoldType().equals("闲时")) {
                    for (String key : serviceSet) {
                        List<DwsCloudServerLevelDO> dwsCloudServerLevelDOS = slaMap.get(key);
                        DwsLeisureAndBusySoldOutDataDfDO soldOut = soldMap.get(key);
                        if (ListUtils.isEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                            dwsCloudServerLevelDO.setVersion(soldOut.getImpDate());
                            dwsCloudServerLevelDO.setInstanceFamily(soldOut.getInstanceType());
                            dwsCloudServerLevelDO.setZoneName(soldOut.getZoneName());
                            dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                            dwsCloudServerLevelDO.setCustomerType("(空值)");
                            dwsCloudServerLevelDO.setSoldTotal(soldOut.getLeisureSoldTotal());
                            dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                            temp.add(dwsCloudServerLevelDO);
                        }else if (ListUtils.isNotEmpty(dwsCloudServerLevelDOS) && soldOut != null) {
                            for (DwsCloudServerLevelDO service : dwsCloudServerLevelDOS) {
                                service.setSoldTotal(soldOut.getLeisureSoldTotal());
                                service.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                            }
                        }
                    }
                }
                if (ListUtils.isNotEmpty(temp)) {
                    slaAll.addAll(temp);
                }
            }
        }
        //将实际库存，sla以及对应的库存健康数据聚合
        Map<String, List<DwsCloudServerLevelDO>> slaMap = ListUtils.toMapList(slaAll,
                o -> String.join("@", o.getInstanceFamily(), o.getZoneName(),
                        changeDateToYearWeek(o.getVersion(), weekInfo)), o -> o);
        List<Item> result = new ArrayList<>();
        for (String key : healthMap.keySet()) {
            DwsInventoryHealthWeekDataDO health = healthMap.get(key);
            if (health == null) {
                continue;
            }
            List<DwsCloudServerLevelDO> sla = slaMap.get(key);
            List<BigDecimal> actual = new ArrayList<>();
            List<ActualInventoryDO> actualInventoryDOS = actMap.get(key);
            if (ListUtils.isNotEmpty(actualInventoryDOS)) {
                actual = actualInventoryDOS.stream().map(ActualInventoryDO::getActualInv)
                        .collect(Collectors.toList());
            }
            List<BigDecimal> preDeduct = new ArrayList<>();
            List<ActualInventoryDO> deductInventory = deductMap.get(key);
            if (ListUtils.isNotEmpty(deductInventory)) {
                preDeduct = deductInventory.stream().map(ActualInventoryDO::getActualInv)
                        .collect(Collectors.toList());
            }
            Item item = new Item();
            item.setActualSla(sla);
            String yearWeek = health.getYearWeek();
            String[] weekSplit = yearWeek.split("W");
            ResPlanHolidayWeekDO holidayWeekInfoByYearWeek = dictService.getHolidayWeekInfoByYearWeek(
                    Integer.parseInt(weekSplit[0]), Integer.parseInt(weekSplit[1]));
            LocalDate startTime = DateUtils.parseLocalDate(holidayWeekInfoByYearWeek.getStart());
            LocalDate temp = startTime;
            LocalDate endTime = DateUtils.parseLocalDate(holidayWeekInfoByYearWeek.getEnd());
            LocalDate yest = LocalDate.now().minusDays(1);
            if (endTime.isAfter(yest)) {
                endTime = yest;
            }
            int count = 0;
            while(!startTime.isAfter(endTime)) {
                count++;
                startTime = startTime.plusDays(1);
            }
            if (count != 0) {
                item.setActualInventoryCore(NumberUtils.sum(actual).divide(BigDecimal.valueOf(count), 2,
                        RoundingMode.HALF_UP).intValue());
                item.setPreDeductInventoryCore(NumberUtils.sum(preDeduct).divide(BigDecimal.valueOf(count), 2,
                        RoundingMode.HALF_UP).intValue());
            }else {
                item.setActualInventoryCore(0);
                item.setPreDeductInventoryCore(0);
            }
            String[] split = key.split("@");
            if (split.length > 2) {
                item.setInstanceType(split[0]);
                item.setZoneName(split[1]);
            }
            item.setDate(temp.getYear() + "W" + temp.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            item.setAreaName(health.getAreaName());
            item.setRegionName(health.getRegionName());
            item.setCustomhouseTitle(health.getCustomhouseTitle());
            item.setDeliveryAvg(health.getDeliveryAvg());
            item.setServiceLevel(health.getServiceLevel());
            item.setBufferServiceLevel(health.getBufferServiceLevel());
            item.setServiceLevelFactor(health.getServiceLevelFactor());
            item.setBufferServiveLevelFactor(health.getBufferServiceLevelFactor());
            item.setPrePaidSafetyInventoryCore(health.getPrePaidSafetyInventoryCore());
            item.setBufferAverageCore(health.getBufferAverageCore());
            item.setBufferSafetyInventoryCore(health.getBufferSafetyInventoryCore());
            item.setSafeInvManualConfig(health.getSafeInvManualConfig());
            item.setSafetyInventoryCore(health.getSafetyInventoryCore());
            item.setTurnoverInv(health.getTurnoverInv());
            item.setTurnoverWeekReservedAvgCore(health.getTurnoverWeekReservedAvgCore());
            item.setTurnoverWeekPeakCore(health.getTurnoverWeekPeakCore());
            if (!DateUtils.formatDate(health.getBufferAvgStartDate()).equals("1997-01-01")) {
                item.setBufferAverageStartDate(DateUtils.formatDate(health.getBufferAvgStartDate()));
            }
            if (!DateUtils.formatDate(health.getBufferAvgEndDate()).equals("1997-01-01")) {
                item.setBufferAverageEndDate(DateUtils.formatDate(health.getBufferAvgEndDate()));
            }
            result.add(item);
        }

        return result;
    }

    private List<Item> queryInventoryHealthTrendByDay(InventoryHealthTrendReq req, Map<String, String> dateMap) {
        Map<String, Future<InventoryHealthActualResp>> futures = new HashMap<>();
        log.info("method:queryInventoryHealthTrendByDay开始获取数据");
        for (Entry<String, String> entry : dateMap.entrySet()) {
            InventoryHealthActualReq monthReq = req.genActualReq(entry.getValue(), req, null);
            Future<InventoryHealthActualResp> submit = newThreadPool.submit(
                    () -> queryInventoryHealthActualByDay(monthReq));
            futures.put(entry.getKey(), submit);
        }
        List<Item> result = new ArrayList<>();
        try {
            for (Entry<String, Future<InventoryHealthActualResp>> entry : futures.entrySet()) {
                Future<InventoryHealthActualResp> value = entry.getValue();
                InventoryHealthActualResp resp = value.get();
                resp.getData().forEach(o -> o.setDate(entry.getKey()));
                if (ListUtils.isNotEmpty(resp.getData())) {
                    result.addAll(resp.getData());
                }

            }
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException("线程执行失败，message: " + e.getMessage());
        }
        log.info("method:queryInventoryHealthTrendByDay开始获取数据成功" + result);
        return result;

    }

    public String changeDateToYearWeek(String dateStr, List<ResPlanHolidayWeekDO> all) {
        if (dateStr.length() == 8) {
            dateStr = DateUtils.formatDate(DateUtils.parse(dateStr, "yyyyMMdd"));
        }
        String finalDateStr = dateStr;
        List<ResPlanHolidayWeekDO> filter =
                ListUtils.filter(all,
                        o -> o != null && o.getStart().compareTo(finalDateStr) <= 0 && o.getEnd().compareTo(
                                finalDateStr) >= 0);
        if (filter.size() != 1) {
            throw new RuntimeException("节假周信息有误，请查看");
        }
        ResPlanHolidayWeekDO week = filter.get(0);
        return week.getYear() + "W" + week.getWeek();
    }

    public String changeDateToYearMonth(String dateStr) {
        if (dateStr.length() == 8) {
            dateStr = DateUtils.formatDate(DateUtils.parse(dateStr, "yyyyMMdd"));
        }
        LocalDate localDate = DateUtils.parseLocalDate(dateStr);
        YearMonth yearMonth = YearMonth.of(localDate.getYear(), localDate.getMonthValue());
        return yearMonth.toString();
    }


    public Map<String, String> transformRangeToMap(String timeDimension, String start, String end) {
        Map<String, String> result = new HashMap<>();
        switch(timeDimension) {
            case "day":
                result = genDayMap(start, end);
                break;
            case "week":
                result = genWeekMap(start, end);
                break;
            case "month":
                result = genMonthMap(start, end);
                break;
        }
        return result;
    }

    private Map<String, String> genMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        YearMonth startMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
        YearMonth endMonth = YearMonth.of(endDate.getYear(), endDate.getMonthValue());
        while(!startMonth.isAfter(endMonth)) {
            result.put(startMonth.toString(), DateUtils.formatDate(startMonth.atDay(1)));
            startMonth = startMonth.plusMonths(1);
        }
        return result;
    }

    private Map<String, String> genWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        DictServiceImpl bean = SpringUtil.getBean(DictServiceImpl.class);
        while(!startDate.isAfter(endDate)) {
            ResPlanHolidayWeekDO temp = bean.getHolidayWeekInfoByDate(DateUtils.formatDate(startDate));
            result.put(temp.getYear() + "W" + temp.getWeek(), temp.getStart());
            startDate = startDate.plusDays(7);
        }
        return result;
    }

    private Map<String, String> genDayMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(DateUtils.formatDate(startDate), DateUtils.formatDate(startDate));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public void setInfoToServiceLevel(ServiceLevelSummaryDataDTO item, List<ServiceLevelDataDTO> value) {
        BigDecimal apiTotal = NumberUtils.sum(value, ServiceLevelDataDTO::getApiTotal);
        BigDecimal apiSuc = NumberUtils.sum(value, ServiceLevelDataDTO::getApiSucTotal);
        BigDecimal soldTotal = NumberUtils.sum(value, ServiceLevelDataDTO::getSoldTotal);
        BigDecimal soldOut = NumberUtils.sum(value, ServiceLevelDataDTO::getSoldOutTotal);
        BigDecimal slap = BigDecimal.valueOf(0.5);
        BigDecimal soldRt = BigDecimal.ZERO;
        if (apiTotal.intValue() > 0) {
            slap = apiSuc.divide(apiTotal, 4, BigDecimal.ROUND_UP).multiply(BigDecimal.valueOf(0.5));
        }
        if (soldTotal.intValue() > 0) {
            soldRt = soldOut.divide(soldTotal, 4, BigDecimal.ROUND_UP);
        }
        slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.5)));
        item.setApiTotal(apiTotal);
        item.setApiSucTotal(apiSuc);
        item.setSoldOutTotal(soldOut);
        item.setSoldTotal(soldTotal);
        item.setServiceLevel(slap);
    }

    /**
     * 在内存中筛选弹性用量，复用运营视图的筛选逻辑
     *
     * @param req
     * @return
     */
//    private List<BufferAverageCoreDTO> filterBufferAverageCore(InventoryHealthActualReq req) {
//        List<BufferAverageCoreDTO> bufferAverageCoreDTOS =
//                SpringUtil.getBean(JxcExternalServiceImpl.class).queryBufferCoreAverage(DateUtils.parse(req.getDate()));
//        // 复用新CVM运营视图的逻辑
//        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
//        //  对弹性规模在内存中筛选
//        OperationViewReq2 opReq = invReqToOpViewReq(req);
//        return operationViewService2.filterBufferPart(bufferAverageCoreDTOS, opReq, req.getDate());
//    }

    /**
     * 将安全库存的参数转换为库存健康的参数
     *
     * @param req
     * @return
     */
    private OperationViewReq2 invReqToOpViewReq(InventoryHealthActualReq req) {
        // 构造运营视图参数，复用运营视图逻辑
        OperationViewReq2 opReq = new OperationViewReq2();
        opReq.setZoneCategory(req.getZoneCategory());
        opReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        opReq.setInstanceType(req.getInstanceType());
        opReq.setCustomhouseTitle(req.getCustomhouseTitle());
        opReq.setAreaName(req.getAreaName());
        opReq.setRegionName(req.getRegionName());
        opReq.setZoneName(req.getZoneName());
        opReq.setIsCombine(req.getIsCombine());
        opReq.setMaterialType(req.getMaterialType());
        opReq.setCustomerCustomGroup(req.getCustomerCustomGroup());
        opReq.setLineType(req.getLineType());
        opReq.setDate(DateUtils.parse(req.getDate()));
        opReq.setCategoryDate(req.getCategoryDate());
        return opReq;
    }

    /**
     * 格式适配，将库存健康的安全库存数据转为运营视图的安全库存结构
     *
     * @param resp
     * @return
     */
    private OperationViewResp2 invRespToOpViewResp(InventoryHealthActualResp resp) {
        OperationViewResp2 opResp = new OperationViewResp2();
        opResp.setData(resp.getData().stream().map(item -> {
            // 安全库存信息此时还没生成，只转换实际存库数据
            OperationViewResp2.Item opItem = new OperationViewResp2.Item();
            opItem.setProductType("CVM"); // 固定 CVM
            opItem.setCustomhouseTitle(item.getCustomhouseTitle());
            opItem.setAreaName(item.getAreaName());
            opItem.setRegionName(item.getRegionName());
            opItem.setZoneName(item.getZoneName());
            opItem.setInstanceType(item.getInstanceType());

            //  总库存
            opItem.setInvTotalNum(BigDecimal.valueOf(item.getActualInventoryCore()));
            //  弹性服务水平/系数
            opItem.setBufferServiceLevel(item.getBufferServiceLevel());
            opItem.setBufferServiceLevelFactor(item.getBufferServiveLevelFactor());
            //  弹性规模日均值
            opItem.setBufferAverageCore(BigDecimal.valueOf(item.getBufferAverageCore()));
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            opItem.setBufferSafetyInv(item.getBufferSafetyInventoryCore());
            return opItem;
        }).collect(Collectors.toList()));

        return opResp;
    }

    private void getSafeInv(InventoryHealthActualReq invReq, InventoryHealthActualResp invResp) {
        // 复用新CVM运营视图的逻辑
        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
        // 根据用户设置的安全库存算法，构建安全库存
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        OperationViewReq2 req = invReqToOpViewReq(invReq);
        OperationViewResp2 resp = invRespToOpViewResp(invResp);

        switch (operationViewAlgorithm) {
            case "historyWeekPeak":
                operationViewService2.buildHistoryWeekPeak(req, resp);
                break;
            case "historyWeekDiff":
                operationViewService2.buildHistoryWeekDiff(req, resp);
                break;
            case "futureWeekPeak":
                operationViewService2.buildFutureWeekPeak(req, resp);
                break;
            case "historyWeekPeakForecastWN":
                operationViewService2.buildHistoryWeekPeakDemand(req, resp);
                break;
            default:
                throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
        }

        // 将运营视图计算的安全库存，填充到库存健康的响应中
        invResp.setData(ListUtils.merge(invResp.getData(), resp.getData(), invItem -> invItem.toKey(), opItem -> opItem.toKeyNoProduct(), (o1, o2) -> {
            if ((ListUtils.isNotEmpty(o1) && o1.size() != 1) || (ListUtils.isNotEmpty(o2) && o2.size() != 1)) {
                log.info("设置安全库存时，键值不唯一，库存：" + JSON.toJson(o1) + "运营视图：" + JSON.toJson(o2));
                throw BizException.makeThrow("内部错误，联系 brightwwu 处理");
            }
            // 如果填充一个空的 o1
            if (ListUtils.isEmpty(o1)) {
                InventoryHealthActualResp.Item invItem = new InventoryHealthActualResp.Item();
                invItem.setCustomhouseTitle(o2.get(0).getCustomhouseTitle());
                invItem.setAreaName(o2.get(0).getAreaName());
                invItem.setRegionName(o2.get(0).getRegionName());
                invItem.setZoneName(o2.get(0).getZoneName());
                invItem.setInstanceType(o2.get(0).getInstanceType());

                o1 = new ArrayList<>();
                o1.add(invItem);
            } else if (ListUtils.isEmpty(o2)) {
                return o1.get(0);
            }

            OperationViewResp2.SafetyInventoryResult safeInv = null;

            switch (operationViewAlgorithm) {
                case "historyWeekPeak":
                    safeInv = o2.get(0).getHistoryWeekPeak();

                    if (safeInv == null) {
                        // 如果安全库存为空，填充一个空安全库存数据
                        operationViewService2.fillNullHistoryWeekPeakSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekPeak();
                    }
                    break;
                case "historyWeekDiff":
                    safeInv = o2.get(0).getHistoryWeekDiff();

                    if (safeInv == null) {
                        operationViewService2.fillNullHistoryWeekDiffSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekDiff();
                    }
                    break;
                case "futureWeekPeak":
                    safeInv = o2.get(0).getFutureWeekPeak();

                    if (safeInv == null) {
                        operationViewService2.fillNullFutureWeekPeakSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getFutureWeekPeak();
                    }
                    break;
                case "historyWeekPeakForecastWN":
                    safeInv = o2.get(0).getHistoryWeekPeakForecastWN();

                    if (safeInv == null) {
                        operationViewService2.fillNullHistoryWeekPeakForecastWeekNSafetyInvItem(o2.get(0), invReq.getDate());
                        safeInv = o2.get(0).getHistoryWeekPeakForecastWN();
                    }
                    break;
                default:
                    throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
            }

            if (safeInv == null) {
                throw BizException.makeThrow("安全库存为空，联系 brightwwu 处理");
            }

            o1.get(0).setSla(safeInv.getSla());
            o1.get(0).setSafetyInventoryCore(safeInv.getSafetyInv().intValue());
            o1.get(0).setSafeInvManualConfig(safeInv.getSafeInvManualConfig());
            o1.get(0).setPrePaidSafetyInventoryCore(safeInv.getMonthlySafetyInv().intValue());
            o1.get(0).setServiceLevel(safeInv.getServiceLevel());
            o1.get(0).setServiceLevelFactor(safeInv.getServiceLevelFactor());
            o1.get(0).setSafetyInventoryCoreExpression(safeInv.getSafetyInventoryCoreExpression());

            if (safeInv.getMediumLongTailForecastDemandCore() != null) {
                o1.get(0).setForecastDemandCore(safeInv.getMediumLongTailForecastDemandCore().intValue());
            }

            o1.get(0).setDemandAvg(safeInv.getDemandAvg());
            o1.get(0).setStandardDiff(safeInv.getStandardDiff());
            o1.get(0).setMediumLongTailSafetyInv(safeInv.getMediumLongTailSafetyInv());
            o1.get(0).setHeadZlkhbSafetyInv(safeInv.getHeadZlkhbSafetyInv());
            o1.get(0).setHeadNotZlkhbSafetyInv(safeInv.getHeadNotZlkhbSafetyInv());
            o1.get(0).setForecastDemandAccuracyRate(safeInv.getForecastDemandAccuracyRate());
            o1.get(0).setDeliveryAvg(safeInv.getDeliveryAvg());
            o1.get(0).setDeliveryStandardDiff(safeInv.getDeliveryStandardDiff());

            if (safeInv.getTurnoverInv() == null) {
                if (safeInv.getForecastTurnOverInvMap() == null || safeInv.getForecastTurnOverInvMap().get("w0") == null) {
                    o1.get(0).setForecastTurnoverInv(BigDecimal.ZERO);
                    o1.get(0).setForecastTurnoverWeekPeakAvg12Core(BigDecimal.ZERO);
                    o1.get(0).setForecastTurnoverWeekDemandCore(BigDecimal.ZERO);
                } else {
                    // 取 W0 周预测
                    Map<String, BigDecimal> w0ForecastTurnoverInv = safeInv.getForecastTurnOverInvMap().get("w0");
                    o1.get(0).setForecastTurnoverInv(w0ForecastTurnoverInv.get("turnoverInv"));
                    o1.get(0).setForecastTurnoverWeekPeakAvg12Core(w0ForecastTurnoverInv.get("weekPeakCore"));
                    o1.get(0).setForecastTurnoverWeekDemandCore(w0ForecastTurnoverInv.get("avgForecastCore"));
                }
            } else {
                // 周转库存相关
                if (invReq.getIsIncludeReserved() == null || invReq.getIsIncludeReserved()) {
                    o1.get(0).setTurnoverInv(safeInv.getTurnoverInv());
                } else {
                    o1.get(0).setTurnoverInv(safeInv.getTurnoverInvWeekPeak());
                }
                o1.get(0).setTurnoverWeekPeakCore(safeInv.getTurnoverInvWeekPeak());
                o1.get(0).setTurnoverWeekReservedAvgCore(safeInv.getTurnoverInvReserved());
            }

            BigDecimal turnoverInv = o1.get(0).getTurnoverInv() == null ? o1.get(0).getForecastTurnoverInv() : o1.get(0).getTurnoverInv();
            turnoverInv = turnoverInv == null ? BigDecimal.ZERO : turnoverInv;

            if (o1.get(0).getActualInventoryCore() != null && o1.get(0).getSafetyInventoryCore() != null && o1.get(0).getSafetyInventoryCore() > 0) {
                // 冗余系数=实际库存 / (安全库存 + 周转库存)
                o1.get(0).setHealthRatio(
                        NumberUtils.divide(
                                o1.get(0).getActualInventoryCore(),
                                turnoverInv.add(
                                        BigDecimal.valueOf(o1.get(0).getSafetyInventoryCore())
                                ),
                                2
                        )
                );
            } else {
                o1.get(0).setHealthRatio(BigDecimal.ZERO);
            }

            return o1.get(0);
        }));
    }

//    public List<ActualInventoryDTO> getActualInv(WhereSQL condition, InventoryHealthActualReq req) {
//        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
//
//        // 填充通用的条件
//        operationViewService2.genBaseFieldsCondition(condition, req.getLineType(), req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), true, req.getDate());
//
//        List<ActualInventoryDTO> all = ckcldDBHelper.getAll(ActualInventoryDTO.class, condition.getSQL(), condition.getParams());
//
//        //  2、 过滤好差呆类型
//        //  补全线下库存部分的好差类型的字段
//        return operationViewService2.filterByMaterialType(
//                all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()),
//                req.getMaterialType()
//        ).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());
//    }

    private InventoryHealthActualResp constructResp(List<OperationViewResp2.Item> mergedItems) {
        InventoryHealthActualResp resp = new InventoryHealthActualResp();
        resp.setData(new ArrayList<>());

        for (OperationViewResp2.Item dto : mergedItems) {
            InventoryHealthActualResp.Item item = new InventoryHealthActualResp.Item();
            item.setCustomhouseTitle(dto.getCustomhouseTitle());
            item.setAreaName(dto.getAreaName());
            item.setRegionName(dto.getRegionName());
            item.setZoneName(dto.getZoneName());
            item.setInstanceType(dto.getInstanceType());

            // 设置弹性开始时间和弹性结束时间
            item.setBufferAverageStartDate(dto.getBufferAverageStartDate());
            item.setBufferAverageEndDate(dto.getBufferAverageEndDate());

            //  总库存
            item.setActualInventoryCore(dto.getInvTotalNum().intValue());
            item.setPreDeductInventoryCore(dto.getPreDeductInv().intValue());
            //  弹性服务水平/系数
            item.setBufferServiceLevel(dto.getBufferServiceLevel());
            item.setBufferServiveLevelFactor(dto.getBufferServiceLevelFactor());
            //  弹性规模日均值
            item.setBufferAverageCore(dto.getBufferAverageCore().intValue());
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            item.setBufferSafetyInventoryCore(dto.getBufferSafetyInv());
            item.setBufferRoi(dto.getBufferRoi());
            item.setBufferRate(dto.getBufferRate());

            resp.getData().add(item);
        }

        return resp;
    }

    private Future<Map<String, YunxiaoGridItemVO>> getYunxiaoGrid(WhereSQL condition, InventoryHealthActualReq req) {
        // 检查好料和库存类型，如果选了，必须包含好料和线上库存
        if (!ListUtils.isEmpty(req.getLineType()) && !req.getLineType().contains("线上库存")) {
            throw new BizException("选中剔除预扣选项时，库存类型必须包含线上库存，当前选中的库存类型为：" + req.getLineType());
        }
        if (!ListUtils.isEmpty(req.getMaterialType()) && !req.getMaterialType().contains("好料")) {
            throw new BizException("选中剔除预扣选项时，必须包含好料，当前选中了：" + req.getMaterialType());
        }

        return threadPool.submit(() -> {
            // 复用新CVM运营视图的逻辑
            OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
            //  针对机型类型、园区类型进行筛选
            WhereSQL categoryCondition = operationViewService2.genCategoryCondition(req.getZoneCategory(), req.getInstanceTypeCategory(), req.getIsCombine(), req.getDate(), req.getCustomhouseTitle());
            condition.and(categoryCondition);
            condition.addGroupBy("instance_type", "zone_name");

            List<YunxiaoGridItemVO> all = ckcldDBHelper.getAll(YunxiaoGridItemVO.class, condition.getSQL(), condition.getParams());
            return all.stream().collect(Collectors.toMap(o -> String.join(o.getZoneName(), "@", o.getInstanceType()), o -> o));
        });
    }
    /**
     * 获取某个日切片的实际库存和安全库存
     *
     * @param req
     * @return
     */
    private InventoryHealthActualResp getActualAndSafeInvByDay(InventoryHealthActualReq req) {
        WhereSQL condition = req.genCondition();
        // 查询预扣块信息信息，并按照机型、可用区分组
        Future<Map<String, YunxiaoGridItemVO>> gridItemMapFuture = null;

        if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
            gridItemMapFuture = getYunxiaoGrid(condition.copy(), req);
        }

        // 复用新CVM运营视图的逻辑
        OperationViewService2Impl operationViewService2 = SpringUtil.getBean(OperationViewService2Impl.class);
        ActualInventoryListReq params = new ActualInventoryListReq();
        params.setStatTime(req.getDate());
        params.setLineType(req.getLineType());
        params.setMaterialType(req.getMaterialType());
        params.setInvDetailType(req.getInvDetailType());
        params.setIsCombine(req.getIsCombine());
        params.setZoneCategory(req.getZoneCategory());
        params.setInstanceTypeCategory(req.getInstanceTypeCategory());
        params.setCategoryDate(req.getCategoryDate());
        params.setCustomhouseTitle(req.getCustomhouseTitle());
        // 获取实际库存和弹性备货配额，复用运营视图的逻辑
        List<DwsActualInventoryDfDO> all = operationViewService2.getActualInventoryList(params, condition);
        LocalDate date = LocalDate.parse(req.getDate());
        String monday = DateUtils.formatDate(date.with(DayOfWeek.MONDAY));
        params.setStatTime(monday);
        if (StringTools.isBlank(req.getCategoryDate())) {
            params.setCategoryDate(req.getDate());
        }
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = operationViewService2.getBufferSafeInventoryList(params, condition);
        // 3、找到最近1个月的弹性用量数据，计算出弹性用来的安全库存 X%(服务水平）*L(弹性量/天)
        //    L=“近”一个月的弹性用量“天”均数据
//        List<BufferAverageCoreDTO> bufferAverageCoreDTOS = filterBufferAverageCore(req);

        //  4、合并实际库存和弹性备货配额的结果，传给下游
//        all = operationViewService2.mergeData(
//                req.getDate(),
//                all.stream().map(item -> (ActualInventoryAvgDTO) item).collect(Collectors.toList()),
//                bufferAverageCoreDTOS
//        ).stream().map(item -> (ActualInventoryDTO) item).collect(Collectors.toList());
        List<OperationViewResp2.Item> mergedItems = operationViewService2.mergeActualBufferInventoryData(req.getDate(), all, bufferSafeInventoryDfDOS);

        // 5、构造实际库存接口返回
        InventoryHealthActualResp resp = constructResp(mergedItems);

        // 获取并填充安全库存，日切片取选中的日期
        getSafeInv(req, resp);

        ForecastViewServiceImpl forecastViewService = SpringUtil.getBean(ForecastViewServiceImpl.class);

        if (req.getIsCombine() != null && req.getIsCombine()) {
            //  查询机型组合的配置
            InstanceTypeCombineDTO instanceTypeCombineDTO = inventoryHealthDictService.queryAllCombineInstanceType();
            resp.setData(forecastViewService.combineItem(instanceTypeCombineDTO.getCombination(), resp.getData(), req.getInstanceTypeCategory(), req.getDate()));
            resp.setErrorMsg(StringTools.isNotBlank(instanceTypeCombineDTO.getErrorMsg()) ?
                    instanceTypeCombineDTO.getErrorMsg() : null);
        }
        // 真实服务水平
        Map<String, List<DwsCloudServerLevelDO>> m = forecastViewService.getSla(req.getDate());
        if (req.getSoldType().equals("忙时")) {
            Map<String, DwsLeisureAndBusySoldOutDataDfDO> leisureSoldOutData = getLeisureSoldOutData(req.getDate());
            for (Item item : resp.getData()) {
                String key = String.join("@", item.getZoneName(), item.getInstanceType());
                List<DwsCloudServerLevelDO> service = m.get(key);
                DwsLeisureAndBusySoldOutDataDfDO soldOut = leisureSoldOutData.get(key);
                if (ListUtils.isEmpty(service) && soldOut != null) {
                    List<DwsCloudServerLevelDO> temp = new ArrayList<>();
                    DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                    dwsCloudServerLevelDO.setVersion(req.getDate().replace("-",""));
                    dwsCloudServerLevelDO.setZoneName(item.getZoneName());
                    dwsCloudServerLevelDO.setInstanceFamily(item.getInstanceType());
                    //默认设置为包年包月
                    dwsCloudServerLevelDO.setDemandType("包年包月");
                    dwsCloudServerLevelDO.setCustomerType("头部");
                    dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                    dwsCloudServerLevelDO.setSoldTotal(soldOut.getBusySoldTotal());
                    dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                    dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                    if (dwsCloudServerLevelDO.getSoldTotal().intValue() > 0) {
                        BigDecimal divide = dwsCloudServerLevelDO.getSoldOutTotal()
                                .divide(dwsCloudServerLevelDO.getSoldTotal(), 3, RoundingMode.HALF_UP);
                        BigDecimal multiply = BigDecimal.valueOf(1).subtract(divide).multiply(BigDecimal.valueOf(0.3));
                        dwsCloudServerLevelDO.setSla(BigDecimal.valueOf(0.7).add(multiply));
                    }else {
                        dwsCloudServerLevelDO.setSla(BigDecimal.valueOf(1));
                    }
                    temp.add(dwsCloudServerLevelDO);
                    m.put(key, temp);
                }else if (ListUtils.isNotEmpty(service) && soldOut != null) {
                    for (DwsCloudServerLevelDO dwsCloudServerLevelDO : service) {
                        dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getBusySoldOutTotal());
                        dwsCloudServerLevelDO.setSoldTotal(soldOut.getBusySoldTotal());
                    }
                }
            }
        }else if (req.getSoldType().equals("闲时")) {
            Map<String, DwsLeisureAndBusySoldOutDataDfDO> leisureSoldOutData = getLeisureSoldOutData(req.getDate());
            for (Item item : resp.getData()) {
                String key = String.join("@", item.getZoneName(), item.getInstanceType());
                List<DwsCloudServerLevelDO> service = m.get(key);
                DwsLeisureAndBusySoldOutDataDfDO soldOut = leisureSoldOutData.get(key);
                if (ListUtils.isEmpty(service) && soldOut != null) {
                    List<DwsCloudServerLevelDO> temp = new ArrayList<>();
                    DwsCloudServerLevelDO dwsCloudServerLevelDO = new DwsCloudServerLevelDO();
                    dwsCloudServerLevelDO.setZoneName(item.getZoneName());
                    dwsCloudServerLevelDO.setInstanceFamily(item.getInstanceType());
                    //默认设置为包年包月与头部
                    dwsCloudServerLevelDO.setVersion(req.getDate().replace("-",""));
                    dwsCloudServerLevelDO.setDemandType("包年包月");
                    dwsCloudServerLevelDO.setCustomerType("头部");
                    dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                    dwsCloudServerLevelDO.setSoldTotal(soldOut.getLeisureSoldTotal());
                    dwsCloudServerLevelDO.setApiTotal(BigDecimal.ZERO);
                    dwsCloudServerLevelDO.setApiSucTotal(BigDecimal.ZERO);
                    if (dwsCloudServerLevelDO.getSoldTotal().intValue() > 0) {
                        BigDecimal divide = dwsCloudServerLevelDO.getSoldOutTotal()
                                .divide(dwsCloudServerLevelDO.getSoldTotal(), 3, RoundingMode.HALF_UP);
                        BigDecimal multiply = BigDecimal.valueOf(1).subtract(divide).multiply(BigDecimal.valueOf(0.3));
                        dwsCloudServerLevelDO.setSla(BigDecimal.valueOf(0.7).add(multiply));
                    }else {
                        dwsCloudServerLevelDO.setSla(BigDecimal.valueOf(1));
                    }
                    temp.add(dwsCloudServerLevelDO);
                    m.put(key, temp);
                }else if (ListUtils.isNotEmpty(service) && soldOut != null) {
                    for (DwsCloudServerLevelDO dwsCloudServerLevelDO : service) {
                        dwsCloudServerLevelDO.setSoldOutTotal(soldOut.getLeisureSoldOutTotal());
                        dwsCloudServerLevelDO.setSoldTotal(soldOut.getLeisureSoldTotal());
                    }
                }
            }
        }

        try {
            Map<String, YunxiaoGridItemVO> gridItemVOMap = gridItemMapFuture != null ? gridItemMapFuture.get() : null;

            resp.getData().forEach(item -> {
                if (item.getInstanceType().contains("/")) {
                    List actualSla = new ArrayList();
                    Splitter.on("/").splitToList(item.getInstanceType())
                            .forEach(s -> {
                                List ls = m.get(item.getZoneName() + "@" + s);
                                if (ls != null && !ls.isEmpty()) {
                                    actualSla.addAll(ls);
                                }
                            });
                    item.setActualSla(actualSla);
                } else {
                    item.setActualSla(m.get(item.getZoneName() + "@" + item.getInstanceType()));
                }

                // 如果指定了客户预扣则增加客户预扣字段，并重新计算冗余系数
                if (gridItemVOMap != null) {
                    YunxiaoGridItemVO gridItemVO = gridItemVOMap.get(String.join(item.getZoneName(), "@", item.getInstanceType()));
                    item.setReservedCores(gridItemVO == null ? 0 : gridItemVO.getSumCores());

                    if (item.getActualInventoryCore() != null && item.getSafetyInventoryCore() != null && item.getSafetyInventoryCore() > 0) {
                        item.setHealthRatio(NumberUtils.divide(item.getActualInventoryCore() - item.getReservedCores(), item.getSafetyInventoryCore(), 2));
                    } else {
                        item.setHealthRatio(BigDecimal.ZERO);
                    }
                }
            });

            return resp;
        } catch (Exception e) {
            log.error("获取库存健康数据失败", e);
            throw BizException.makeThrow("获取库存健康数据失败，请重试，若多次失败请联系 brightwwu");
        }

    }

    /**
     * 根据日切片查询库存健康度
     *
     * @param req
     * @return
     */
    private InventoryHealthActualResp queryInventoryHealthActualByDay(InventoryHealthActualReq req) {
        String startDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(req.getDate()), Calendar.DATE, -30));
        // 查询给定日期下，近一个月所有的原因分析，然后按照 机型 + 可用区 分组
        log.info("method queryInventoryHealthActualByDay 获取原因分析");
        Map<String, List<InventoryReasonDO>> reasonMap  = outsideService.listRangeReasonsParallelByInstanceTypeAndZoneName(startDate, req.getDate());
        log.info("method queryInventoryHealthActualByDay 获取原因分析成功：" +  reasonMap);
        log.info("method queryInventoryHealthActualByDay 获取安全库存数据");
        InventoryHealthActualResp resp = getActualAndSafeInvByDay(req);
        log.info("method queryInventoryHealthActualByDay 获取安全库存数据成功" + resp);
        String turnover = redisHelper.getString("DisassembleTurnoverType");
        List<String> turnoverType = new ArrayList<>();
        if (!StringUtils.isBlank(turnover) && !turnover.equals("(空值)")) {
            String[] split = turnover.split("@");
            turnoverType.addAll(Arrays.asList(split));
        }
        InventoryDisassembleResp disassembleResp = inventoryDisassembleService.queryInventoryDisassembleReport(
                req.transformReq(req, turnoverType));
        Map<String, InventoryDisassembleResp.Item> map = ListUtils.toMap(disassembleResp.getData(),
                o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
        for (Item item : resp.getData()) {
            InventoryDisassembleResp.Item tempItem = map.get(String.join("@", item.getZoneName(), item.getInstanceType()));
            if (tempItem != null) {
                item.setActualSafetyInventoryCore(tempItem.getActualSafeInventory());
                item.setDisassembleActualInventory(tempItem.getActualInventory());
                item.setDisassembleTurnoverInventory(tempItem.getActualTurnoverInventory());
            }
        }

        //获取采购机型
        List<String> purchaseInstanceType = inventoryHealthDictService.getPurchaseInstanceType();
        for (Item item : resp.getData()) {
            if (ListUtils.isNotEmpty(req.getInvDetailType())) {
                List<String> invDetailType = req.getInvDetailType();
                if (invDetailType.contains("用户预扣")) {
                    item.setIsContainPreDeduct(true);
                }else {
                    item.setIsContainPreDeduct(false);
                }
            }else {
                item.setIsContainPreDeduct(true);
            }

            if (purchaseInstanceType.contains(item.getInstanceType())) {
                item.setIsPurchaseInstance(true);
            }else {
                item.setIsPurchaseInstance(false);
            }
        }

        //根据机型族过滤数据
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            List<Item> items = resp.getData();
            resp.setData(items.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList()));
        }

        // 设置配置的组合机型表
        List<Set<String>> combination = SpringUtil.getBean(InventoryHealthDictServiceImpl.class).
                queryAllCombineInstanceType().getCombination();
        resp.setCombineList(ListUtils.transform(combination, o -> String.join("/", o)));

        // 设置原因详情
        resp.getData().forEach(item -> {
            String key = String.join(item.getInstanceType(), "@", item.getZoneName());
            List<InventoryReasonDO> list = reasonMap.get(key);
            if (list != null && !list.isEmpty()) {
                item.setReasons(list.stream().map(o -> {
                    InventoryHealthActualResp.ReasonItem reasonItem = new InventoryHealthActualResp.ReasonItem();
                    reasonItem.setReasonId(o.getId());
                    reasonItem.setReasonDate(DateUtils.formatDate(o.getDate()));
                    reasonItem.setReasonType(o.getReasonType());
                    reasonItem.setReasonDetail(o.getReasonDetail());
                    return reasonItem;
                }).collect(Collectors.toList()));
            }
        });
        return resp;
    }

    private InventoryHealthActualResp queryInventoryHealthActualByRangeAvg(InventoryHealthActualReq req) {
        // 复用安全库存趋势的日期范围。周取节假周
        Date beginDate;
        Date endDate;

        LocalDate reqDate = LocalDate.parse(req.getDate());
        TrendGraphServiceImpl trendGraphService = SpringUtil.getBean(TrendGraphServiceImpl.class);
        switch (req.getTimeDimension()) {
            case "周日均":
                // reqDate 始终是周一，时间范围取 req.getDate() 到 req.getDate() + 7
                List<ResPlanHolidayWeekDO> weekDOS = trendGraphService.getHolidayWeekInfo(reqDate, reqDate.plusDays(7));
                // 取到的第一周作为结果
                ResPlanHolidayWeekDO weekDO = weekDOS.get(0);
                beginDate = DateUtils.parse(weekDO.getStart());
                // 如果昨天小于周结束，则取昨天
                LocalDate yesterday = DateUtils.yesterday();

                if (yesterday.isBefore(LocalDate.parse(weekDO.getEnd()))) {
                    endDate = DateUtils.parse(yesterday.toString());
                } else {
                    endDate = DateUtils.parse(weekDO.getEnd());
                }
                break;
            case "月日均":
                TrendGraphReq trendGraphReq = new TrendGraphReq();
                trendGraphReq.setDateRange("month");
                trendGraphReq.setBeginDate(reqDate);
                trendGraphReq.setEndDate(reqDate);
                List<LocalDate> days = trendGraphService.getDaysFromParams(trendGraphReq);
                beginDate = DateUtils.parse(days.get(0).toString());
                endDate = DateUtils.parse(days.get(days.size() - 1).toString());
                break;
            default:
                throw BizException.makeThrow("仅支持 `周日均` 和 `月日均` 两个维度");
        }


        Date finalEndDate = endDate;

        List<Future<InventoryHealthActualResp>> futures = new ArrayList<>();

        Date tempBeginDate = beginDate;

        while (!tempBeginDate.after(finalEndDate)) {
            String date = DateUtils.formatDate(tempBeginDate);
            futures.add(threadPool.submit(() -> {
                log.info("计算{}的InventoryHealthActual", date);
                InventoryHealthActualReq clonedReq = req.clone();
                clonedReq.setDate(date);
                // 主力机型以及主力可用区均取date当天的，后面汇总成并集
                clonedReq.setCategoryDate(date);
                InventoryHealthActualResp resp = getActualAndSafeInvByDay(clonedReq);
                // 查询给定日期下，近一个月所有的原因分析，然后按照 机型 + 可用区 分组
                String startDate = DateUtils.formatDate(DateUtils.addTime(DateUtils.parse(clonedReq.getDate()), Calendar.DATE, -30));
                Map<String, List<InventoryReasonDO>> reasonMap = outsideService.listRangeReasonsParallelByInstanceTypeAndZoneName(startDate,clonedReq.getDate());
                resp.getData().forEach(item -> {
                    String key = String.join(item.getInstanceType(), "@", item.getZoneName());
                    List<InventoryReasonDO> list = reasonMap.get(key);
                    if (list != null && !list.isEmpty()) {
                        item.setReasons(list.stream().map(o -> {
                            InventoryHealthActualResp.ReasonItem reasonItem = new InventoryHealthActualResp.ReasonItem();
                            reasonItem.setReasonId(o.getId());
                            reasonItem.setReasonDate(DateUtils.formatDate(o.getDate()));
                            reasonItem.setReasonType(o.getReasonType());
                            reasonItem.setReasonDetail(o.getReasonDetail());
                            return reasonItem;
                        }).collect(Collectors.toList()));
                    }
                });
                return resp;
            }));

            tempBeginDate = DateUtils.addTime(tempBeginDate, Calendar.DATE, 1);
        }

        InventoryHealthActualResp resp = new InventoryHealthActualResp();
        resp.setData(new ArrayList<>());

        for (Future<InventoryHealthActualResp> future : futures) {
            try {
                resp.getData().addAll(future.get().getData());
            } catch (InterruptedException | ExecutionException e) {
                log.error("queryInventoryHealthActualByWeekAvg error", e);
                throw BizException.makeThrow("查询库存健康度失败，请联系 brightwwu 处理");
            }
        }

        // 按照主要属性分组求平均
        Map<String, List<InventoryHealthActualResp.Item>> map = ListUtils.groupBy(resp.getData(), o -> o.toKey());
        resp.setData(ListUtils.transform(map.entrySet(), e -> {
            InventoryHealthActualResp.Item item = new InventoryHealthActualResp.Item();
            item.setCustomhouseTitle(e.getValue().get(0).getCustomhouseTitle());
            item.setAreaName(e.getValue().get(0).getAreaName());
            item.setRegionName(e.getValue().get(0).getRegionName());
            item.setZoneName(e.getValue().get(0).getZoneName());
            item.setInstanceType(e.getValue().get(0).getInstanceType());
            item.setIsPurchaseInstance(e.getValue().get(0).getIsPurchaseInstance());
            item.setIsContainPreDeduct(e.getValue().get(0).getIsContainPreDeduct());

            List<Integer> actualInventory = e.getValue().stream().map(o -> o.getActualInventoryCore()).collect(Collectors.toList());
            List<Integer> preDeductInventory = e.getValue().stream().map(o -> o.getPreDeductInventoryCore()).collect(
                    Collectors.toList());
            List<Integer> safeInventory = e.getValue().stream().map(o -> o.getSafetyInventoryCore()).collect(Collectors.toList());
            List<BigDecimal> invManualConfig = e.getValue().stream().map(o -> o.getSafeInvManualConfig()).collect(Collectors.toList());
            List<Integer> prePaidSafeTyInventory = e.getValue().stream().map(o -> o.getPrePaidSafetyInventoryCore()).collect(Collectors.toList());
            List<Integer> actualSafeInv = e.getValue().stream().map(o -> o.getActualSafetyInventoryCore()).collect(
                    Collectors.toList());
            List<BigDecimal> bufferInventory = e.getValue().stream().map(o -> o.getBufferSafetyInventoryCore()).collect(Collectors.toList());
            List<Integer> reservedCores = e.getValue().stream().map(o -> o.getReservedCores()).collect(Collectors.toList());
            List<BigDecimal> deliveryAvgs = e.getValue().stream().map(o -> o.getDeliveryAvg()).collect(Collectors.toList());
            List<BigDecimal> levels = e.getValue().stream().map(o -> o.getServiceLevel()).collect(Collectors.toList());
            List<BigDecimal> levelFactors = e.getValue().stream().map(o -> o.getServiceLevelFactor()).collect(Collectors.toList());
            List<BigDecimal> bufferLevels = e.getValue().stream().map(o -> o.getBufferServiceLevel()).collect(Collectors.toList());
            List<BigDecimal> bufferLevelFactors = e.getValue().stream().map(o -> o.getBufferServiveLevelFactor()).collect(Collectors.toList());
            List<Integer> averageCores = e.getValue().stream().map(o -> o.getBufferAverageCore()).collect(Collectors.toList());
            List<BigDecimal> turnoverInvs = e.getValue().stream().map(o -> o.getTurnoverInv()).collect(Collectors.toList());
            List<BigDecimal> weekAvgCores = e.getValue().stream().map(o -> o.getTurnoverWeekReservedAvgCore()).collect(Collectors.toList());
            List<BigDecimal> peakCores = e.getValue().stream().map(o -> o.getTurnoverWeekPeakCore()).collect(Collectors.toList());
            List<String> startDates = e.getValue().stream().map(o -> o.getBufferAverageStartDate()).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> endDates = e.getValue().stream().map(o -> o.getBufferAverageEndDate()).filter(Objects::nonNull).collect(Collectors.toList());
            String firstDate = null;
            String lastDate = null;
            if (ListUtils.isNotEmpty(startDates)) {
                startDates.sort(String::compareTo);
                firstDate = startDates.get(0);
            }

            if (ListUtils.isNotEmpty(endDates)) {
                endDates.sort(String::compareTo);
                lastDate = endDates.get(endDates.size() - 1);
            }

            item.setActualInventoryCore(NumberUtils.avg(actualInventory, 2).intValue());
            item.setPreDeductInventoryCore(NumberUtils.avg(preDeductInventory, 2).intValue());
            item.setSafetyInventoryCore(NumberUtils.avg(safeInventory, 2).intValue());
            item.setSafeInvManualConfig(NumberUtils.avg(invManualConfig, 2));
            item.setPrePaidSafetyInventoryCore(NumberUtils.avg(prePaidSafeTyInventory, 2).intValue());
            item.setActualSafetyInventoryCore(NumberUtils.avg(actualSafeInv, 2).intValue());
            item.setBufferSafetyInventoryCore(NumberUtils.avg(bufferInventory, 2));
            item.setReservedCores(NumberUtils.avg(reservedCores, 2).intValue());
            item.setDeliveryAvg(NumberUtils.avg(deliveryAvgs, 2));
            item.setServiceLevel(NumberUtils.avg(levels, 2));
            item.setServiceLevelFactor(NumberUtils.avg(levelFactors, 2));
            item.setBufferServiceLevel(NumberUtils.avg(bufferLevels, 2));
            item.setBufferServiveLevelFactor(NumberUtils.avg(bufferLevelFactors, 2));
            item.setBufferAverageCore(NumberUtils.avg(averageCores, 2).intValue());
            item.setTurnoverInv(NumberUtils.avg(turnoverInvs, 2));
            item.setTurnoverWeekReservedAvgCore(NumberUtils.avg(weekAvgCores, 2));
            item.setTurnoverWeekPeakCore(NumberUtils.avg(peakCores, 2));

            //设置BufferAverageStartDate与BufferAverageEndDate
            item.setBufferAverageStartDate(firstDate);
            item.setBufferAverageEndDate(lastDate);


            if (item.getActualInventoryCore() != null && item.getSafetyInventoryCore() != null && item.getSafetyInventoryCore() > 0) {
                Integer actualInv = item.getActualInventoryCore();

                if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
                    actualInv = actualInv - item.getReservedCores();
                }
                item.setHealthRatio(NumberUtils.divide(BigDecimal.valueOf(actualInv), BigDecimal.valueOf(item.getSafetyInventoryCore()), 2));
            } else {
                item.setHealthRatio(BigDecimal.ZERO);
            }

            // 真实服务水平组合起来，直接把原始值返回，前端算
            List<DwsCloudServerLevelDO> actualSla = new ArrayList<>();
            e.getValue().forEach(o -> {
                if (o.getActualSla() != null) {
                    actualSla.addAll(o.getActualSla());
                }
            });
            if (ListUtils.isNotEmpty(actualSla)) {
                item.setActualSla(actualSla);
            }

            //将原因汇总起来并去重
            List<InventoryHealthActualResp.ReasonItem> reasonSet = new ArrayList<>();
            e.getValue().forEach(o -> {
                List<InventoryHealthActualResp.ReasonItem> reasons = o.getReasons();
                if (ListUtils.isNotEmpty(reasons)) {
                    reasonSet.addAll(reasons);
                }
            });
            Map<String, List<InventoryHealthActualResp.ReasonItem>> mapList = ListUtils.toMapList(reasonSet, o -> String.valueOf(o.getReasonId()), o -> o);
            List<InventoryHealthActualResp.ReasonItem> reason = new ArrayList<>();
            for (List<InventoryHealthActualResp.ReasonItem> value : mapList.values()) {
                if (ListUtils.isNotEmpty(value)) {
                    reason.add(value.get(0));
                }
            }
            item.setReasons(reason);
            return item;
        }));

        //根据机型族过滤数据
        if (ListUtils.isNotEmpty(req.getGinFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinFamily());
            List<Item> items = resp.getData();
            resp.setData(items.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList()));
        }

        // 设置配置的组合机型表
        List<Set<String>> combination = SpringUtil.getBean(InventoryHealthDictServiceImpl.class).
                queryAllCombineInstanceType().getCombination();
        resp.setCombineList(ListUtils.transform(combination, o -> String.join("/", o)));

        //获取采购机型
        List<String> purchaseInstanceType = inventoryHealthDictService.getPurchaseInstanceType();
        for (Item item : resp.getData()) {
            if (ListUtils.isNotEmpty(req.getInvDetailType())) {
                List<String> invDetailType = req.getInvDetailType();
                if (invDetailType.contains("用户预扣")) {
                    item.setIsContainPreDeduct(true);
                }else {
                    item.setIsContainPreDeduct(false);
                }
            }else {
                item.setIsContainPreDeduct(true);
            }

            if (purchaseInstanceType.contains(item.getInstanceType())) {
                item.setIsPurchaseInstance(true);
            }else {
                item.setIsPurchaseInstance(false);
            }
        }

        return resp;
    }

    /**
     * 根据周日均查询库存健康度
     */
    public InventoryHealthActualResp queryInventoryHealthActualByWeekAvg(InventoryHealthActualReq req) {
        return queryInventoryHealthActualByRangeAvg(req);
    }

    /**
     * 根据月日均查询库存健康度
     */
    public InventoryHealthActualResp queryInventoryHealthActualByMonthAvg(InventoryHealthActualReq req) {
        return queryInventoryHealthActualByRangeAvg(req);
    }



    public Map<String, DwsLeisureAndBusySoldOutDataDfDO> getLeisureSoldOutData(String date) {
        String version = DateUtils.format(DateUtils.parse(date), "yyyyMMdd");
        List<DwsLeisureAndBusySoldOutDataDfDO> all = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class,
                "where imp_date = ?", version);
        return ListUtils.toMap(all, o -> String.join("@", o.getZoneName(), o.getInstanceType()), o -> o);
    }

    @Data
    public static class ActualInventoryDO {
        @Column("stat_time")
        private String statTime;

        /** 可用区名 */
        @Column("zone_name")
        private String zoneName;

        @Column("inv_detail_type")
        private String invDetailType;

        /**实例类型*/
        @Column("instance_type")
        private String instanceType;

        @Column("sum")
        private BigDecimal actualInv;
    }

}
