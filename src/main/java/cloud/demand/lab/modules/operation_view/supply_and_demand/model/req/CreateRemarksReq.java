package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.LinkedHashMap;
import java.util.List;

@Data
public class CreateRemarksReq {

    @NotBlank(message = "产品不能为空")
    private String productCategory;

    @NotBlank(message = "版本不能为空")
    private String version;

    @NotEmpty(message = "维度key集合不能空")
    private List<String> dimKey;

    private List<String> dimValue;

    @Pattern(regexp = "^(inventory|supply|demand)$", message = "dataType只能是inventory|supply|demand")
    @NotBlank(message = "dataType不能为空")
    private String dataType;

    @NotBlank(message = "备注不能空")
    @Length(max = 4096, message = "备注字符长度不能超过4096")
    private String remarks;

}
