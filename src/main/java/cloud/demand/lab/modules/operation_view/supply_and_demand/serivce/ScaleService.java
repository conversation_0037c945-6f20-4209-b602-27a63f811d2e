package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.operation_view.entity.yunti.BasObsCloudCvmTypeDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandCommonReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import report.utils.utils.SelectCaseBuilder;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/8 15:36
 */
public interface ScaleService {

    String getProductCategory();

    List<DemandReportDetailVO> queryCurScaleData(DemandCommonReq req);
    List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req);

    List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req, List<String> notOtherCustomerUin, List<String> notOtherCommonCustomerShortName);

    default String getCountryNameSelectCase(List<TxyRegionInfoDTO> txyRegionInfoDTOList) {
        txyRegionInfoDTOList = txyRegionInfoDTOList.stream().filter(item -> !StringUtils.equals("中国内地", item.getCountry())).collect(Collectors.toList());
        Map<String, List<TxyRegionInfoDTO>> countryNameGroupMap = ListUtils.groupBy(txyRegionInfoDTOList, TxyRegionInfoDTO::getCountry);
        Map<List<String>, String> countryNameMap = new HashMap<>();
        countryNameGroupMap.forEach((k, v) -> {
            countryNameMap.put(v.stream().map(TxyRegionInfoDTO::getRegionName).collect(Collectors.toList()), k);
        });
        return SelectCaseBuilder.build("region_name", "'中国内地'", countryNameMap);
    }

    default String getAreaNameSelectCase(List<TxyRegionInfoDTO> txyRegionInfoDTOList) {
        Map<String, List<TxyRegionInfoDTO>> countryNameGroupMap = ListUtils.groupBy(txyRegionInfoDTOList, TxyRegionInfoDTO::getAreaName);
        Map<List<String>, String> areaMap = new HashMap<>();
        countryNameGroupMap.forEach((k, v) -> {
            List<String> list = v.stream().map(TxyRegionInfoDTO::getRegionName).collect(Collectors.toList());
            areaMap.put(list, k);
        });
        return SelectCaseBuilder.build("region_name", "'(空值)'", areaMap);
    }

    default String getZoneCategorySelectCase(List<InventoryHealthMainZoneNameConfigDO> zoneConfigList) {
        Map<String, List<InventoryHealthMainZoneNameConfigDO>> groupMap = ListUtils.groupBy(zoneConfigList, InventoryHealthMainZoneNameConfigDO::getTypeName);
        Map<List<String>, String> map = new HashMap<>();
        groupMap.forEach((k, v) -> {
            List<String> list = v.stream().map(InventoryHealthMainZoneNameConfigDO::getZoneName).collect(Collectors.toList());
            map.put(list, k);
        });
        return SelectCaseBuilder.build("zone_name", "'(空值)'", map);
    }

    default String getInstanceCategorySelectCase(List<InventoryHealthMainInstanceTypeConfigDO> instanceConfigList) {
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> groupMap = ListUtils.groupBy(instanceConfigList, InventoryHealthMainInstanceTypeConfigDO::getType2Name);
        Map<List<String>, String> map = new HashMap<>();
        groupMap.forEach((k, v) -> {
            List<String> list = v.stream().map((item) ->
                            StringUtils.joinWith("@", StringUtils.equals("海外", item.getRegionType()) ? "境外" : item.getRegionType(), item.getInstanceType()))
                    .collect(Collectors.toList());
            map.put(list, k);
        });
        return SelectCaseBuilder.build("instance_config", "'(空值)'", map);
    }

    default String getInstanceGroupSelectCase(List<BasObsCloudCvmTypeDO> cvmTypeList) {
        Map<String, List<BasObsCloudCvmTypeDO>> groupMap = ListUtils.groupBy(cvmTypeList, BasObsCloudCvmTypeDO::getCvmInstanceGroup);
        Map<List<String>, String> map = new HashMap<>();
        groupMap.forEach((k, v) -> {
            List<String> list = v.stream().map(BasObsCloudCvmTypeDO::getCvmInstanceTypeCode).collect(Collectors.toList());
            map.put(list, k);
        });
        return SelectCaseBuilder.build("instance_type", "'(空值)'", map);
    }

    default String getUinOrCommonShortNameSelectCase(List<String> values, String sourceFieldName, String targetValue) {
        StringBuilder sbr = new StringBuilder();
        if (ListUtils.isEmpty(values)) {
            return sourceFieldName;
        }
        sbr.append("(case when ").append(sourceFieldName).append(" in ('").append(StringUtils.join((values), "','")).append("')");
        sbr.append(" then ").append(sourceFieldName);
        sbr.append(" else ").append(targetValue).append(" end)");

        return sbr.toString();
    }

    default List<String> addLastMonthStatTime(List<String> statTimeList) {
        List<String> lastMonthStatTime = ListUtils.newArrayList();
        for (String statTime : statTimeList) {
            String lastMonth = LocalDate.parse(statTime).minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ISO_LOCAL_DATE);
            lastMonthStatTime.add(lastMonth);
        }
        return ListUtils.union(statTimeList, lastMonthStatTime);
    }

}
