package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import cloud.demand.lab.modules.operation_view.supply_and_demand.parse.VolumeTypeParse;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import report.utils.anno.WhereReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
public class DayCbsScaleReq {

    @WhereReport
    private List<String> statTime;

    private List<String> zoneCategory;

    @WhereReport
    private List<String> customhouseTitle;

    private List<String> countryName;

    @WhereReport(sql = " area_name in (?) ")
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;

    @WhereReport(parseParams = "device_type", parsers = VolumeTypeParse.class)
    private List<String> volumeType;

    private List<String> dims;

    private List<String> originalStatTime;

    public static DayCbsScaleReq transform(DemandCommonReq req) {
        DayCbsScaleReq targetReq = new DayCbsScaleReq();
        BeanUtils.copyProperties(req, targetReq);
        targetReq.setStatTime(req.getScaleStatTime());
        return targetReq;
    }

}
