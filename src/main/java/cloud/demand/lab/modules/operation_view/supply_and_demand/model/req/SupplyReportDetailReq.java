package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyOnTheWayDetailData;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import javax.validation.constraints.NotEmpty;

@Data
public class SupplyReportDetailReq {

    private String startDate;

    private String endDate;

    private String startMonth;

    private String endMonth;

    @NotEmpty(message = "产品类型不能为空")
    private List<String> productType;

    /**
     * 产品子类
     */
    private List<String> product;

    private List<String> quotaPlanProductName;

    private List<String> quotaIds;

    private List<String> xyIndustry;

    private List<String> xyCustomerName;

    private List<String> deliveryPoint;

    private List<String> quotaCampusName;

    private List<String> campus;

    private List<String> zoneCategory;

    private List<String> instanceCategory;

    private List<String> quotaDeviceClass;

    private List<String> instanceGroup;

    private List<String> instanceType;

    private List<String> volumeType;

    private List<String> customhouseTitle;

    private List<String> countryName;

    private List<String> regionName;

    private List<String> areaName;

    private List<String> zoneName;

    private List<String> projectName;

    /**
     * productType,quotaPlanProductName,quotaId,quotaCampusName,campus,quotaRegionName,module,xyIndustry,xyCustomerName
     * isFakePosition,zoneCategory,instanceCategory,customhouseTitle,countryName,areaName,regionName,zoneName,instanceGroup,instanceType
     */
    private List<String> dims = ListUtils.newArrayList();

    private boolean includeUnKnow = true;

    private String unit = "逻辑核";

    private boolean hedging = false;

    /**
     * 交付状态:在途、已交付
     */
    private List<String> deliveryStatus = ListUtils.newArrayList("在途");


    public WhereSQL genBasicCondition() {
        WhereSQL condition = new WhereSQL();

        if (ListUtils.isNotEmpty(productType)) {
            condition.and("product_type in (?)", productType);
        }
        condition.andIf(ListUtils.isNotEmpty(product), "product in (?)", product);
        if (ListUtils.isNotEmpty(quotaPlanProductName)) {
            condition.and("quota_plan_product_name in (?)", quotaPlanProductName);
        }
        if (ListUtils.isNotEmpty(quotaIds)) {
            condition.and("quota_id in (?)", quotaIds);
        }
        if (ListUtils.isNotEmpty(quotaCampusName)) {
            condition.and("campus_name in (?)", quotaCampusName);
        }
        if (ListUtils.isNotEmpty(campus)) {
            condition.and("campus in (?)", campus);
        }
        condition.andIf(ListUtils.isNotEmpty(xyIndustry), "xy_industry in (?)", xyIndustry);
        condition.andIf(ListUtils.isNotEmpty(xyCustomerName), "xy_customer_name in (?)", xyCustomerName);
        condition.andIf(ListUtils.isNotEmpty(deliveryPoint), " delivery_point in (?) ", deliveryPoint);
        condition.andIf(ListUtils.isNotEmpty(instanceGroup), "gin_family in (?)", instanceGroup);
        condition.andIf(ListUtils.isNotEmpty(quotaDeviceClass), "quota_device_class in (?)", quotaDeviceClass);

        if (ListUtils.isNotEmpty(instanceType)) {
            condition.and("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(volumeType)) {
            condition.and("volume_type in (?)", volumeType);
        }
        if (ListUtils.isNotEmpty(customhouseTitle)) {
            condition.and("customhouse_title in (?)", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(countryName)) {
            condition.and("country in (?)", countryName);
        }
        if (ListUtils.isNotEmpty(regionName)) {
            condition.and("region_name in (?)", regionName);
        }
        if (ListUtils.isNotEmpty(areaName)) {
            condition.and("area_name in (?)", areaName);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            condition.and("zone_name in (?)", zoneName);
        }

        condition.andIf(ListUtils.isNotEmpty(zoneCategory), "zone_category in (?)", zoneCategory);
        condition.andIf(ListUtils.isNotEmpty(instanceCategory), "instance_category in (?)", instanceCategory);
        condition.andIf(ListUtils.isNotEmpty(deliveryStatus), "delivery_status in (?)", deliveryStatus);

        List<String> statTime = new ArrayList<>();
        if(StringUtils.isNotBlank(startDate)){
            statTime.add(startDate);
        }
        if(StringUtils.isNotBlank(endDate)){
            statTime.add(endDate);
        }
        condition.and("stat_time in (?)", statTime);
        return condition;

    }

    public static SupplyReportDetailReq transform(SupplyDemandHedgingReq req) {
        SupplyReportDetailReq ret = new SupplyReportDetailReq();
        if(BooleanUtils.isTrue(req.isOverView())){
            ret.setEndDate(req.getStatTime());
        }else {
            ret.setStartDate(req.getStartStatTime());
            ret.setEndDate(req.getEndStatTime());
        }
        ret.setProductType(ListUtils.newArrayList(req.getProductCategory()));
        ret.setProduct(req.getProduct());
        //ret.setQuotaPlanProductName(ListUtils.newArrayList("腾讯云CVM"));
        ret.setVolumeType(req.getVolumeType());
        ret.setStartMonth(req.getStartYearMonth());
        ret.setEndMonth(req.getEndYearMonth());
        ret.setZoneCategory(req.getZoneCategory());
        ret.setInstanceCategory(req.getInstanceCategory());
        ret.setInstanceGroup(req.getInstanceGroup());
        ret.setInstanceType(req.getInstanceType());
        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        ret.setCountryName(req.getCountryName());
        ret.setAreaName(req.getAreaName());
        ret.setRegionName(req.getRegionName());
        ret.setZoneName(req.getZoneName());
        ret.setDims(ListUtils.union(req.getDims(),ListUtils.newArrayList("statTime","salMonth")));
        ret.setUnit(req.getUnit());
        ret.setHedgingParams();
        ret.setDeliveryStatus(ListUtils.newArrayList( "在途"));
        return ret;
    }

    public void setHedgingParams() {
        String productType = this.getProductType().get(0);
        if(hedging && StringUtils.equals(productType, ProductCategoryEnum.DB.getName())){
            List<String> deviceList = SpringUtil.getBean("demandDBHelper", DBHelper.class).getAll(BasDeviceMemoryDO.class)
                    .stream().map(BasDeviceMemoryDO::getDeviceType).collect(Collectors.toList());
            this.setQuotaDeviceClass(deviceList);
            if(ListUtils.isEmpty(this.getProduct())){
                this.setProduct(ListUtils.newArrayList("CRS", "CDB"));
            }
        }
    }
}
