package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryReasonDO;
import java.util.List;
import java.util.Map;

public interface OutsideHealthService {

    public Map<String, List<InventoryReasonDO>> listRangeReasonsParallelByInstanceTypeAndZoneName(String startDate, String endDate);
    Boolean isZysyModule(String planProduct, String biz1, String biz2, String biz3);
}
