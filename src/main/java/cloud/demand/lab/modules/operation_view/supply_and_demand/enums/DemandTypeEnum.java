package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/27 16:54
 */
@Getter
@AllArgsConstructor
public enum DemandTypeEnum {

    RETURN("RETURN","退回需求"),
    NEW("NEW","新增需求"),
    ELASTIC("ELASTIC","弹性需求"),
    ;

    private final String code;

    private final String name;


    public static DemandTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DemandTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    public static DemandTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (DemandTypeEnum value : values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        DemandTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        DemandTypeEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }
}
