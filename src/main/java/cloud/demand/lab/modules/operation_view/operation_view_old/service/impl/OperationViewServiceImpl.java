package cloud.demand.lab.modules.operation_view.operation_view_old.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.common.utils.AmountUtils;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.enums.ProductTypeEnum;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.DeviceSlaDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.GroupByEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.LineTypeEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.MonthAvgAlgorithmEnum;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewReq;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewRsp;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OperationViewService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.common.OperationViewCommonService;
import cloud.demand.lab.modules.operation_view.operation_view_old.utils.OperationViewTools;
import cloud.demand.lab.modules.operation_view.operation_view_old.vo.ReportOperationViewDetailVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OperationViewServiceImpl implements OperationViewService {

    @Resource
    private DictService dictService;
    @Resource
    private DBHelper purchasereportDBHelper;
    @Resource
    private OperationViewCommonService commonService;

    @Resource
    private OutsideViewOldService outsideViewOldService;


    /**
     * 服务水平，默认0.9
     */
    @Value("${operationView.serviceLevel:0.9}")
    private BigDecimal serviceLevel;

    /**
     * 服务水平，默认0.9
     */
    @Value("${operationView.bufferServiceLevel:0.9}")
    private BigDecimal bufferServiceLevel;

    /**
     * 国内的sla，默认28天
     */
    @Value("${operationView.mainlandSla:28}")
    private Integer mainlandSla;
    /**
     * 国外的sla，默认57天
     */
    @Value("${operationView.overseaSla:57}")
    private Integer overseaSla;

    @Override
    public Integer getSLA(String deviceType, boolean isMainland) {
        if (StringTools.isBlank(deviceType)){
            return isMainland ? mainlandSla : overseaSla;
        }
        Map<String, DeviceSlaDO> map = SpringUtil.getBean(OperationViewServiceImpl.class).getDeviceSlaMap();
        DeviceSlaDO deviceSlaDO = map.get(deviceType);
        if (deviceSlaDO == null) {
            return isMainland ? mainlandSla : overseaSla;
        }
        return isMainland ? deviceSlaDO.getMainlandSla() : deviceSlaDO.getOverseasSla();
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0] + args[1]")
    @Override
    public Integer getSLAByInstanceType(String instanceType, boolean isMainland) {
        // 1. 将instanceType转换为deviceType
        List<String> deviceTypes = new ArrayList<>();
//        // 1.1 如果manual_device_type_to_instance_type 人工已经配置了，则用人工的，只会是1对1
//        List<ManualDeviceTypeToInstanceTypeDO> manual =
//                SpringUtil.getBean(OperationViewServiceImpl.class).getManualDeviceTypeToInstanceType();
//              // self.getManualDeviceTypeToInstanceType();
//        Map<String, String> manualMap = ListUtils.toMap(manual, o -> o.getInstanceType(), o -> o.getDeviceType());
//        if (manualMap.containsKey(instanceType)) {
//            deviceTypes.add(manualMap.get(instanceType));
//        } else {
//            // 1.2 如果没有，抛出异常
//            List<String> devices = pplDictService.getDeviceTypeByInstanceType(ListUtils.newList(instanceType));
//            if (devices != null) {
//                deviceTypes.addAll(devices);
//            }
//        }

        deviceTypes.addAll(dictService.getCsigDeviceTypeByInstanceType(instanceType));

        // 2. 通过deviceType获得sla，多个则取平均值
        List<Integer> slas = new ArrayList<>();
        for (String type : deviceTypes) {
            // slas.add(self.getSLA(type, isMainland));
            slas.add(SpringUtil.getBean(OperationViewServiceImpl.class).getSLA(type, isMainland));
        }

        if (slas.isEmpty()) {
            return isMainland ? mainlandSla : overseaSla; // 如果instanceType没有对应物理机，则用默认值
        }

        return NumberUtils.avg(slas, 0).intValue();
    }

    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, keyScript = "args[0]")
    @Override
    public Map<String, Integer> getAllSLA(boolean isMainland) {
        Map<String,Integer> ret = new HashMap<>();
        Integer devSla = getDefSLA(isMainland);
        // 设备类型 --> SLA
        Map<String, DeviceSlaDO> deviceSlaMap = SpringUtil.getBean(OperationViewServiceImpl.class).getDeviceSlaMap();
        // 实例类型集合
        Set<String> allCvmType = outsideViewOldService.getAllCvmType().stream().map(CvmType::getInstanceType).collect(
                Collectors.toSet());
        // 实例类型 --> 设备类型
        Map<String, List<String>> typeMap = dictService.getCsigInstanceTypeToDeviceTypeMap();
        for (String instanceType : allCvmType) {
            List<String> deviceTypeList = typeMap.get(instanceType);
            Integer sla;
            if (ListUtils.isEmpty(deviceTypeList)){
                sla = devSla;
            }else {
                List<Integer> slaList = new ArrayList<>();
                for (String deviceType : deviceTypeList) {
                    DeviceSlaDO deviceSlaDO = deviceSlaMap.get(deviceType);
                    if (deviceSlaDO == null){
                        slaList.add(devSla);
                    }else {
                        slaList.add(isMainland?deviceSlaDO.getMainlandSla():deviceSlaDO.getOverseasSla());
                    }
                }
                sla = NumberUtils.avg(slaList, 0).intValue();
            }
            ret.put(instanceType,sla);
        }
        return ret;
    }

    @Override
    public Integer getDefSLA(boolean isMainland) {
        return isMainland? mainlandSla : overseaSla;
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600, cloneReturn = false)
    public Map<String, DeviceSlaDO> getDeviceSlaMap() {
        List<DeviceSlaDO> sla = purchasereportDBHelper.getAll(DeviceSlaDO.class,
                "where endTime is null");
        return ListUtils.toMap(sla, o -> o.getDeviceType(), o -> o);
    }





}