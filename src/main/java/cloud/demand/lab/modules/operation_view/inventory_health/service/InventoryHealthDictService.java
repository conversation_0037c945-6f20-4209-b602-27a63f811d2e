package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.InstanceTypeCombineDTO;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/**
 * 库存健康相关字典接口
 */
public interface InventoryHealthDictService {

    /**
     * 查询全部组合机型列表
     * @return
     */
    InstanceTypeCombineDTO queryAllCombineInstanceType();

    /**
     * 获取节假周信息，以baseDate为基准日期，向前或向后偏移n周的节假周信息
     * @param baseDate  基准日期
     * @param n 偏移周数
     * @return
     */
    List<HolidayWeekInfoDTO> getHolidayWeekInfoBase(LocalDate baseDate, int n);

    /**
     * 获取节假周信息，以baseDate为基准日期，向前或向后偏移n周的节假周信息，包括当周
     * @param baseDate  基准日期
     * @param n 偏移周数
     * @return
     * */
    List<HolidayWeekInfoDTO> getHolidayWeekFromCurrent(LocalDate baseDate,int n);

    /**
     * 获取节假周信息，查询两个日期之间的节假周信息
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @return
     */
    List<HolidayWeekInfoDTO> getHolidayWeekBetWeen(LocalDate startDate,LocalDate endDate);

    /**
     * 获取节假周信息，查询年月的首周
     * @param yearMonth 年月
     * @return 年月的第一周
     */
    ResPlanHolidayWeekDO getFirstWeekByMonth(YearMonth yearMonth);

    ResPlanHolidayWeekDO getFirstWeekByMonth(int year,int month);

    ResPlanHolidayWeekDO getFirstWeekByDate(LocalDate date);

    List<String> getPurchaseInstanceType();
}
