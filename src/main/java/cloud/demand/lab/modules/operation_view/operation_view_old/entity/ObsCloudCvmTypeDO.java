package cloud.demand.lab.modules.operation_view.operation_view_old.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class ObsCloudCvmTypeDO {

    @Column("CvmInstanceGroup")
    private String instanceFamily;

    @Column("CvmInstanceTypeCode")
    private String instanceType;


    @Column("CvmInstanceType")
    private String instanceTypeName;

    @Column("HostDeviceClass")
    private String deviceType;
}