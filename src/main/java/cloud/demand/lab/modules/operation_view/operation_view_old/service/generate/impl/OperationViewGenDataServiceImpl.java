package cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.impl;

import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.OperationViewGenDataService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.cvm.GenCvmDetailService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.gpu.GenGpuDetailService;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.generate.metal.GenMetalDetailService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OperationViewGenDataServiceImpl implements OperationViewGenDataService {

    @Resource
    GenCvmDetailService cvmDetailService;
    @Resource
    GenMetalDetailService metalDetailService;
    @Resource
    GenGpuDetailService gpuDetailService;


    @Override
    @Synchronized(waitLockMillisecond = 100, keyScript = "args[0]", throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "GenOperationViewDetail")
    public void genAllDetailData(String statTime) {

    }
}
