package cloud.demand.lab.modules.operation_view.supply_and_demand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/27 16:54
 */
@Getter
@AllArgsConstructor
public enum UnitEnum {

    NUM("NUM", "台"),
    CORE("CORE", "逻辑核"),
    STORAGE("STORAGE", "逻辑容量"),

    MEMORY("MEMORY", "逻辑内存量"),
    CARD("CARD", "卡数");

    private final String code;

    private final String name;


    public static UnitEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (UnitEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    public static UnitEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (UnitEnum value : values()) {
            if (Objects.equals(value.getName(), name)) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        UnitEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        UnitEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }
}
