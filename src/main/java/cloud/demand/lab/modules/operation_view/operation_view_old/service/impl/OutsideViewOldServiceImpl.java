package cloud.demand.lab.modules.operation_view.operation_view_old.service.impl;

import cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.InstanceTypeLongTail;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.ObsCloudCvmTypeDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;

@Service
@Slf4j
public class OutsideViewOldServiceImpl implements OutsideViewOldService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper obsDBHelper;

    @Resource
    private DBHelper yuntiDBHelper;

    @Resource
    private DBHelper ckForecastStdCrpDBHelper;

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, useRedis = true)
    @SynchronizedHiSpeedCache1Second
    public List<CvmType> getAllCvmType() {
        log.info("method getAllCvmType开始从obsDBHelper查询数据");
        List<ObsCloudCvmTypeDO> data_with_family = obsDBHelper.getRaw(ObsCloudCvmTypeDO.class, "select DISTINCT CvmInstanceGroup,CvmInstanceType,CvmInstanceTypeCode,HostDeviceClass from bas_obs_cloud_cvm_type");
        log.info("method getAllCvmType从obsDBHelper查询数据成功：" + data_with_family);
        List<CvmType> data = new ArrayList<>();
        for (ObsCloudCvmTypeDO typeDO : data_with_family) {
            CvmType cvmType = new CvmType();
            cvmType.setDeviceType(typeDO.getDeviceType());
            cvmType.setGinsFamily(typeDO.getInstanceFamily());
            cvmType.setInstanceType(typeDO.getInstanceType());
            cvmType.setInstanceTypeName(typeDO.getInstanceTypeName());
            data.add(cvmType);
        }
        List<CvmType> cvmTypes = fillCvmType(data);
        Map<String, String> map = ListUtils.toMap(cvmTypes, CvmType::getInstanceType, CvmType::getGinsFamily);

        //判断是否添加过机型
        HashSet<String> existInstanceType = new HashSet<>();
        log.info("method getAllCvmType开始从yuntiDBHelper查询数据");
        List<CloudDemandCsigDeviceExtendInfoDO> all = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
        log.info("method getAllCvmType从yuntiDBHelper查询数据成功：" + all);
        List<CvmType> ret = new ArrayList<>();
        for (CloudDemandCsigDeviceExtendInfoDO infoDO : all) {
            CvmType e = new CvmType();
            e.setDeviceType(infoDO.getDeviceType());
            e.setInstanceType(infoDO.getInstanceTypeEng());
            e.setInstanceTypeName(infoDO.getInstanceType());
            e.setGinsFamily(map.getOrDefault(infoDO.getInstanceTypeEng(), Constant.EMPTY_VALUE_STR));

            existInstanceType.add(infoDO.getInstanceTypeEng());

            ret.add(e);
        }

        //中长尾特有机型处理
        log.info("method getAllCvmType开始从ckForecastStdCrpDBHelper查询数据");
        String sql = "SELECT distinct gins_family\n" +
                "from dwd_crp_longtail_forecast_item_df\n" +
                "where category like '方案725%' and seq_type = 'NEW'";
        List<InstanceTypeLongTail> instanceTypeLongTails = ckForecastStdCrpDBHelper.getRaw(InstanceTypeLongTail.class, sql);
        log.info("method getAllCvmType从yuntiDBHelper查询数据成功：" + instanceTypeLongTails);
        for (InstanceTypeLongTail i : instanceTypeLongTails) {
            if (i.getInstanceType() != null && !existInstanceType.contains(i.getInstanceType())) {
                CvmType e = new CvmType();
                e.setDeviceType(Constant.EMPTY_VALUE_STR);
                e.setInstanceType(i.getInstanceType());
                e.setInstanceTypeName(Constant.EMPTY_VALUE_STR);
                //无法确定机型族，暂时处理成空值
                e.setGinsFamily(Constant.EMPTY_VALUE_STR);
                ret.add(e);
            }
        }

        return ret;
    }

    private List<CvmType> fillCvmType(List<CvmType> data_with_family) {
        List<CvmType> ret = new ArrayList<>(ObjectUtils.defaultIfNull(data_with_family, new ArrayList<>()));
        // step2：全量CVM实例规格表，但是不一定有机型族(通过instance_type_name截取)
        String sql = "select distinct instance_type,instance_type_name from industry_demand_region_zone_instance_type_dict" +
                " where deleted = 0 and (instance_type is not null and instance_type <> '')";
        Set<String> filterSet = ListUtils.toSet(ret, CvmType::getInstanceType);
        List<InstanceType> data_without_family = demandDBHelper.getRaw(InstanceType.class, sql);
        // step3：data_without_family 提取到 Map -> k ：实例类型，v：机型族
        Map<String, String> instanceType2Family = new HashMap<>();
        data_without_family.forEach(item -> {
            String instanceType = item.getInstanceType();
            if (!filterSet.contains(instanceType)) {
                String instanceTypeName = item.getInstanceTypeName();
                String instanceFamily = instanceTypeName;
                if (StringUtils.isNotBlank(instanceFamily)) {
                    instanceFamily = instanceTypeName.replace(instanceType, "");
                }

                // 实例类型匹配到机型族，后续再有改实例类型直接跳过
                if (StringUtils.isNotBlank(instanceFamily)) {
                    filterSet.add(instanceType);
                }

                instanceType2Family.put(instanceType, instanceFamily);
            }
        });

        // step4：data_without_family merge --> data_with_family
        // 机型族：继承(空替换为空值)
        // gpu卡型：空值
        // 实例类型：继承
        // 实例类型名称；机型族+实例类型

        instanceType2Family.forEach((k, v) -> {
            CvmType e = new CvmType();
            e.setInstanceType(k);
            e.setGinsFamily(StringUtils.isBlank(v) ? Constant.EMPTY_VALUE_STR : v);
            e.setInstanceTypeName(v + k);
            e.setDeviceType(Constant.EMPTY_VALUE_STR);
            ret.add(e);
        });

        return ret;
    }

    @Data
    public static class InstanceType {

        /**
         * 实例类型
         */
        @Column("instance_type")
        private String instanceType;

        /**
         * 实例类型名称（机型族+实例类型）
         */
        @Column("instance_type_name")
        private String instanceTypeName;
    }
}
