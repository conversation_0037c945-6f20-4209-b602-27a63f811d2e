package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/31 13:38
 */
@Data
public class ExportCbsDemandVO {

    private List<List<String>> headList;

    private List<List<Object>> dataList;

    public static ExportCbsDemandVO build(List<ExportDemandItemVO> data, String startStatTime, String endStatTime) {
        ExportCbsDemandVO vo = new ExportCbsDemandVO();
        vo.setHeadList(buildHeadList(startStatTime, endStatTime));
        vo.setDataList(buildDataList(data));
        return vo;
    }

    private static List<List<String>> buildHeadList(String startStatTime, String endStatTime) {
        List<List<String>> headList = ListUtils.newArrayList();
        headList.add(ListUtils.newArrayList("需求年月"));
        headList.add(ListUtils.newArrayList("开始购买时间"));
        headList.add(ListUtils.newArrayList("结束购买时间"));
        headList.add(ListUtils.newArrayList("单据类型"));
        headList.add(ListUtils.newArrayList("单号"));
        headList.add(ListUtils.newArrayList("行业"));
        headList.add(ListUtils.newArrayList("战区"));
        headList.add(ListUtils.newArrayList("客户简称"));
        headList.add(ListUtils.newArrayList("客户UIN"));
        headList.add(ListUtils.newArrayList("项目名称"));
        headList.add(ListUtils.newArrayList("需求场景"));
        headList.add(ListUtils.newArrayList("园区类型"));
        headList.add(ListUtils.newArrayList("境内外"));
        headList.add(ListUtils.newArrayList("国家"));
        headList.add(ListUtils.newArrayList("区域"));
        headList.add(ListUtils.newArrayList("地域"));
        headList.add(ListUtils.newArrayList("可用区"));
        headList.add(ListUtils.newArrayList("云盘类型\n(性能维度)"));
        headList.add(ListUtils.newArrayList("云盘类型\n(功能维度)"));
        headList.add(ListUtils.newArrayList("需求类型"));

        headList.add(ListUtils.newArrayList("云盘数量\n" + startStatTime));
        headList.add(ListUtils.newArrayList("云盘数量\n" + endStatTime));
        headList.add(ListUtils.newArrayList("云盘数量\n变化量"));
        headList.add(ListUtils.newArrayList("云盘容量\n" + startStatTime));
        headList.add(ListUtils.newArrayList("云盘容量\n" + endStatTime));
        headList.add(ListUtils.newArrayList("云盘容量\n变化量"));

        return headList;
    }

    private static List<List<Object>> buildDataList(List<ExportDemandItemVO> data) {
        List<List<Object>> rows = ListUtils.newArrayList();
        for (ExportDemandItemVO item : data) {
            List<Object> row = ListUtils.newArrayList();
            row.add(item.getYearMonth());
            row.add(item.getBeginBuyDate());
            row.add(item.getEndBuyDate());
            row.add(item.getDataSource());
            row.add(item.getBillNumber());
            row.add(item.getIndustryDept());
            row.add(item.getWarZone());
            row.add(item.getCommonCustomerShortName());
            row.add(item.getCustomerUin());
            row.add(item.getProjectName());
            row.add(item.getDemandScene());
            row.add(item.getZoneCategory());
            row.add(item.getCustomhouseTitle());
            row.add(item.getCountryName());
            row.add(item.getAreaName());
            row.add(item.getRegionName());
            row.add(item.getZoneName());
            row.add(item.getVolumeType());
            row.add(item.getDiskType());
            row.add(item.getDemandType());
            row.add(item.getStartAmount1());
            row.add(item.getEndAmount1());
            row.add(item.getDiffAmount1());
            row.add(item.getStartAmount2());
            row.add(item.getEndAmount2());
            row.add(item.getDiffAmount2());
            rows.add(row);
        }
        return rows;
    }

}
