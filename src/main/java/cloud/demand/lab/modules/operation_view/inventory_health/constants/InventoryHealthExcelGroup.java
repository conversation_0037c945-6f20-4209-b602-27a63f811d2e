package cloud.demand.lab.modules.operation_view.inventory_health.constants;

public class InventoryHealthExcelGroup {
    public static final String INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL = "inventory_health_excel_group_service_level";
    public static final String INVENTORY_HEALTH_EXCEL_GROUP_ZONE = "inventory_health_excel_group_zone";
    public static final String INVENTORY_HEALTH_EXCEL_GROUP_INSTANCE_TYPE = "inventory_health_excel_group_instance_type";

    public static final String INVENTORY_HEALTH_EXCEL_GROUP_BUFFER_POOL = "inventory_health_excel_group_buffer_pools";

    public static final String INVENTORY_HEALTH_TREND_GRAPH_PURCHASE_DETAILS = "inventory_health_trend_graph_purchase_details";
    public static final String INVENTORY_HEALTH_TREND_GRAPH_MOVE_DETAILS = "inventory_health_trend_graph_move_details";

    public static final String INVENTORY_HEALTH_INSTANCE_MODEL_CONFIG = "inventory_health_instance_model_config";

    public static final String INVENTORY_HEALTH_WEEK_N_ALGORITHM = "inventory_health_week_n_algorithm";

    public static final String INVENTORY_HEALTH_DELIVERY_DETAILS = "inventory_health_delivery_details";
}

