package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.CBSDeliveryData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ServiceLevelDataDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ServiceLevelSummaryDataDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualRangeReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualRangeResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.QueryServiceLevelReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.QueryInvDetailTypesReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.OperationViewSoeReq;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewReq;
import java.util.List;
import yunti.boot.web.jsonrpc.JsonrpcParam;

public interface InventoryHealthActualV2Service {
    /**
     * 查询库存健康度 实际库存，基于运营视图的逻辑取数
     */
    InventoryHealthActualResp queryInventoryHealthActualV2(InventoryHealthActualReq req);

    /**
     * 导出交付周期数据详情
     * @param req
     * @return
     */
    FileNameAndBytesDTO exportDeliveryDetailExcel(TrendGraphReq req);

    List<CBSDeliveryData> getAllCBSDeliveryData(String statTime, int spanNum);

    /**
     * 查询库存细类的字典
     */
    List<String> queryInvDetailTypes(QueryInvDetailTypesReq req);

    List<String> queryLineType(QueryInvDetailTypesReq req);

    List<String> queryMaterialType(QueryInvDetailTypesReq req);

    InventoryHealthActualRangeResp queryFullExcelData(@JsonrpcParam InventoryHealthActualRangeReq req);

    List<ServiceLevelDataDTO> queryServiceLevelData(QueryServiceLevelReq req);

    List<ServiceLevelSummaryDataDTO> queryServiceLevelSummaryData(QueryServiceLevelReq req);

    InventoryHealthTrendResp queryInventoryHealthTrend(InventoryHealthTrendReq req);
}
