package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import cloud.demand.lab.common.entity.BaseUserDO;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 产品供应看板备注
 */
@Data
@ToString
@Table("product_report_dim_remarks")
public class ProductReportDimRemarksDO extends BaseUserDO {

    /** 产品：CVM、CBS等<br/>Column: [product_category] */
    @Column(value = "product_category")
    private String productCategory;

    /** 版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 数据类型:采购-supply、需求-demand<br/>Column: [data_type] */
    @Column(value = "data_type")
    private String dataType;

    /** 维度值集合,用@分割,开头结尾都有@<br/>Column: [dim_key] */
    @Column(value = "dim_key")
    private String dimKey;

    /** 维度值集合，用@分割,开头结尾都有@。如果是总结，维度值不传<br/>Column: [dim_value] */
    @Column(value = "dim_value")
    private String dimValue;

    /** 备注<br/>Column: [remarks] */
    @Column(value = "remarks")
    private String remarks;

}