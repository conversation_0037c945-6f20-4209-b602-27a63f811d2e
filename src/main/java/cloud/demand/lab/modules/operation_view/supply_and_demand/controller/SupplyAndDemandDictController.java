package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;


import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.VersionReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.VersionData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@JsonrpcController("/supply-demand/dict")
public class SupplyAndDemandDictController {

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;


    @RequestMapping
    public List<VersionData> getAllVersion(@JsonrpcParam VersionReq req) {
        List<VersionData> ret = supplyAndDemandDictService.getAllVersion(req);
        return ret;
    }

    @RequestMapping
    public List<VersionData> getCbsDefinitiveVersion() {
        VersionReq req = new VersionReq();
        req.setDefinitive(true);
        req.setType("demand");
        req.setProductCategory(ProductCategoryEnum.CBS.getName());
        List<VersionData> ret = supplyAndDemandDictService.getAllVersion(req);
        return ret;
    }

    @RequestMapping
    public Boolean doDefinitive(@JsonrpcParam VersionReq req) {
        return supplyAndDemandDictService.doDefinitive(req);
    }


}
