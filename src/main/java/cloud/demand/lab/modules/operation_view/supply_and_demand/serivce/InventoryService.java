package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/18 14:53
 */
public interface InventoryService {

    List<String> queryParams(SupplyDemandParamTypeReq req);

    List<SupplyDemandHedgingItemVO> queryInventory(SupplyDemandHedgingReq req);
}
