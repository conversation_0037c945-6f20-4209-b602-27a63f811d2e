package cloud.demand.lab.modules.operation_view.operation_view_old.service.common.model;

import cloud.demand.lab.modules.operation_view.entity.rrp.ReportPlanDetailDO;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Table("report_plan_detail")
@Data
public class PlanDetailVO extends ReportPlanDetailDO {

    private String yearMonth;

    /**
     * 母机机型<br/>Column: [device_name]
     */
    private String deviceName;

    /**
     * 可用区名<br/>Column: [zone_name]
     */
    private String zoneName;

    /**
     * 地域名<br/>Column: [region_name]
     */
    private String regionName;

    /**
     * 地区名<br/>Column: [area_name]
     */
    private String areaName;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    private String customhouseTitle;

    /**
     * 好差呆类型，其中线下库存后处理
     */
    private String materialType;

    /**
     * 线上核心数
     */
    private BigDecimal onlineCores;

    /**
     * 线下核心数
     */
    private BigDecimal offlineCores;

    /**
     * 线上逻辑数
     */
    private BigDecimal onlineLogicNum;

    /**
     * 线下逻辑数
     */
    private BigDecimal offlineLogicNum;

    /**
     * 物理机单台核心数
     */
    private BigDecimal logicCpuCore;

    /**
     * 售卖核心数
     */
    private BigDecimal saleCores;

    /**
     * 售卖逻辑数
     */
    private BigDecimal saleLogicNum;

    public String getGroupK() {
        return String.join("-",
                yearMonth, deviceName, materialType, customhouseTitle, areaName, regionName, zoneName);
    }
}
