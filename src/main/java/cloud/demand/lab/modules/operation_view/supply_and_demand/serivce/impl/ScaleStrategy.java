package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandCommonReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.ScaleService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/4/23 18:30
 */
@Service
public class ScaleStrategy implements InitializingBean {

    @Resource
    private List<ScaleService> scaleServices;

    private final Map<String, ScaleService> scaleServiceMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        if (ListUtils.isEmpty(scaleServices)) {
            return;
        }
        // bean初始化完成后，扫描所有的inventoryTargetServices，注册进来
        for (ScaleService service : scaleServices) {
            scaleServiceMap.put(service.getProductCategory(), service);
        }
    }

    public List<DemandReportDetailVO> queryCurScaleData(DemandCommonReq req) {
        ScaleService service = getService(req.getProductCategory());
        return service.queryCurScaleData(req);
    }

    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req) {
        ScaleService service = getService(req.getProductCategory());
        return service.queryChangeScaleDataFromLastMonth(req);
    }

    public List<DemandReportDetailVO> queryChangeScaleDataFromLastMonth(DemandCommonReq req, List<String> notOtherCustomerUin, List<String> notOtherCommonCustomerShortName) {
         ScaleService service = getService(req.getProductCategory());
         return service.queryChangeScaleDataFromLastMonth(req, notOtherCustomerUin, notOtherCommonCustomerShortName);
    }

    private ScaleService getService(String productCategory) {
        ScaleService service = scaleServiceMap.get(productCategory);
        if (Objects.isNull(service)) {
            throw new BizException("未找到对应的productCategory:" + productCategory);
        }
        return service;
    }
}
