package cloud.demand.lab.modules.operation_view.operation_view_old.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum MonthAvgAlgorithmEnum {

    LAST_3_MONTH("LAST_3_MONTH", "最近3个月"),

    LAST_6_MONTH("LAST_6_MONTH", "最近半年"),

    LAST_12_MONTH("LAST_12_MONTH", "最近一年"),
    ;

    final private String code;
    final private String name;

    MonthAvgAlgorithmEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MonthAvgAlgorithmEnum getByCode(String code) {
        for (MonthAvgAlgorithmEnum e : MonthAvgAlgorithmEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        MonthAvgAlgorithmEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static List<String> getAllNames(){
        return ListUtils.transform(MonthAvgAlgorithmEnum.values(), o -> o.getName());
    }

}
