package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.excel.DotExcelReadUtil;
import cloud.demand.lab.common.excel.ExcelGroupEnum;
import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.checker.NotBlankColumnChecker;
import cloud.demand.lab.common.excel.checker.NotInStringListColumnChecker;
import cloud.demand.lab.common.excel.checker.NumberColumnChecker;
import cloud.demand.lab.common.excel.checker.RegexpColumnChecker;
import cloud.demand.lab.common.excel.checker.WhetherOrNotChecker;
import cloud.demand.lab.common.excel.core.ErrorMessage;
import cloud.demand.lab.common.excel.core.ReadResult;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.entity.web.common.StreamDownloadBean;
import cloud.demand.lab.modules.operation_view.operation_view.model.ReturnT;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataAdjustDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DownloadTemplateReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.ImportAdjustDemandReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandCvmImportVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ExportDemandItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ExportDemandVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandExcelService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/4/24 15:18
 */
@Service
@Slf4j
public class DemandCvmExcelServiceImpl implements DemandExcelService {

    @Resource
    private DemandService demandService;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;

    @Resource
    private DictService dictService;

    @Resource
    private FillerService fillerService;


    @Override
    public DownloadBean downloadTemplate(DownloadTemplateReq req) {
        DemandReq demandReq = new DemandReq();
        demandReq.setStatTime(req.getStatTime());
        List<String> dims = ListUtils.newArrayList("billNumber", "product", "industryDept", "warZone", "commonCustomerShortName", "customerUin", "projectName",
                "demandScene", "customhouseTitle", "countryName", "areaName", "regionName", "zoneCategory", "zoneName", "instanceCategory", "instanceGroup", "instanceType",
                "beginBuyDate", "endBuyDate", "demandType");
        demandReq.setDims(dims);
        demandReq.setStartYearMonth(req.getStatTime().substring(0, 7));
        demandReq.setAdjust(req.isAdjust());
        demandReq.setProductCategory(getProductCategory());
        demandReq.setUnit(UnitEnum.CORE.getName());
        List<DemandReportDetailVO> data = demandService.queryDemand(demandReq);
        InputStream template = IOUtils.readClasspathResourceInputStream(ExcelGroupEnum.PPL_AND_ORDER_CVM_DEMAND.getTemplatePath());

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template).registerConverter(new LocalDateStringConverter("yyyy-MM-dd")).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("需求校准").build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = getProductCategory() + "需求明细(" + (req.isAdjust() ? "人工校准" : "共识需求") + ")" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CVM.getName();
    }

    @Override
    public ResponseEntity<InputStreamResource> exportDemand(DemandReportReq req) {
        req.setNeedScale(false);
        req.setStatTimeList(ListUtils.newArrayList(req.getStartStatTime(), req.getEndStatTime()));
        req.setExcel(true);
        List<String> dims = ListUtils.newArrayList("yearMonth", "dataSource", "billNumber", "industryDept", "warZone", "commonCustomerShortName", "customerUin",
                "projectName", "demandScene", "customhouseTitle", "countryName", "areaName", "regionName",
                "zoneCategory", "zoneName", "instanceCategory", "instanceGroup", "instanceType", "beginBuyDate", "endBuyDate", "demandType");
        req.setDims(dims);

        List<String> demandCalibers = ListUtils.newArrayList("人工校准", "共识需求");
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();
            for (int i = 0; i < demandCalibers.size(); i++) {
                String demandCaliber = demandCalibers.get(i);
                req.setDemandCaliber(demandCaliber);
                List<DemandReportDetailVO> dataList = demandService.queryReport(req);
                List<ExportDemandItemVO> exportItemList = ExportDemandItemVO.builder(dataList, req.getStartStatTime(), req.getEndStatTime());

                List<String> headList = ListUtils.newArrayList("需求年月", "开始购买时间", "结束购买时间", "单据类型", "单号", "行业", "战区", "客户简称", "客户UIN",
                        "项目名称", "需求场景", "园区类型", "机型类型", "境内外", "国家", "区域", "地域", "可用区", "机型族", "实例", "需求类型",
                        "实例数量\n" + req.getStartStatTime(), "实例数量\n" + req.getEndStatTime(), "实例数量\n变化量", "核心数\n" + req.getStartStatTime(), "核心数\n" + req.getEndStatTime(), "核心数\n变化量");

                List<String> fieldNameList = ListUtils.newArrayList("yearMonth", "beginBuyDate", "endBuyDate", "dataSource", "billNumber", "industryDept", "warZone", "commonCustomerShortName", "customerUin",
                        "projectName", "demandScene", "zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName", "regionName", "zoneName", "instanceGroup", "instanceType", "demandType",
                        "startAmount1", "endAmount1", "diffAmount1", "startAmount2", "endAmount2", "diffAmount2");

                ExportDemandVO exportDemandVO = new ExportDemandVO().init(exportItemList, headList, fieldNameList);

                WriteSheet sheet = EasyExcel.writerSheet(i, demandCaliber).head(exportDemandVO.getHeadList())
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                writer.write(exportDemandVO.getDataList(), sheet);
            }
            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }
        String filename = String.format("产品供应看板-(CVM)需求明细-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx");
        return new StreamDownloadBean(filename, in);
    }

    @Override
    @SneakyThrows
    public ReturnT<List<ErrorMessage>> importAdjustDemand(MultipartFile file, ImportAdjustDemandReq req) {
        List<ProductDemandDataDfDO> sourceList = checkReq(req);

        DemandCvmImportVO demo = new DemandCvmImportVO();
        DotExcelReadUtil.DotExcelBuilder<DemandCvmImportVO> builder = DotExcelReadUtil.createBuilder(DemandCvmImportVO.class, ExcelGroupEnum.PPL_AND_ORDER_CVM_DEMAND, 2).inputStream(file.getInputStream());
        builder.registerValueCheckerByGetter(demo::getStatTime, new NotInStringListColumnChecker(ListUtils.newList(req.getVersionCode())));

        builder.registerValueCheckerByGetter(demo::getYearMonth, new NotBlankColumnChecker(), new RegexpColumnChecker("^\\d{4}-\\d{1,2}$"));
        // 声明可用区checker
        List<String> zoneNameList = dictService.queryZoneNameList();
        zoneNameList.add("随机可用区");
        NotInStringListColumnChecker zoneNameChecker = new NotInStringListColumnChecker(zoneNameList);
        builder.registerValueCheckerByGetter(demo::getZoneName, zoneNameChecker);

        // 声明配额数量范围checker
        NumberColumnChecker numberColumnChecker = new NumberColumnChecker(null, null);
        builder.registerValueCheckerByGetter(demo::getTotalAmount, numberColumnChecker);
        builder.registerValueCheckerByGetter(demo::getBuyAmount, numberColumnChecker);
        builder.registerValueCheckerByGetter(demo::getWaitBuyAmount, numberColumnChecker);

        builder.registerValueCheckerByGetter(demo::getIntervene, new WhetherOrNotChecker());

        // 声明实例类型checker
        List<String> instanceTypeList = dictService.queryInstanceType(null);
        NotInStringListColumnChecker instanceTypeChecker = new NotInStringListColumnChecker(instanceTypeList);
        builder.registerValueCheckerByGetter(demo::getInstanceType, instanceTypeChecker);


        ReadResult<DemandCvmImportVO> result = DotExcelReadUtil.read(builder);
        if (ListUtils.isEmpty(result.getErrors())) {
            if (ListUtils.isEmpty(result.getData())) {
                throw new BizException("导入配额数据为空，请检查excel");
            }
            // 数据导入
            List<ProductDemandDataAdjustDO> retList = transformToAdjustDO(result.getData(), sourceList, req.getVersionCode());
            demandService.saveAdjustDemand(retList, req.getVersionCode(), getProductCategory());
        }
        return ListUtils.isEmpty(result.getErrorsAndSort()) ? ReturnT.ok(result.getErrorsAndSort()) : ReturnT.fail(result.getErrorsAndSort());
    }

    private List<ProductDemandDataAdjustDO> transformToAdjustDO(List<DemandCvmImportVO> data, List<ProductDemandDataDfDO> sourceList, String statTime) {
        List<ProductDemandDataAdjustDO> ret = ListUtils.newArrayList();
        fillerService.fill(data);
        Map<String, ProductDemandDataDfDO> sourceMap = ListUtils.toMap(sourceList, ProductDemandDataDfDO::getBillNumber, Function.identity());
        for (DemandCvmImportVO item : data) {
            ProductDemandDataDfDO source = sourceMap.get(item.getBillNumber());
            if (Objects.isNull(source)) {
                ret.add(ProductDemandDataAdjustDO.transform(item));
            } else {
                ret.add(ProductDemandDataAdjustDO.transform(item, source));
            }
        }
        return ret;
    }
}
