package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class QueryRemarksReq {

    @NotEmpty(message = "产品不能为空")
    private List<String> productCategory;

    @NotEmpty(message = "dataType集合不能为空")
    private List<String> dataType;

    @NotEmpty(message = "版本集合不能空")
    private List<String> version;

    @NotEmpty(message = "维度key集合不能空")
    private List<String> dimKey = ListUtils.newArrayList();

    private List<String> dimValue = ListUtils.newArrayList();
}
