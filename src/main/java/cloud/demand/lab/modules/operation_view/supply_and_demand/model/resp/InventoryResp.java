package cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp;

import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplAndDemandInventoryTargetDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryItemVO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/26 11:39
 */
@Data
public class InventoryResp {
    private String statTime;

    private String zoneCategory;

    private String instanceCategory;

    private String customhouseTitle;

    private String countryName;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceGroup;

    private String instanceType;

    private Integer beginInventory;

    private Integer safetyInventoryCore;

    private Integer withholdInventoryCore;

    private List<Item> demand;

    private List<Item> supply;

    private List<Item> endInventory;

    private Integer targetInventory;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        private String yearMonth;

        private Integer totalCore;
    }

    public static List<InventoryResp> builder(List<InventoryItemVO> itemList, List<SupplAndDemandInventoryTargetDfDO> targetList, Boolean targetMatchFlag, List<String> yearMonth) {
        List<InventoryResp> respList = ListUtils.newArrayList();
        Map<String, Integer> targetMap = targetList.stream().collect(Collectors.toMap(item -> StringUtils.joinWith("@", item.getStatTime(), item.getInstanceType(), item.getRegionName()),
                SupplAndDemandInventoryTargetDfDO::getInventoryTarget, (k1, k2) -> k1));
        Map<String, List<InventoryItemVO>> outGroup = ListUtils.groupBy(itemList, item -> InventoryItemVO.getGroupKey(item));
        for (Map.Entry<String, List<InventoryItemVO>> outEntry : outGroup.entrySet()) {
            List<InventoryItemVO> innerList = outEntry.getValue();
            InventoryResp resp = new InventoryResp();
            BeanUtils.copyProperties(innerList.get(0), resp);
            Map<String, List<InventoryItemVO>> innerGroup = ListUtils.groupBy(innerList, InventoryItemVO::getType);
            //demand、supply、targetInventory、actualSafeInventory、actualInventory、withholdInventory
            for (Map.Entry<String, List<InventoryItemVO>> innerEntry : innerGroup.entrySet()) {
                InventoryItemVO itemVO = innerEntry.getValue().get(0);
                if (StringUtils.equals(innerEntry.getKey(), "withholdInventory")) {
                    resp.setWithholdInventoryCore(itemVO.getCore());
                } else if (StringUtils.equals(innerEntry.getKey(), "actualInventory")) {
                    resp.setBeginInventory(itemVO.getCore());
                } else if (StringUtils.equals(innerEntry.getKey(), "actualSafeInventory")) {
                    resp.setSafetyInventoryCore(itemVO.getCore());
                } else {
                    List<Item> respItem = ListUtils.transform(innerEntry.getValue(), item -> new Item(item.getYearMonth(), item.getCore()));
                    for (String itemYearMonth : yearMonth) {
                        if (ListUtils.isEmpty(respItem)) {
                            respItem.add(new Item(itemYearMonth, null));
                            continue;
                        }
                        if (!ListUtils.contains(respItem, item -> StringUtils.equals(itemYearMonth, item.getYearMonth()))) {
                            respItem.add(new Item(itemYearMonth, null));
                        }
                    }
                    respItem = respItem.stream().sorted(Comparator.comparing(item -> item.getYearMonth())).collect(Collectors.toList());
                    if (StringUtils.equals(innerEntry.getKey(), "demand")) {
                        resp.setDemand(respItem);
                    } else {
                        resp.setSupply(respItem);
                    }
                }
            }
            String key = StringUtils.joinWith("@", resp.getStatTime(), resp.getInstanceType(), resp.getRegionName());
            if (targetMatchFlag && targetMap.containsKey(key)) {
                resp.setTargetInventory(targetMap.get(key));
            }
            //如果demand为null
            if (ListUtils.isEmpty(resp.getDemand())) {
                List<Item> demand = ListUtils.newArrayList();
                for (String itemYearMonth : yearMonth) {
                    demand.add(new Item(itemYearMonth, null));
                }
                resp.setDemand(demand);
            }
            //如果supply为null
            if (ListUtils.isEmpty(resp.getSupply())) {
                List<Item> supply = ListUtils.newArrayList();
                for (String itemYearMonth : yearMonth) {
                    supply.add(new Item(itemYearMonth, null));
                }
                resp.setSupply(supply);
            }
            respList.add(resp);
        }
        //计算期末库存
        respList.forEach(item -> calEndInventory(item));
        return respList;
    }

    private static void calEndInventory(InventoryResp resp) {
        List<InventoryResp.Item> respItem = ListUtils.newArrayList();
        List<String> demandMonth = ListUtils.isEmpty(resp.getDemand()) ? ListUtils.newArrayList() : resp.getDemand().stream().map(item -> item.getYearMonth()).collect(Collectors.toList());
        List<String> supplyMonth = ListUtils.isEmpty(resp.getSupply()) ? ListUtils.newArrayList() : resp.getSupply().stream().map(item -> item.getYearMonth()).collect(Collectors.toList());
        List<String> endMonth = ListUtils.union(demandMonth, supplyMonth).stream().distinct().collect(Collectors.toList());

        for (String month : endMonth) {
            Integer beginInventoryCore = resp.getBeginInventory();
            Integer demandCore = getLessMonthCore(month, resp.getDemand());
            Integer supplyCore = getLessMonthCore(month, resp.getSupply());
            Integer endInventoryCore = addWithNull(subWithNull(beginInventoryCore, demandCore), supplyCore);
            respItem.add(new Item(month, endInventoryCore));
        }
        resp.setEndInventory(respItem);
    }

    private static Integer getLessMonthCore(String yearMonth, List<InventoryResp.Item> items) {
        if (ListUtils.isEmpty(items)) {
            return null;
        }
        return items.stream()
                .filter(item -> StringUtils.compare(item.getYearMonth(), yearMonth) <= 0)
                .filter(item -> Objects.nonNull(item.getTotalCore()))
                .map(item -> item.totalCore).reduce(0, Integer::sum);
    }

    public static Integer addWithNull(Integer v1, Integer v2) {
        if (v1 == null) {
            return v2;
        }
        if (v2 == null) {
            return v1;
        }
        return v1 + v2;
    }

    public static Integer subWithNull(Integer v1, Integer v2) {
        if (v2 == null) {
            return v1;
        }
        if (v1 == null) {
            return -v2;
        }
        return v1 - v2;
    }
}
