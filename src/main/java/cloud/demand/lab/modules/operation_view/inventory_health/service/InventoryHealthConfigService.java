package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryBufferPoolConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryInstanceTypeConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryServiceLevelConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.ConfigureInstanceTypeConfigReq;

public interface InventoryHealthConfigService {
    /**
     * 初始化主力可用区配置
     */
    void syncMainZoneNameConfig(Boolean isInitial);

    /**
     * 初始化主力机型配置
     */
    void syncMainInstanceTypeConfig(Boolean isInitial);

    /**
     * 配置目标服务水平
     * @return
     */
    Object configureTargetServiceLevel(ConfigureServiceLevelConfigReq req);

    /**
     *
     */
    Object importTargetServiceLevel(MultipartFile file, String date);


    /**
     * 配置可用区
     */
    Object configureZone(ConfigureZoneConfigReq req);

    Object importZone(MultipartFile file, String date);

    /**
     * 配置机型
     */
    Object configureInstanceType(ConfigureInstanceTypeConfigReq req);

    Object importInstanceType(MultipartFile file, String date);
    /**
     * 查询目标服务水平
     */
    Object queryTargetServiceLevel(QueryServiceLevelConfigReq req);

    /**
     * 通过 Excel 导出目标服务水平
     */
    FileNameAndBytesDTO exportTargetServiceLevel(String date);

    /**
     * 查询可用区配置
     */
    Object queryZoneConfig(QueryZoneConfigReq req);

    /**
     * 通过 Excel 导出可用区配置
     */
    FileNameAndBytesDTO exportZoneConfig(String date);

    /**
     * 查询机型配置
     */
    Object queryInstanceTypeConfig(QueryInstanceTypeConfigReq req);

    /**
     * 通过 Excel 导出机型配置
     */
    FileNameAndBytesDTO exportInstanceTypeConfig(String date);

    /**
     * 每日继承前一天的配置，做一个切片
     */
    void snapshotInventoryHealthConfig(String today);

    /**
     * 获取当前机型配置
     * @param today
     * @return
     */
    Map<String, List<String>> getInstanceTypeConfigMap(String today);

    /**
     * 机型 -> 二级类型
     */
    Map<String, String> getInstanceTypeToType2ConfigMap(String today);

    /**
     * 查询当前可用区配置
     */
    Map<String, List<String>> getZoneConfigMap(String today);

    /**
     * 可用区 -> 类型
     */
    Map<String, InventoryHealthMainZoneNameConfigDO> getZoneNameToTypeConfigMap(String today);

    /**
     * 根据：境内外+机型类型+可用区类型+计费类型查询对应的服务水平 map
     */
    Map<String, BigDecimal> queryTargetServiceLevel(String date);

    /**
     * 配置弹性备货池
     */
    Object configureBufferPool(ConfigureBufferPoolConfigReq req);

    /**
     * 导入弹性备货池
     */
    Object importBufferPool(MultipartFile file, String date);

    /**
     * 查询弹性备货池策略
     */
    Object queryBufferPoolConfig(QueryBufferPoolConfigReq req);

    /**
     * 通过 Excel 导出弹性配货池策略
     */
    FileNameAndBytesDTO exportBufferPoolConfig(String date);
}
