package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;
import org.springframework.beans.BeanUtils;

/**
 * ppl拼订单需求校正版本表
 */
@Data
@ToString
@Table("ppl_and_order_adjust_version_data")
public class PplAndOrderAdjustVersionDataDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "deleted")
    private Integer deleted;

    @Column(value = "create_time")
    private LocalDateTime createTime;

    @Column(value = "update_time")
    private LocalDateTime updateTime;

    /** 校正版本<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    @Column(value = "product_category")
    private String productCategory;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 机型族<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    /** 机型类型<br/>Column: [instance_category] */
    @Column(value = "instance_category")
    private String instanceCategory;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 可用区类型<br/>Column: [zone_category] */
    @Column(value = "zone_category")
    private String zoneCategory;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 年月<br/>Column: [demand_year_month] */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /** 待履约量<br/>Column: [wait_buy_total_num] */
    @Column(value = "wait_buy_total_num")
    private Integer waitBuyTotalNum;

    /** 退回量<br/>Column: [return_total_num] */
    @Column(value = "return_total_num")
    private Integer returnTotalNum;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 是否经过校正<br/>Column: [is_adjust] */
    @Column(value = "is_adjust")
    private Integer isAdjust;

    /** 校正后待履约量<br/>Column: [adjust_wait_buy_total_num] */
    @Column(value = "adjust_wait_buy_total_num")
    private Integer adjustWaitBuyTotalNum;

    /** 校正后退回量<br/>Column: [adjust_return_total_num] */
    @Column(value = "adjust_return_total_num")
    private Integer adjustReturnTotalNum;

    /** 备注<br/>Column: [remark] */
    @Column(value = "remark")
    private String remark;

}