package cloud.demand.lab.modules.operation_view.operation_view_old.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.Getter;
import org.nutz.lang.Lang;

@Getter
public enum BillingScaleGroupByEnum {

    statTime("statTime", "统计日期", "stat_time"),

    appId("appId", "appId", "app_id"),

    customerUin("customerUin", "uin", "customer_uin"),

    appRole("appRole", "appRole", "app_role"),

    industryDept("industryDept", "行业部门", "industry_dept"),

    customerShortName("customerShortName", "客户简称", "customer_short_name"),

    customerName("customerName", "客户名称", "customer_name"),

    customhouseTitle("customhouseTitle", "境内外", "customhouse_title"),

    areaName("areaName", "区域名", "area_name"),

    regionName("regionName", "Region", "region_name"),

    zoneName("zoneName", "可用区", "zone_name"),

    ginsFamily("ginsFamily", "机型族", "gins_family"),

    isDirectSellingCustomer("isDirectSellingCustomer", "是否直销用户", "is_direct_selling_customer"),

    isRenderingCustomer("isRenderingCustomer", "是否渲染用户", "is_rendering_customer"),

    isPersonalCustomer("isPersonalCustomer", "是否个人用户", "is_personal_customer"),

    orgLabel("orgLabel", "组织架构标签", "org_label"),

    demandType("demandType", "需求类型", "demand_type"),

    industryCategory("category", "行业分类", "industry_category"),

    isTurnoverCustomer("isTurnoverCustomer", "是否流转用户", "is_turnover_customer");


    private String code;
    private String name;
    private String column;

    BillingScaleGroupByEnum(String code, String name, String column) {
        this.code = code;
        this.name = name;
        this.column = column;
    }

    public static Set<String> getGroupByColumn(List<String> groupBys) {
        Set<String> columns = Lang.set();
        ListUtils.forEach(groupBys, o -> {
            BillingScaleGroupByEnum group = BillingScaleGroupByEnum.getByCode(o);
            if (group != null && StringTools.isNotBlank(group.getColumn())) {
                columns.add(group.getColumn());
            }
        });
        return columns;
    }

    public static BillingScaleGroupByEnum getByCode(String code) {
        for (BillingScaleGroupByEnum e : BillingScaleGroupByEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

}
