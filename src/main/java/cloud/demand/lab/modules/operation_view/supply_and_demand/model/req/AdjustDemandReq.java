package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;


import cloud.demand.lab.modules.operation_view.supply_and_demand.parse.DemandTypeParse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import report.utils.anno.WhereReport;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/10 20:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdjustDemandReq {

    @WhereReport(sql = " version_code = ? ")
    private String statTime;

    @WhereReport(sql = " demand_year_month >= ? ")
    private String startYearMonth;

    @WhereReport(sql = " demand_year_month <= ? ")
    private String endYearMonth;

    //@WhereReport
    private String productCategory;

    @WhereReport
    private List<String> product;

    @WhereReport
    private List<String> demandType;

    @WhereReport
    private List<String> industryDept;

    @WhereReport
    private List<String> warZone;

    @WhereReport(sql = " customer_short_name in (?) ")
    private List<String> commonCustomerShortName;

    @WhereReport
    private List<String> zoneCategory;

    @WhereReport
    private List<String> instanceCategory;

    @WhereReport(sql = " gins_family in (?) ")
    private List<String> instanceGroup;

    @WhereReport
    private List<String> instanceType;

    @WhereReport
    private List<String> customhouseTitle;

    @WhereReport(sql = " country in (?) ")
    private List<String> countryName;

    @WhereReport
    private List<String> areaName;

    @WhereReport
    private List<String> regionName;

    @WhereReport
    private List<String> zoneName;

    public static AdjustDemandReq transform(DemandReq req) {
        AdjustDemandReq ret = new AdjustDemandReq();
        BeanUtils.copyProperties(req, ret);
        return ret;
    }

}
