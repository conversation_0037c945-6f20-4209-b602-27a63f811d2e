package cloud.demand.lab.modules.operation_view.supply_and_demand.model.req;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.LinkedHashMap;
import java.util.List;

@Data
public class QueryChangeResultReq {

    @NotEmpty(message = "产品不能为空")
    private List<String> productCategory;

    @NotBlank(message = "开始版本不能为空")
    private String startVersion;

    @NotBlank(message = "结束版本不能为空")
    private String endVersion;

    private String startYearMonth;

    private String endYearMonth;

    @NotEmpty(message = "dataType集合不能为空")
    private List<String> dataType;

    @NotEmpty(message = "维度key集合不能空")
    private List<String> dimKey = ListUtils.newArrayList();

    @NotEmpty(message = "维度value集合不能空")
    private List<String> dimValue = ListUtils.newArrayList();

    /** 查询条件 */
    private LinkedHashMap<String,Object> queryData;
}
