package cloud.demand.lab.modules.operation_view.supply_and_demand.entity;


import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandMarketDTO;
import cloud.demand.lab.modules.order.service.filler.InstanceCategoryFiller;
import cloud.demand.lab.modules.order.service.filler.ZoneCategoryFiller;
import com.pugwoo.wooutils.lang.DateUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.function.Function;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString
@Table("cloud_demand.supply_on_the_way_data_df")
public class SupplyOnTheWayDataDfDO implements ZoneCategoryFiller, InstanceCategoryFiller {

    /**
     * 切片日期<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private String statTime;

    /**
     * 业务类型<br/>Column: [cloud_business_type]
     */
    @Column(value = "cloud_business_type")
    private String cloudBusinessType;

    /**
     * 预约单号<br/>Column: [quota_id]
     */
    @Column(value = "quota_id")
    private String quotaId;

    /**
     * 规划产品<br/>Column: [quota_plan_product_name]
     */
    @Column(value = "quota_plan_product_name")
    private String quotaPlanProductName;

    /**
     * 产品分类<br/>Column: [product_type]
     */
    @Column(value = "product_type")
    private String productType;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 设备类型<br/>Column: [quota_device_class]
     */
    @Column(value = "quota_device_class")
    private String quotaDeviceClass;


    /**
     * 实例类型<br/>Column: [instance_category]
     */
    @Column(value = "instance_category")
    private String instanceCategory;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "volume_type", insertValueScript = "'(空值)'")
    private String volumeType;

    /**
     * 机型族<br/>Column: [gin_family]
     */
    @Column(value = "gin_family")
    private String ginFamily;

    /**
     * 区域<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_category")
    private String zoneCategory;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;


    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country]
     */
    @Column(value = "country")
    private String country;

    /**
     * 需求region<br/>Column: [quota_region_name]
     */
    @Column(value = "quota_region_name")
    private String quotaRegionName;

    /**
     * 需求campus<br/>Column: [campus_name]
     */
    @Column(value = "campus_name")
    private String campusName;

    /**
     * 当前匹配campus<br/>Column: [campus]
     */
    @Column(value = "campus")
    private String campus;

    /**
     * 当前匹配module<br/>Column: [module]
     */
    @Column(value = "module")
    private String module;

    /**
     * 共识期望交付时间<br/>Column: [quota_use_time]
     */
    @Column(value = "quota_use_time")
    private String quotaUseTime;

    /**
     * cloud提单时间
     */
    @Column("quota_create_time")
    private String quotaCreateTime;

    /**
     * 共识期望交付月份<br/>Column: [quota_use_month]
     */
    @Column(value = "quota_use_month")
    private Integer quotaUseMonth;

    /**
     * 预计货期<br/>Column: [est_arrive_date]
     */
    @Column(value = "est_arrive_date")
    private String estArriveDate;

    /**
     * 校准后机位时间<br/>Column: [adjust_pos_datetime]
     */
    @Column(value = "adjust_pos_datetime")
    private String adjustPosDatetime;

    /**
     * 校准后交付时间<br/>Column: [adjust_sla_date]
     */
    @Column(value = "adjust_sla_date")
    private String adjustSlaDate;

    /**
     * 交付月份<br/>Column: [sla_date_month]
     */
    @Column(value = "sla_date_month")
    private String slaDateMonth;

    /**
     * 校准后交付月份<br/>Column: [adjust_sla_date_month]
     */
    @Column(value = "adjust_sla_date_month")
    private String adjustSlaDateMonth;

    /**
     * 交付卡点<br/>Column: [delivery_point]
     */
    @Column(value = "delivery_point")
    private String deliveryPoint;

    /**
     * 是否有风险，校准交付时间-共识期望交付时间>7则显示是，反之否<br/>Column: [gap]
     */
    @Column(value = "gap")
    private Integer gap;

    /**
     * 预计交付日期<br/>Column: [sla_date_expect]
     */
    @Column(value = "sla_date_expect")
    private String slaDateExpect;

    /**
     * 生产状态<br/>Column: [produce_status]
     */
    @Column(value = "produce_status")
    private String produceStatus;

    /**
     * 交付状态<br/>Column: [is_delivery]
     */
    @Column(value = "is_delivery")
    private String isDelivery;

    /**
     * 行业<br/>Column: [xy_industry]
     */
    @Column(value = "xy_industry")
    private String xyIndustry;

    /**
     * 客户名称<br/>Column: [xy_customer_name]
     */
    @Column(value = "xy_customer_name")
    private String xyCustomerName;

    /**
     * 机位预启用日期<br/>Column: [pos_pre_start_datetime]
     */
    @Column(value = "pos_pre_start_datetime")
    private String posPreStartDatetime;

    @Column(value = "cloud_delivery_time")
    private String cloudDeliveryTime;

    /**
     * 是否假机位<br/>Column: [is_fake_position]
     */
    @Column(value = "is_fake_position")
    private String isFakePosition;

    /**
     * cpu逻辑核<br/>Column: [cpu_logic_core]
     */
    @Column(value = "cpu_logic_core", insertValueScript = "0")
    private BigDecimal cpuLogicCore;

    /**
     * cpu台数<br/>Column: [cpu_num]
     */
    @Column(value = "cpu_num", insertValueScript = "0")
    private BigDecimal cpuNum;

    /**
     * 总核数<br/>Column: [total_core]
     */
    @Column(value = "total_core", insertValueScript = "0")
    private BigDecimal totalCore;

    /**
     * GPU卡数
     */
    @Column(value = "gpu_num", insertValueScript = "0")
    private BigDecimal gpuNum;

    /**
     * 逻辑容量
     */
    @Column(value = "logic_capacity", insertValueScript = "0")
    private BigDecimal logicCapacity;

    /**
     * 内存<br/>Column: [memory]
     */
    @Column(value = "memory", insertValueScript = "0")
    private BigDecimal memory;

    private BigDecimal singleLogicCapacity;

    /**
     * 校准备注<br/>Column: [adjust_remark]
     */
    @Column(value = "adjust_remark")
    private String adjustRemark;

    @Column(value = "delivery_status")
    private String deliveryStatus;

    @Column(value = "project_name")
    private String projectName;

    public BigDecimal getMemoryAmount() {
        //TB 转换为GB
        return memory.multiply(new BigDecimal(1024));
    }

    public BigDecimal getLogicCapacityAmount() {
        //TB 转换为GB
        return logicCapacity.multiply(new BigDecimal(1024));
    }


    public static SupplyOnTheWayDataDfDO genSupplyDataByMarket(DemandMarketDTO market, String statTime) {
        SupplyOnTheWayDataDfDO supplyOnTheWayDataDfDO = new SupplyOnTheWayDataDfDO();
        supplyOnTheWayDataDfDO.setStatTime(statTime);
        supplyOnTheWayDataDfDO.setCloudBusinessType(market.getCloudBusinessType());
        supplyOnTheWayDataDfDO.setQuotaId(market.getQuotaId());
        supplyOnTheWayDataDfDO.setQuotaPlanProductName(market.getQuotaPlanProductName());
        supplyOnTheWayDataDfDO.setQuotaDeviceClass(market.getQuotaDeviceClass());
        supplyOnTheWayDataDfDO.setCampusName(market.getQuotaCampusName());
        supplyOnTheWayDataDfDO.setCampus(market.getCampus());
        supplyOnTheWayDataDfDO.setQuotaUseTime(market.getQuotaUseTime());
        supplyOnTheWayDataDfDO.setModule(market.getModule());
        supplyOnTheWayDataDfDO.setQuotaRegionName(market.getQuotaRegionName());
        supplyOnTheWayDataDfDO.setQuotaCreateTime(market.getQuotaCreateTime());
        supplyOnTheWayDataDfDO.setCloudDeliveryTime(market.getCloudDeliveryTime());
        supplyOnTheWayDataDfDO.setProjectName(market.getProjectName());
        if (StringUtils.isNotBlank(supplyOnTheWayDataDfDO.getQuotaUseTime())) {
            LocalDate localDate = DateUtils.parseLocalDate(supplyOnTheWayDataDfDO.getQuotaUseTime());
            supplyOnTheWayDataDfDO.setQuotaUseMonth(localDate.getMonthValue());
            supplyOnTheWayDataDfDO.setSlaDateMonth(YearMonth.of(localDate.getYear(), localDate.getMonthValue()).toString());
        }
        supplyOnTheWayDataDfDO.setEstArriveDate(market.getEstArriveDate());
        supplyOnTheWayDataDfDO.setSlaDateExpect(market.getSlaDateExpect());
        supplyOnTheWayDataDfDO.setProduceStatus(market.getProduceStatus());
        supplyOnTheWayDataDfDO.setIsDelivery(market.getIsDelivery());
        supplyOnTheWayDataDfDO.setXyIndustry(market.getXyIndustry());
        supplyOnTheWayDataDfDO.setXyCustomerName(market.getXyCustomerName());
        supplyOnTheWayDataDfDO.setPosPreStartDatetime(market.getPosPreStartDateTime());
        supplyOnTheWayDataDfDO.setIsFakePosition(market.getIsFakePosition());
        supplyOnTheWayDataDfDO.setCpuLogicCore(market.getCpuLogicCore());
        supplyOnTheWayDataDfDO.setCpuNum(market.getNum());
        supplyOnTheWayDataDfDO.setTotalCore(market.getTotalCore());
        supplyOnTheWayDataDfDO.setGpuNum(market.getGpuNum() == null ? BigDecimal.ZERO : market.getGpuNum());
        //设置交付卡点
        LocalDate posPre = DateUtils.parseLocalDate(supplyOnTheWayDataDfDO.getPosPreStartDatetime());
        LocalDate estArrive = DateUtils.parseLocalDate(supplyOnTheWayDataDfDO.getEstArriveDate());
        if (checkIsAbnormalDate(posPre)) {
            supplyOnTheWayDataDfDO.setDeliveryPoint("机位");
        } else if (!checkIsAbnormalDate(posPre) && checkIsAbnormalDate(estArrive)) {
            supplyOnTheWayDataDfDO.setDeliveryPoint("服务器");
        } else {
            supplyOnTheWayDataDfDO.setDeliveryPoint(posPre.isAfter(estArrive) ? "机位" : "服务器");
        }
        supplyOnTheWayDataDfDO.setDeliveryStatus(market.getDeliveryStatus());
        return supplyOnTheWayDataDfDO;
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public String provideCustomhouseTitle() {
        return this.customhouseTitle;
    }

    @Override
    public void fillInstanceCategory(String instanceCategory) {
        this.instanceCategory = instanceCategory;
    }

    @Override
    public String provideZoneName() {
        return this.getZoneName();
    }

    @Override
    public void fillZoneCategory(String zoneCategory) {
        this.zoneCategory = zoneCategory;
    }

    public static boolean checkIsAbnormalDate(LocalDate date) {
        return date == null || date.toString().equals("1900-01-01");
    }

    public static Function<SupplyOnTheWayDataDfDO, BigDecimal> amountGetter(String unit) {
        Function<SupplyOnTheWayDataDfDO, BigDecimal> getter = null;
        switch (unit) {
            case "台":
                getter = SupplyOnTheWayDataDfDO::getCpuNum;
                break;
            case "逻辑核":
                getter = SupplyOnTheWayDataDfDO::getTotalCore;
                break;
            case "逻辑容量":
                getter = SupplyOnTheWayDataDfDO::getLogicCapacityAmount;
                break;
            case "卡":
                getter = SupplyOnTheWayDataDfDO::getGpuNum;
                break;
            case "逻辑内存量":
                getter = SupplyOnTheWayDataDfDO::getMemoryAmount;
                break;
            default:
                getter = SupplyOnTheWayDataDfDO::getTotalCore;
        }
        return getter;
    }
}
