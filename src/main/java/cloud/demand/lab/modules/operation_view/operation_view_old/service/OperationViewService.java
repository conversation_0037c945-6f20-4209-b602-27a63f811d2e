package cloud.demand.lab.modules.operation_view.operation_view_old.service;

import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewReq;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewRsp;
import java.util.Map;

/**
 * 运营视图Service
 */
public interface OperationViewService {


    /**
     * 获得SLA，天数
     * @param deviceType 设备类型
     * @param isMainland 是否国内
     */
    Integer getSLA(String deviceType, boolean isMainland);

    /**
     * 根据机型大类获得SLA，天数
     * @param instanceType 注意：一个机型可以对应多个物理机，当有多个对应时，取多个物理机型的SLA平均值
     * @param isMainland
     * @return 不会返回null
     */
    Integer getSLAByInstanceType(String instanceType, boolean isMainland);


    /**
     * 根据机型大类获得SLA，天数
     * @param isMainland
     * @return 全部机型的 SLA
     */
    Map<String,Integer> getAllSLA(boolean isMainland);

    /**
     * 默认 SLA
     * @param isMainland
     * @return
     */
    Integer getDefSLA(boolean isMainland);

}

