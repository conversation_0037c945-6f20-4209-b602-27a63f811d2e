package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.DateUtils;
import cloud.demand.lab.modules.operation_view.entity.web.common.StreamDownloadBean;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.PplProductEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandReportDetailVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DemandReportReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.ExportInventoryRespVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.InventoryTargetReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingOverviewResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyOnTheWayDetailData;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyReportDetailReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.resp.SupplyDemandHedgingResp;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.InventoryTargetContext;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyDemandHedgingService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.DemandService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyOnTheWayReportService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.util.PageQueryUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/26 14:02
 */
@Slf4j
@Service
public class SupplyDemandHedgingServiceImpl implements SupplyDemandHedgingService {

    @Resource
    private DemandService demandService;

    @Resource
    private SupplyOnTheWayReportService supplyService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private SafeInventoryStrategy safeInventoryContext;

    @Resource
    private InventoryTargetStrategy inventoryTargetContext;

    @Resource
    private ScaleStrategy scaleStrategy;

    @Override
    public List<SupplyDemandHedgingResp> queryReport(SupplyDemandHedgingReq req) {
        List<String> yearMonth = listYearMonth(req.getStartYearMonth(), req.getEndYearMonth());
        return doQueryReport(req, yearMonth);
    }

    @Override
    public List<String> queryParams(SupplyDemandParamTypeReq req) {
        List<String> inventoryList = inventoryService.queryParams(req);
        List<String> demandList = demandService.queryParams(req);
        List<String> supplyList = supplyService.queryParams(req);
        return ListUtils.union(inventoryList, supplyList, demandList).stream().distinct().collect(Collectors.toList());
    }

    @Override
    public ResponseEntity<InputStreamResource> exportInventory(SupplyDemandHedgingReq req) {

        List<String> yearMonth = listYearMonth(req.getStartYearMonth(), req.getEndYearMonth());
        List<SupplyDemandHedgingResp> dataList = doQueryReport(req, yearMonth);

        ExportInventoryRespVO exportData = ExportInventoryRespVO.build(req.getProductCategory(), dataList, yearMonth);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(exportData.getHeadList()).sheet("供需明细")
                    .doWrite(exportData.getDataList());
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }
        String filename = String.format("产品供应看板-供需明细(" + req.getProductCategory() + ")-" + com.pugwoo.wooutils.lang.DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx");
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public List<SupplyDemandHedgingOverviewResp> queryInventoryOverview(SupplyDemandHedgingReq req) {
        List<SupplyDemandHedgingResp> list = queryReport(req);
        return ListUtils.transform(list, SupplyDemandHedgingOverviewResp::transform);
    }

    public List<SupplyDemandHedgingResp> doQueryReport(SupplyDemandHedgingReq req, List<String> yearMonth) {
        List<List<SupplyDemandHedgingItemVO>> allList = PageQueryUtils.waitAll(30,
                () -> queryDemand(req),
                () -> queryScale(req),
                () -> querySupply(req),
                () -> inventoryService.queryInventory(req),
                () -> safeInventoryContext.querySafeInventory(req, req.getProductCategory())
        );
        List<SupplyDemandHedgingItemVO> itemList = allList.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        //目标库存
        InventoryTargetReq targetReq = InventoryTargetReq.transform(req);
        InventoryTargetContext targetContext = inventoryTargetContext.initContext(targetReq);
        List<SupplyDemandHedgingResp> ret = SupplyDemandHedgingResp.builder(req.getProductCategory(), itemList, targetContext, yearMonth);

        return ret;
    }


    private List<SupplyDemandHedgingItemVO> queryDemand(SupplyDemandHedgingReq req) {
        //需求
        DemandReportReq demandReportReq = DemandReportReq.transform(req);
        demandReportReq.setDims(ListUtils.newArrayList("zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName",
                "regionName", "zoneName", "instanceGroup", "instanceType", "volumeType", "projectType"));
        List<DemandReportDetailVO> demandList = demandService.queryReport(demandReportReq);

        List<SupplyDemandHedgingItemVO> itemList = ListUtils.transform(demandList, SupplyDemandHedgingItemVO::transform);

        List<String> mergeDims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type", "yearMonth"));

        return SupplyDemandHedgingItemVO.mergeList(itemList, mergeDims);
    }

    private List<SupplyDemandHedgingItemVO> queryScale(SupplyDemandHedgingReq req) {
        //
        DemandReportReq demandReportReq = DemandReportReq.transform(req);
        demandReportReq.setDims(ListUtils.newArrayList("zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName",
                "regionName", "zoneName", "instanceGroup", "instanceType", "volumeType"));
        List<DemandReportDetailVO> demandList = scaleStrategy.queryCurScaleData(demandReportReq);

        List<SupplyDemandHedgingItemVO> itemList = ListUtils.transform(demandList, SupplyDemandHedgingItemVO::transform);

        itemList = itemList.stream().filter(item -> StringUtils.equals(item.getYearMonth(), req.getStartYearMonth())).collect(Collectors.toList());

        List<SupplyDemandHedgingItemVO> ret = ListUtils.newArrayList();
        for (SupplyDemandHedgingItemVO item : itemList) {
            for (String statTime : demandReportReq.getStatTimeList()) {
                SupplyDemandHedgingItemVO newItem = new SupplyDemandHedgingItemVO();
                BeanUtils.copyProperties(item, newItem);
                newItem.setStatTime(statTime);
                ret.add(newItem);
            }

        }

        List<String> mergeDims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type"));

        return SupplyDemandHedgingItemVO.mergeList(ret, mergeDims);
    }

    private List<SupplyDemandHedgingItemVO> querySupply(SupplyDemandHedgingReq req) {
        //采购
        SupplyReportDetailReq supplyReportDetailReq = SupplyReportDetailReq.transform(req);
        if (ListUtils.isNotEmpty(supplyReportDetailReq.getProductType()) && supplyReportDetailReq.getProductType().get(0).equals("数据库")) {
            SupplyDemandParamTypeReq paramReq = new SupplyDemandParamTypeReq();
            paramReq.setParamType("projectName");
            paramReq.setProductCategory("数据库");
            List<String> projectList = supplyService.queryParams(paramReq);
            projectList = projectList.stream().filter(o -> !o.equals("轻量云徙") && !o.contains("裁撤")).collect(Collectors.toList());
            supplyReportDetailReq.setProjectName(projectList);
        }
        supplyReportDetailReq.setDims(ListUtils.newArrayList("statTime", "slaMonth", "zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName",
                "regionName", "zoneName", "instanceGroup", "instanceType", "volumeType"));
        List<SupplyOnTheWayDetailData> supplyList = supplyService.querySupplyOnTheWayDetailReport(supplyReportDetailReq)
                .stream().filter(item -> !StringUtils.equals("未知", item.getSlaMonth())).collect(Collectors.toList());

        List<SupplyDemandHedgingItemVO> itemList = ListUtils.transform(supplyList, SupplyDemandHedgingItemVO::transform);

        List<String> mergeDims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type", "yearMonth"));

        return SupplyDemandHedgingItemVO.mergeList(itemList, mergeDims);

    }


    private List<String> listYearMonth(String fromYearMonth, String toYearMonth) {
        List<String> ret = ListUtils.newArrayList();
        List<DateUtils.YearMonth> list = DateUtils.listYearMonth(fromYearMonth, toYearMonth);
        for (DateUtils.YearMonth item : list) {
            String month = item.getMonth() < 10 ? "-0" + item.getMonth() : "-" + item.getMonth();
            String yearMonth = item.getYear() + month;
            ret.add(yearMonth);
        }
        return ret;
    }
}
