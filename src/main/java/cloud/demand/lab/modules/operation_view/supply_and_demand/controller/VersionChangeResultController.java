package cloud.demand.lab.modules.operation_view.supply_and_demand.controller;

import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.CreateChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.DeleteChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.QueryChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyAndDemandVersionChangeResultVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.UpdateChangeResultReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandVersionChangeResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/15 14:15
 */
@Slf4j
@JsonrpcController("/supply-demand/change-result")
public class VersionChangeResultController {

    @Resource
    private SupplyAndDemandVersionChangeResultService changeResultService;


    /**
     * 版本变化原因
     *
     * @param req 请求参数
     * @return 返回的原因组
     */
    @RequestMapping
    public List<SupplyAndDemandVersionChangeResultVO> queryChangeResult(@JsonrpcParam QueryChangeResultReq req){
        return changeResultService.queryChangeResult(req);
    }

    /**
     * 创建版本变化原因
     *
     * @param req 请求参数
     */
    @RequestMapping
    public void createChangeResult(@JsonrpcParam CreateChangeResultReq req){
        changeResultService.createChangeResult(req);
    }

    /**
     * 更新版本变化原因
     *
     * @param req 请求参数
     */
    @RequestMapping
    public void updateChangeResult(@JsonrpcParam UpdateChangeResultReq req){
        changeResultService.updateChangeResult(req);
    }

    /**
     * 通过id主键组删除版本变化原因
     *
     */
    @RequestMapping
    public void deleteChangeResult(@JsonrpcParam DeleteChangeResultReq req){
        changeResultService.deleteChangeResult(req.getIds());
    }
}
