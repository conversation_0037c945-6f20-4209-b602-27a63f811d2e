package cloud.demand.lab;

import cloud.demand.lab.common.filter.weblog.CommonWebLogFilter;
import com.pugwoo.dbhelper.DBHelper;
import javax.annotation.Resource;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;


@SpringBootApplication
public class Application {

    /**
     * Spring上下文
     */
    public static ConfigurableApplicationContext context;

    public static void main(String[] args) {
        context = SpringApplication.run(Application.class, args);
    }

    @Resource
    private DBHelper demandDBHelper;
    @Bean
    public CommonWebLogFilter webLogFilter() {
        return new CommonWebLogFilter(demandDBHelper);
    }
}


