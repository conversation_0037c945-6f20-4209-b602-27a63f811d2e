package cloud.demand.lab.common.db_check;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SaveRunningSqlTest {

    @Resource
    SaveRunningSql  saveRunningSql;

    @Test
    void saveRunningSql() {
        saveRunningSql.saveRunningSql();
    }
}