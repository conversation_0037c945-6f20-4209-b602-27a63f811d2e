package cloud.demand.lab.modules.longterm.predict;

import cloud.demand.lab.modules.longterm.predict.service.HistoryAndPredictTrendService;
import cloud.demand.lab.modules.longterm.predict.web.req.history_and_predict_trend.*;
import cloud.demand.lab.modules.longterm.predict.web.resp.history_and_predict_trend.ScaleHistoryAndPredictDictResp;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class HistoryAndPredictTrendServiceTests {
    @Resource
    private HistoryAndPredictTrendService scaleHistoryAndPredictService;

    @Test
    public void testTotal(){
        ScaleHistoryAndPredictTotalReq req= new ScaleHistoryAndPredictTotalReq();
        req.setTaskId(121L);
        req.setSplitVersionId(1L);
        scaleHistoryAndPredictService.scaleHistoryAndPredictTotal(req);
    }

    @Test
    public void testdict(){
        LongtermTaskIdReq req = new LongtermTaskIdReq();
        req.setTaskId(23L);
        ScaleHistoryAndPredictDictResp resp = scaleHistoryAndPredictService.getScaleHistoryAndPredictDict(req);
        System.out.println(JSON.toJSON(resp));
    }

    @Test
    public void testscale(){
        ScaleHistoryReq req = new ScaleHistoryReq();
        req.setTaskId(23L);
        req.setDims("instanceFamily");
        req.setStartYearmonth("2024-01");
        req.setEndYearmonth("2024-03");
        List<String> coutry = new ArrayList<>();
        coutry.add("加拿大");
        coutry.add("上海");
        //req.setRegionOrCountry(coutry);
        scaleHistoryAndPredictService.getScaleHistory(req);
    }

    @Test
    public void testPredict(){
        ScaleHistoryPredictReq req = new ScaleHistoryPredictReq();
        req.setTaskId(34L);
        req.setSplitVersionId(65L);
        req.setStartYearmonth("2024-09");
        req.setEndYearmonth("2024-11");
        List<String> coutry = new ArrayList<>();
        coutry.add("加拿大");
        coutry.add("上海");
        //req.setRegionOrCountry(coutry);
        req.setDims("yearMonth");
        scaleHistoryAndPredictService.scaleHistoryPredict(req);
    }

    @Test
    public void testHistoryMonth(){
        ScaleHistoryAndPredictYearMonthReq req = new ScaleHistoryAndPredictYearMonthReq();
        req.setTaskId(31L);
        scaleHistoryAndPredictService.getScaleHistoryAndPredictYearMonth(req);
    }
}
