package cloud.demand.lab.modules.longterm.predict;

import cloud.demand.lab.modules.longterm.predict.service.LongtermPredictArgsTrendService;
import com.pugwoo.wooutils.lang.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class LongtermPredictArgsTrendServiceTests {

    @Autowired
    private LongtermPredictArgsTrendService longtermPredictArgsTrendService;

    @Test
    public void test() {
        longtermPredictArgsTrendService.doCalArgs(1L, DateUtils.parseLocalDate("2024-08-31"));
    }
}
