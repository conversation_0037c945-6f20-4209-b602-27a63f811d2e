package cloud.demand.lab.modules.longterm.predict;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermDecisionPredictService;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermPredictService;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.InputArgsDTO;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.operation_view.entity.web.dict.QueryCampusAndBizType2ZonePageDTO;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class CreateLongtermPredictServiceTests {

    @Resource
    private CreateLongtermPredictService createLongtermPredictService;
    @Resource
    private CreateLongtermDecisionPredictService createLongtermDecisionPredictService;
    @Resource
    private DictService dictService;

    @Test
    public void testCampusToCvmZone() {
        Map<String, StaticZoneDO> campus2ZoneInfoMap = dictService.getCampus2ZoneInfoMap();
        System.out.println(campus2ZoneInfoMap.get("沙特阿拉伯利雅得PDC-利雅得"));
        System.out.println(campus2ZoneInfoMap);
    }

    @Test
    public void testCampusToCvmZone2() {
        QueryCampusAndBizType2ZonePageDTO map = dictService.getCampusAndBizType2ZoneInfosPage();
        System.out.println(map);
    }

    @Test
    public void testCategoryList() {
        QueryCategoryForCreateResp resp = createLongtermPredictService.queryCategoryForCreate(new QueryCategoryForCreateReq());
        System.out.println(JSON.toJson(resp));
    }

    @Test
    public void testCreate() {
        CreatePredictTaskReq req = new CreatePredictTaskReq();
        req.setCategoryId(1L);
        req.setIsEnable(true);

        List<InputArgsDTO> inputArgs = new ArrayList<>();
        req.setInputArgs(inputArgs);

        List<LongtermPredictInputArgsDO.Attachment> attachment = new ArrayList<>();

        LongtermPredictInputArgsDO.Attachment att1 = new LongtermPredictInputArgsDO.Attachment();
        att1.setFilename("a.txt");
        att1.setUrl("https://www.baidu.com");
        attachment.add(att1);

        LongtermPredictInputArgsDO.Attachment att2 = new LongtermPredictInputArgsDO.Attachment();
        att2.setFilename("b.txt");
        att2.setUrl("https://www.qq.com");
        attachment.add(att2);

        // 2024下半年
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.EXTREME.getCode())
                .dateName("2024下半年").startDate("2024-07-01").endDate("2024-12-31")
                .scaleGrowthRate(new BigDecimal("0.082"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.MIDDLE.getCode())
                .dateName("2024下半年").startDate("2024-07-01").endDate("2024-12-31")
                .scaleGrowthRate(new BigDecimal("0.082"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.CAUTIOUS.getCode())
                .dateName("2024下半年").startDate("2024-07-01").endDate("2024-12-31")
                .scaleGrowthRate(new BigDecimal("0.082"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());

        // 2025上半年
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.EXTREME.getCode())
                .dateName("2025上半年").startDate("2025-01-01").endDate("2025-06-30")
                .scaleGrowthRate(new BigDecimal("0.092"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.MIDDLE.getCode())
                .dateName("2025上半年").startDate("2025-01-01").endDate("2025-06-30")
                .scaleGrowthRate(new BigDecimal("0.08"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.CAUTIOUS.getCode())
                .dateName("2025上半年").startDate("2025-01-01").endDate("2025-06-30")
                .scaleGrowthRate(new BigDecimal("0.06"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());

        // 2025下半年
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.EXTREME.getCode())
                .dateName("2025下半年").startDate("2025-07-01").endDate("2025-12-31")
                .scaleGrowthRate(new BigDecimal("0.102"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.MIDDLE.getCode())
                .dateName("2025下半年").startDate("2025-07-01").endDate("2025-12-31")
                .scaleGrowthRate(new BigDecimal("0.08"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.CAUTIOUS.getCode())
                .dateName("2025下半年").startDate("2025-07-01").endDate("2025-12-31")
                .scaleGrowthRate(new BigDecimal("0.06"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());

        // 2026上半年
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.EXTREME.getCode())
                .dateName("2026上半年").startDate("2026-01-01").endDate("2026-06-30")
                .scaleGrowthRate(new BigDecimal("0.102"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.MIDDLE.getCode())
                .dateName("2026上半年").startDate("2026-01-01").endDate("2026-06-30")
                .scaleGrowthRate(new BigDecimal("0.08"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());
        inputArgs.add(InputArgsDTO.builder()
                .strategyType(StrategyTypeEnum.CAUTIOUS.getCode())
                .dateName("2026上半年").startDate("2026-01-01").endDate("2026-06-30")
                .scaleGrowthRate(new BigDecimal("0.06"))
                .replaceRate(new BigDecimal("0.14"))
                .purchaseRate(BigDecimal.ONE)
                .note("备注123").attachment(attachment)
                .build());

        CreatePredictTaskResp resp = createLongtermPredictService.createPredictTask(req);

        System.out.println(JSON.toJson(resp));
    }

    @Test
    public void testRun() {
        Long taskId = 342L;
        createLongtermPredictService.doRunTask(taskId);
    }

    @Test
    public void testRunDecision() {
        Long taskId = 256L;
        createLongtermDecisionPredictService.doRunTask(taskId);
    }

}
