package cloud.demand.lab.modules.longterm.predict.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitAdjustDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.service.TransToAnnualService;
import com.pugwoo.dbhelper.DBHelper;
import java.util.List;
import javax.annotation.Resource;
import org.apache.poi.ss.formula.functions.T;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class TransToAnnualServiceImplTest {

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private TransToAnnualServiceImpl transToAnnualService;

    @Test
    void tranFrom() {
        List<LongtermPredictOutputPurchaseSplitDO> data =
                cdLabDbHelper.getAll(LongtermPredictOutputPurchaseSplitDO.class, "where task_id=256");
        List<LongtermPredictOutputPurchaseSplitAdjustDO> longtermPredictOutputPurchaseSplitAdjustDOS
                = transToAnnualService.tranFrom( data);
        cdLabDbHelper.insertBatchWithoutReturnId(longtermPredictOutputPurchaseSplitAdjustDOS);
    }
}