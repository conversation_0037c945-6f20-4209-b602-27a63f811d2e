package cloud.demand.lab.modules.longterm.cos;

import cloud.demand.lab.modules.longterm.cos.algorithm.ARIMA;
import cloud.demand.lab.modules.longterm.cos.algorithm.DateNumDTO;
import cloud.demand.lab.modules.longterm.cos.algorithm.PredictResult;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ARIMATests {

    @Test
    public void test() {

        // debug时，请设置 ARIMA 这一行String arimaUrl为测试环境的ip地址，例如：String arimaUrl ="http://*************";

        List<DateNumDTO> data = new ArrayList<>();

        data.add(new DateNumDTO("2021-02-01", new BigDecimal("100")));
        data.add(new DateNumDTO("2021-02-02", new BigDecimal("200")));
        data.add(new DateNumDTO("2021-02-03", new BigDecimal("300")));
        data.add(new DateNumDTO("2021-02-04", new BigDecimal("400")));
        data.add(new DateNumDTO("2021-02-05", new BigDecimal("500")));

        PredictResult predict = ARIMA.predict(data, 600, ListUtils.of(0, 2, 3));

        System.out.println(predict);


    }

}
