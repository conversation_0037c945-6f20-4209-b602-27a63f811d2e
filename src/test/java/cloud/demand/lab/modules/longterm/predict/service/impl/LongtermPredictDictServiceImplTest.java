package cloud.demand.lab.modules.longterm.predict.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.lab.modules.longterm.predict.service.impl.LongtermPredictDictServiceImpl.FullCampusBaseDictData;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class LongtermPredictDictServiceImplTest {

    @Resource
    LongtermPredictDictServiceImpl longtermPredictDictService;

    @Test
    void listAllCampusData() {

        List<FullCampusBaseDictData> fullCampusBaseDictData = longtermPredictDictService.listAllCampusData(null);
        System.out.println(fullCampusBaseDictData);

    }
}