package cloud.demand.lab.modules.longterm.cos;

import cloud.demand.lab.modules.longterm.cos.service.CosQueryPredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskPredictResultSummaryReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskPredictResultSummaryResp;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class CosQueryPredictTaskServiceTests {

    @Resource
    private CosQueryPredictTaskService cosQueryPredictTaskService;

    @Test
    public void testQueryTaskPredictResultSummary() {
        QueryTaskPredictResultSummaryReq req = new QueryTaskPredictResultSummaryReq();
        req.setTaskId(3L);
        QueryTaskPredictResultSummaryResp resp = cosQueryPredictTaskService.queryTaskPredictResultSummary(req);
        System.out.println(JSON.toJson(resp));
    }


}
