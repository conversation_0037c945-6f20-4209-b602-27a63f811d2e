package cloud.demand.lab.modules.longterm.predict.service.impl;

import cloud.demand.lab.modules.longterm.predict.web.req.split.CreateSplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SplitServiceImplTest {

    @Resource
    SplitService splitService;

    @Test
    void splitTest() {

        CreateSplitVersionReq createSplitVersionReq = new CreateSplitVersionReq();
        createSplitVersionReq.setName("test" + new Date());
        createSplitVersionReq.setTaskId(34L);
        createSplitVersionReq.setNote("test");
        splitService.splitWithOutRet(createSplitVersionReq);

    }

    @Test
    void splitScale() {
        CreateSplitVersionReq createSplitVersionReq = new CreateSplitVersionReq();
        createSplitVersionReq.setName("test" + new Date());
        createSplitVersionReq.setTaskId(34L);
        createSplitVersionReq.setNote("test");
//        splitService.splitScale(createSplitVersionReq);
    }
}