package cloud.demand.lab.modules.longterm.demand_delivery_data;

import cloud.demand.lab.modules.longterm.demand_delivery_data.service.ProvideDataService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class ProvideDataServiceTests {

    @Resource
    private ProvideDataService provideDataService;

    @Test
    public void test() {
        provideDataService.generateLongtermDemandAndExecuteDataDO();
        provideDataService.generateWeekDict();
    }
}
