package cloud.demand.lab.modules.operation_view;



import cloud.demand.lab.modules.operation_view.inventory_health.dto.BufferAverageCoreDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryReasonDO;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import yunti.boot.config.DynamicProperty;

@SpringBootTest
public class jsonrpcTest {

    @Test
    public void test1() throws Exception {
        OkHttpClient client = new OkHttpClient();
        String url = "https://exp-crp.woa.com/cloud-demand-app/operation-view2/getEnableDeliveryDataForAlgorithm";
        String json = "";
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("cookie", "_ra=_ra1713754354212.0.6525250561893701; x_host_key_access_https=ced08e244c965c1646f628d97180e679c915f10b_s; x-client-ssid=9c10b00b:0190107c01f7:07137c; x-tofapi-host-key=190107c02d2-9009f8747c7cfa62026286161c25cf611657d3af; ERP_USERNAME=regalguo; ERP_REAL_USERNAME=regalguo; uname=regalguo; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22regalguo%22%2C%22first_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%7D; fileDownload=true; DiggerTraceId=782ce7f9-f667-4395-9d1e-f365f1517a2e; DiggerTraceIdTs=1721380768182; x-imp-host-key=190cade82ce-2d60dc7bd391bbe71cad55630bfae16541dc41fd; _ga=GA1.2.1411422688.1721706196; _gcl_au=1.1.1754983964.1721706196; RIO_TOKEN=0.sheyr2.f1RRsUJh9YGsM4QH8KDEBEEKAge25G2tdCT8Wmsw22X75WFC9DCz3GYV-nigxNdHM-h0W5LtXeFiWJr7cn1BiFXO_f500rK_U3qxAhP7D3QlzldPSNJIbL7qBlwuYkLsuTRcLKjy24y8Ka3cxR-7W_L1d2955WsUtc2-zLnGgxN0B1x-E_4pkWD28trce9nS_bSOC7QfFhcrne3YHLHTZMB84LdEN54gf-Zo8WPtjHIRwtPRXHRmY9wqr90wEg3gUJVpcQ2r7cr7v-aWkILczMVVAHqJS1Vw9E4g5kwyvBdfz8KO_JaXZ3t3I7It76chQVYUU1m8tY-hhQyr7pFhAPfLzckxAqwlKg1yeILZvP8rSmV7vFEJnNqJfbmnQu7wu4Vremos2ICyBd-3P-8nbTS55JJyuFHY-ROxEouJZtPuZrt1yx7qoAT9MsgRL7m4DYiz5qhukT29uYzGh_PwWepycD_FJcC1wkRJDwohhMpNUEci2e2zqr-WoTMtBFRZSAKCWZrDpn8dv9lGemh8mBB9pZKoRlt0F0Ygt0v_PF_5TfHa7kyaXJpngy0THdyFE_DWUlf1Fc3LOOjto-5g3uX7cRQgCpVji7Y5TzU.vouh2w7i5MHxlvVaNzTDaw; RIO_TOKEN_HTTPS=0.sheyr2.f1RRsUJh9YGsM4QH8KDEBEEKAge25G2tdCT8Wmsw22X75WFC9DCz3GYV-nigxNdHM-h0W5LtXeFiWJr7cn1BiFXO_f500rK_U3qxAhP7D3QlzldPSNJIbL7qBlwuYkLsuTRcLKjy24y8Ka3cxR-7W_L1d2955WsUtc2-zLnGgxN0B1x-E_4pkWD28trce9nS_bSOC7QfFhcrne3YHLHTZMB84LdEN54gf-Zo8WPtjHIRwtPRXHRmY9wqr90wEg3gUJVpcQ2r7cr7v-aWkILczMVVAHqJS1Vw9E4g5kwyvBdfz8KO_JaXZ3t3I7It76chQVYUU1m8tY-hhQyr7pFhAPfLzckxAqwlKg1yeILZvP8rSmV7vFEJnNqJfbmnQu7wu4Vremos2ICyBd-3P-8nbTS55JJyuFHY-ROxEouJZtPuZrt1yx7qoAT9MsgRL7m4DYiz5qhukT29uYzGh_PwWepycD_FJcC1wkRJDwohhMpNUEci2e2zqr-WoTMtBFRZSAKCWZrDpn8dv9lGemh8mBB9pZKoRlt0F0Ygt0v_PF_5TfHa7kyaXJpngy0THdyFE_DWUlf1Fc3LOOjto-5g3uX7cRQgCpVji7Y5TzU.vouh2w7i5MHxlvVaNzTDaw; P_RIO_TOKEN=0.sheyr2.f-xQQMqvUinht_Scr3gu3z_Y9gioIzZrmnXFj0rJ6sPqGZlDe7tdHZh1ASKwKihaDHBIc7frfd9fCEd6AKTTGR01wlQsvQuLyqpoXDlmratwNz-C9iJr_0lMInI02EFBwpvI6fkuqY9aNvR5_iBNLtU_mRaxgCFFSEN2uMMEElfU7EVTrL6tB9BPc6TekGmDLX0aPeFzc5syj6jD7q3anUbih_FJ9UTXFjfKj3YdUnJbyAHXVwtOWeAQHDz5rFSEN9KRflarP1apnuKzJH9ShswZ4dUDjSkagDzzCecvfIgQwayhQ4UWunf2gOz1VQJCrOpJfKZfmzQpdNGxs4uZTTJgy6llDL7k923bbKegk8dZuwNX2m9SWIZKUtC1Y1D_Zi1ne1D9Q35goeOtfdb0Y35NiuLdPICJv5vxR5QjUu8zOEyvChNzvLqC1mEGAVK07CPpX65l8tW6NxQliYfqPVgWz1CKYlgil5usM6OKJIMAM0FLX1G8PWpTSYLJaV9r4ytAAMF-p0tJUIDGdwkjwyH9O6OdW91trchwM5Kb-47JHByMLTAE7ZgdBPlQuoN-bEgAdZBNScrK56NEcORm-RNqzhO5HBjO-s_fJmfcO0eln69TWGEWdZSiWapngQF_b7GdlGYvIAUzmdkUWc9aM_L4z1qvWCrdZt0qYiJt4o54CpQnaxDU1dWxeEqQqfCMwNzCQsRaJaq3MUwehi2LSFXZZM8MOd6nmW_rboYOVUDYSsA5YdZsWN01MdtT-lpOjaBNxb_ChrC58wTCetxFQf1tOaWotGkJMSbWp2P7RbxwpPqHJ6OrVI_QocpEt07HeNojwh516n4we4Y8w_Gn8BEI54_Ig1bS5u7GTxjfkpU96ZCuAtEY24u4RvGmIa0-pJBToeSE_NnUpQ.gHimBQ1dboyOkmoTWSbPVw; tgw_l7_route=aa0d5a127a7fc130370718fd557d8c90")
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                System.out.println(responseBody);
            } else {
            }
        } catch (IOException e) {
            throw new Exception();
        }

    }

    @Test
    public void test2() throws Exception {
        OkHttpClient client = new OkHttpClient();
        DynamicProperty<String> domain = DynamicProperty.create("app.product.domain", "");
        String url = String.format("%s/cloud-demand-app/rpc/getAllCvmType", domain.get());
        System.out.println(url);
        String body = "";
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("cookie", "_ra=_ra1713754354212.0.6525250561893701; x_host_key_access_https=ced08e244c965c1646f628d97180e679c915f10b_s; x-client-ssid=9c10b00b:0190107c01f7:07137c; x-tofapi-host-key=190107c02d2-9009f8747c7cfa62026286161c25cf611657d3af; ERP_USERNAME=regalguo; ERP_REAL_USERNAME=regalguo; uname=regalguo; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22regalguo%22%2C%22first_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%7D; fileDownload=true; DiggerTraceId=782ce7f9-f667-4395-9d1e-f365f1517a2e; DiggerTraceIdTs=1721380768182; x-imp-host-key=190cade82ce-2d60dc7bd391bbe71cad55630bfae16541dc41fd; _ga=GA1.2.1411422688.1721706196; _gcl_au=1.1.1754983964.1721706196; RIO_TOKEN=0.shgtir.9OGC_SyqrCUr8I5ZF34ZU3z9Ex-keqSSa7ElClf1MtPg4JwUz8LoDMfzFJ9sjguyx_ytIdhDFRTMzVcvrAyZkE2lKkey5boSyyr9J-qmoEUP-TmqKMve1cHGC_O54C6emmAV_PYPKnNa-AK99JfccU5Y6Ru1_pZUps03G_OZdyLTrS3zrXVx0qGAdauFLEYXqT_HTg90zV1uYGcFnjaJrYBGWklFJ_gsU8uoiw-my7RZNFQklDwMFjOPMIJsrgKDIXdCp3VNLMQOfTjf17XXzLoPeI1X-6fQA7xphe4D1zF4u2CiKoZAqAW-hV-7D8Ip2i2Jicl9--oP_jhr5vd1hWidIXjHAb3DxkyYYpYXsB55ivPIWyJpqkvi1ZUjKxDF-_F8fY2DeR2OIR12I3KuofTWbpmPG0ApmPjEtgQZBWpZVSJJmOLEUYdXbOCO5ETmT5yRK8vT80I5SVqxsagfkxqXdgKPCtgWFMnTKfnjhXeJNQZDG7pGuiD39An7uYeJ6QPCrCZC2VxeQwxSEAZ2QVl6nLz0rG8pR72wVvvhZq6SaZniGXof6jVCYEARPSWrmcf7BcfatrM8sg7d70WEE5bkLqa46x3CSmGGN31wjdpyetk.WpYTZ1qT6gNfRGXautABGg; RIO_TOKEN_HTTPS=0.shgtir.9OGC_SyqrCUr8I5ZF34ZU3z9Ex-keqSSa7ElClf1MtPg4JwUz8LoDMfzFJ9sjguyx_ytIdhDFRTMzVcvrAyZkE2lKkey5boSyyr9J-qmoEUP-TmqKMve1cHGC_O54C6emmAV_PYPKnNa-AK99JfccU5Y6Ru1_pZUps03G_OZdyLTrS3zrXVx0qGAdauFLEYXqT_HTg90zV1uYGcFnjaJrYBGWklFJ_gsU8uoiw-my7RZNFQklDwMFjOPMIJsrgKDIXdCp3VNLMQOfTjf17XXzLoPeI1X-6fQA7xphe4D1zF4u2CiKoZAqAW-hV-7D8Ip2i2Jicl9--oP_jhr5vd1hWidIXjHAb3DxkyYYpYXsB55ivPIWyJpqkvi1ZUjKxDF-_F8fY2DeR2OIR12I3KuofTWbpmPG0ApmPjEtgQZBWpZVSJJmOLEUYdXbOCO5ETmT5yRK8vT80I5SVqxsagfkxqXdgKPCtgWFMnTKfnjhXeJNQZDG7pGuiD39An7uYeJ6QPCrCZC2VxeQwxSEAZ2QVl6nLz0rG8pR72wVvvhZq6SaZniGXof6jVCYEARPSWrmcf7BcfatrM8sg7d70WEE5bkLqa46x3CSmGGN31wjdpyetk.WpYTZ1qT6gNfRGXautABGg; P_RIO_TOKEN=0.shgtir.9M2DXRyjuhrBGFLNrP8gTc6zdAcFsgndOEzQIjxsrSVsMhvzNK5DU711L9Zfqd1ma8tl4jlvsXlM929Jvm6btvTip7418cV-ZBMUiKib16QqIuxwtOKCkEpviSGcWLSTgzZIUOqU0FwD8INjwMaWbACbWjf_zCGhiYySbU3M9iCyQg1fEKhXuH5oAeu4IIqFD9w5SEEHsMxAsPF4na9rgyfamlRnLybvQbQFWXe4_r-fIADOvDWHCPy-dcQycscJiRfZEBuHT9l8kjn2P9M6ccmEgD3vlxCV1hOuB78Bxt4KPebTuVrybum6bcFCN0tqs4D999id-wRK7VNZ5d-sm-ciX7jIneWLpJmymM_iwJdug8VJr1er3Sl9GCtTHooK5b5eQIb7vO3EwCI0ELwUbescG0rylHrsN8lBOqkHrNjs8UHzWsRHPYDgfAgfZchSi2Y_l6ISoEPTdSko8dPvBwYgfY3LJiqrvfJrVC1xIP8SlrMBgxky_wzRLh22cJw9eEIrXaf46G6lKLAbUcO6Jp6_WZ4xR4nTYhlDDcbEdfVWDOgfaLd3RdEpVNaDxme0hw6uNN3lUoxiazvGQG-WyiS7-1NW_oSLwvCW9fSRI2oclQ0CjiFqwDXizM176LWrwbxzAco78Q4y_jYAb_GVWi188d7CSBF7NWffo05O_bsQDPloqGatUgqBUThqbclU2ZNzPWthIQ4DPtkoi3CfjqmYuLvdPERf8q0aKxIQ5se15aSOwby9sXzu1b3GSKknGHd34TtiGOp7Wol8TpRS1MKiFf1MCdFj9Bhd-9wlWk0wAc7Y.FLh_g1XBaizZKqamxCJ3AQ; tgw_l7_route=aa0d5a127a7fc130370718fd557d8c90")
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject resp = JSON.parseObject(responseBody);
                JSONArray result = resp.getJSONArray("result");
                List<CvmType> all = new ArrayList<>();
                if (result != null) {
                    all = JSON.parseObject(result.toJSONString(), new TypeReference<List<CvmType>>(){});
                }
                System.out.println(all);
            }
        } catch (IOException e) {
            throw new Exception();
        }

    }

    @Test
    public void test3() throws Exception {
        OkHttpClient client = new OkHttpClient();
        DynamicProperty<String> domain = DynamicProperty.create("app.product.domain", "");
        String url = String.format("%s/cloud-demand-app/rpc/listRangeReasonsParallel",
                domain.get());
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("startDate", "2024-05-10");
        params.put("endDate", "2024-07-10");
        body.put("params", params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body.toJSONString());
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("cookie", "_ra=_ra1713754354212.0.6525250561893701; x_host_key_access_https=ced08e244c965c1646f628d97180e679c915f10b_s; x-client-ssid=9c10b00b:0190107c01f7:07137c; x-tofapi-host-key=190107c02d2-9009f8747c7cfa62026286161c25cf611657d3af; ERP_USERNAME=regalguo; ERP_REAL_USERNAME=regalguo; uname=regalguo; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22regalguo%22%2C%22first_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%7D; fileDownload=true; DiggerTraceId=782ce7f9-f667-4395-9d1e-f365f1517a2e; DiggerTraceIdTs=1721380768182; x-imp-host-key=190cade82ce-2d60dc7bd391bbe71cad55630bfae16541dc41fd; _ga=GA1.2.1411422688.1721706196; _gcl_au=1.1.1754983964.1721706196; RIO_TOKEN=0.sheyr2.f1RRsUJh9YGsM4QH8KDEBEEKAge25G2tdCT8Wmsw22X75WFC9DCz3GYV-nigxNdHM-h0W5LtXeFiWJr7cn1BiFXO_f500rK_U3qxAhP7D3QlzldPSNJIbL7qBlwuYkLsuTRcLKjy24y8Ka3cxR-7W_L1d2955WsUtc2-zLnGgxN0B1x-E_4pkWD28trce9nS_bSOC7QfFhcrne3YHLHTZMB84LdEN54gf-Zo8WPtjHIRwtPRXHRmY9wqr90wEg3gUJVpcQ2r7cr7v-aWkILczMVVAHqJS1Vw9E4g5kwyvBdfz8KO_JaXZ3t3I7It76chQVYUU1m8tY-hhQyr7pFhAPfLzckxAqwlKg1yeILZvP8rSmV7vFEJnNqJfbmnQu7wu4Vremos2ICyBd-3P-8nbTS55JJyuFHY-ROxEouJZtPuZrt1yx7qoAT9MsgRL7m4DYiz5qhukT29uYzGh_PwWepycD_FJcC1wkRJDwohhMpNUEci2e2zqr-WoTMtBFRZSAKCWZrDpn8dv9lGemh8mBB9pZKoRlt0F0Ygt0v_PF_5TfHa7kyaXJpngy0THdyFE_DWUlf1Fc3LOOjto-5g3uX7cRQgCpVji7Y5TzU.vouh2w7i5MHxlvVaNzTDaw; RIO_TOKEN_HTTPS=0.sheyr2.f1RRsUJh9YGsM4QH8KDEBEEKAge25G2tdCT8Wmsw22X75WFC9DCz3GYV-nigxNdHM-h0W5LtXeFiWJr7cn1BiFXO_f500rK_U3qxAhP7D3QlzldPSNJIbL7qBlwuYkLsuTRcLKjy24y8Ka3cxR-7W_L1d2955WsUtc2-zLnGgxN0B1x-E_4pkWD28trce9nS_bSOC7QfFhcrne3YHLHTZMB84LdEN54gf-Zo8WPtjHIRwtPRXHRmY9wqr90wEg3gUJVpcQ2r7cr7v-aWkILczMVVAHqJS1Vw9E4g5kwyvBdfz8KO_JaXZ3t3I7It76chQVYUU1m8tY-hhQyr7pFhAPfLzckxAqwlKg1yeILZvP8rSmV7vFEJnNqJfbmnQu7wu4Vremos2ICyBd-3P-8nbTS55JJyuFHY-ROxEouJZtPuZrt1yx7qoAT9MsgRL7m4DYiz5qhukT29uYzGh_PwWepycD_FJcC1wkRJDwohhMpNUEci2e2zqr-WoTMtBFRZSAKCWZrDpn8dv9lGemh8mBB9pZKoRlt0F0Ygt0v_PF_5TfHa7kyaXJpngy0THdyFE_DWUlf1Fc3LOOjto-5g3uX7cRQgCpVji7Y5TzU.vouh2w7i5MHxlvVaNzTDaw; P_RIO_TOKEN=0.sheyr2.f-xQQMqvUinht_Scr3gu3z_Y9gioIzZrmnXFj0rJ6sPqGZlDe7tdHZh1ASKwKihaDHBIc7frfd9fCEd6AKTTGR01wlQsvQuLyqpoXDlmratwNz-C9iJr_0lMInI02EFBwpvI6fkuqY9aNvR5_iBNLtU_mRaxgCFFSEN2uMMEElfU7EVTrL6tB9BPc6TekGmDLX0aPeFzc5syj6jD7q3anUbih_FJ9UTXFjfKj3YdUnJbyAHXVwtOWeAQHDz5rFSEN9KRflarP1apnuKzJH9ShswZ4dUDjSkagDzzCecvfIgQwayhQ4UWunf2gOz1VQJCrOpJfKZfmzQpdNGxs4uZTTJgy6llDL7k923bbKegk8dZuwNX2m9SWIZKUtC1Y1D_Zi1ne1D9Q35goeOtfdb0Y35NiuLdPICJv5vxR5QjUu8zOEyvChNzvLqC1mEGAVK07CPpX65l8tW6NxQliYfqPVgWz1CKYlgil5usM6OKJIMAM0FLX1G8PWpTSYLJaV9r4ytAAMF-p0tJUIDGdwkjwyH9O6OdW91trchwM5Kb-47JHByMLTAE7ZgdBPlQuoN-bEgAdZBNScrK56NEcORm-RNqzhO5HBjO-s_fJmfcO0eln69TWGEWdZSiWapngQF_b7GdlGYvIAUzmdkUWc9aM_L4z1qvWCrdZt0qYiJt4o54CpQnaxDU1dWxeEqQqfCMwNzCQsRaJaq3MUwehi2LSFXZZM8MOd6nmW_rboYOVUDYSsA5YdZsWN01MdtT-lpOjaBNxb_ChrC58wTCetxFQf1tOaWotGkJMSbWp2P7RbxwpPqHJ6OrVI_QocpEt07HeNojwh516n4we4Y8w_Gn8BEI54_Ig1bS5u7GTxjfkpU96ZCuAtEY24u4RvGmIa0-pJBToeSE_NnUpQ.gHimBQ1dboyOkmoTWSbPVw; tgw_l7_route=aa0d5a127a7fc130370718fd557d8c90")
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONObject result = jsonObject.getJSONObject("result");
                System.out.println(result.toJSONString());
                Map<String, List<InventoryReasonDO>> stringListMap = JSON.parseObject(result.toJSONString(),
                        new TypeReference<Map<String, List<InventoryReasonDO>>>() {
                        });
                System.out.println(stringListMap);
            }
        } catch (IOException e) {
            throw new Exception();
        }

    }

    @Test
    public void test4() throws IOException {
        OkHttpClient client = new OkHttpClient();
        DynamicProperty<String> crpUrl = DynamicProperty.create("app.product.domain", "");
        String url = String.format("%s/cloud-demand-app/rpc/queryBufferCoreAverage",
                crpUrl.get());
        JSONObject body = new JSONObject();
        JSONObject params = new JSONObject();
        params.put("date", "2024-06-10");
        body.put("params", params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),
                body.toJSONString());
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("cookie", "_ra=_ra1713754354212.0.6525250561893701; x_host_key_access_https=ced08e244c965c1646f628d97180e679c915f10b_s; x-client-ssid=9c10b00b:0190107c01f7:07137c; x-tofapi-host-key=190107c02d2-9009f8747c7cfa62026286161c25cf611657d3af; ERP_USERNAME=regalguo; ERP_REAL_USERNAME=regalguo; uname=regalguo; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22regalguo%22%2C%22first_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22190337feda212bb-065d330f3d9832-1a525637-1930176-190337feda31f48%22%7D; fileDownload=true; DiggerTraceId=782ce7f9-f667-4395-9d1e-f365f1517a2e; DiggerTraceIdTs=1721380768182; x-imp-host-key=190cade82ce-2d60dc7bd391bbe71cad55630bfae16541dc41fd; _ga=GA1.2.1411422688.1721706196; _gcl_au=1.1.1754983964.1721706196; RIO_TOKEN=0.shgtir.9OGC_SyqrCUr8I5ZF34ZU3z9Ex-keqSSa7ElClf1MtPg4JwUz8LoDMfzFJ9sjguyx_ytIdhDFRTMzVcvrAyZkE2lKkey5boSyyr9J-qmoEUP-TmqKMve1cHGC_O54C6emmAV_PYPKnNa-AK99JfccU5Y6Ru1_pZUps03G_OZdyLTrS3zrXVx0qGAdauFLEYXqT_HTg90zV1uYGcFnjaJrYBGWklFJ_gsU8uoiw-my7RZNFQklDwMFjOPMIJsrgKDIXdCp3VNLMQOfTjf17XXzLoPeI1X-6fQA7xphe4D1zF4u2CiKoZAqAW-hV-7D8Ip2i2Jicl9--oP_jhr5vd1hWidIXjHAb3DxkyYYpYXsB55ivPIWyJpqkvi1ZUjKxDF-_F8fY2DeR2OIR12I3KuofTWbpmPG0ApmPjEtgQZBWpZVSJJmOLEUYdXbOCO5ETmT5yRK8vT80I5SVqxsagfkxqXdgKPCtgWFMnTKfnjhXeJNQZDG7pGuiD39An7uYeJ6QPCrCZC2VxeQwxSEAZ2QVl6nLz0rG8pR72wVvvhZq6SaZniGXof6jVCYEARPSWrmcf7BcfatrM8sg7d70WEE5bkLqa46x3CSmGGN31wjdpyetk.WpYTZ1qT6gNfRGXautABGg; RIO_TOKEN_HTTPS=0.shgtir.9OGC_SyqrCUr8I5ZF34ZU3z9Ex-keqSSa7ElClf1MtPg4JwUz8LoDMfzFJ9sjguyx_ytIdhDFRTMzVcvrAyZkE2lKkey5boSyyr9J-qmoEUP-TmqKMve1cHGC_O54C6emmAV_PYPKnNa-AK99JfccU5Y6Ru1_pZUps03G_OZdyLTrS3zrXVx0qGAdauFLEYXqT_HTg90zV1uYGcFnjaJrYBGWklFJ_gsU8uoiw-my7RZNFQklDwMFjOPMIJsrgKDIXdCp3VNLMQOfTjf17XXzLoPeI1X-6fQA7xphe4D1zF4u2CiKoZAqAW-hV-7D8Ip2i2Jicl9--oP_jhr5vd1hWidIXjHAb3DxkyYYpYXsB55ivPIWyJpqkvi1ZUjKxDF-_F8fY2DeR2OIR12I3KuofTWbpmPG0ApmPjEtgQZBWpZVSJJmOLEUYdXbOCO5ETmT5yRK8vT80I5SVqxsagfkxqXdgKPCtgWFMnTKfnjhXeJNQZDG7pGuiD39An7uYeJ6QPCrCZC2VxeQwxSEAZ2QVl6nLz0rG8pR72wVvvhZq6SaZniGXof6jVCYEARPSWrmcf7BcfatrM8sg7d70WEE5bkLqa46x3CSmGGN31wjdpyetk.WpYTZ1qT6gNfRGXautABGg; P_RIO_TOKEN=0.shgtir.9M2DXRyjuhrBGFLNrP8gTc6zdAcFsgndOEzQIjxsrSVsMhvzNK5DU711L9Zfqd1ma8tl4jlvsXlM929Jvm6btvTip7418cV-ZBMUiKib16QqIuxwtOKCkEpviSGcWLSTgzZIUOqU0FwD8INjwMaWbACbWjf_zCGhiYySbU3M9iCyQg1fEKhXuH5oAeu4IIqFD9w5SEEHsMxAsPF4na9rgyfamlRnLybvQbQFWXe4_r-fIADOvDWHCPy-dcQycscJiRfZEBuHT9l8kjn2P9M6ccmEgD3vlxCV1hOuB78Bxt4KPebTuVrybum6bcFCN0tqs4D999id-wRK7VNZ5d-sm-ciX7jIneWLpJmymM_iwJdug8VJr1er3Sl9GCtTHooK5b5eQIb7vO3EwCI0ELwUbescG0rylHrsN8lBOqkHrNjs8UHzWsRHPYDgfAgfZchSi2Y_l6ISoEPTdSko8dPvBwYgfY3LJiqrvfJrVC1xIP8SlrMBgxky_wzRLh22cJw9eEIrXaf46G6lKLAbUcO6Jp6_WZ4xR4nTYhlDDcbEdfVWDOgfaLd3RdEpVNaDxme0hw6uNN3lUoxiazvGQG-WyiS7-1NW_oSLwvCW9fSRI2oclQ0CjiFqwDXizM176LWrwbxzAco78Q4y_jYAb_GVWi188d7CSBF7NWffo05O_bsQDPloqGatUgqBUThqbclU2ZNzPWthIQ4DPtkoi3CfjqmYuLvdPERf8q0aKxIQ5se15aSOwby9sXzu1b3GSKknGHd34TtiGOp7Wol8TpRS1MKiFf1MCdFj9Bhd-9wlWk0wAc7Y.FLh_g1XBaizZKqamxCJ3AQ; tgw_l7_route=aa0d5a127a7fc130370718fd557d8c90")
                .post(requestBody)
                .build();
        List<BufferAverageCoreDTO> all = new ArrayList<>();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                // 获取响应体
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject resp = JSON.parseObject(responseBody);
                JSONArray result = resp.getJSONArray("result");
                if (result != null) {
                    all = JSON.parseObject(result.toJSONString(), new TypeReference<List<BufferAverageCoreDTO>>(){});
                }
            }
        }
        System.out.println(all);
    }


}
