package cloud.demand.lab.modules.operation_view;

import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandGenService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.task.SupplyAndDemandTask;
import java.time.LocalDate;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SupplyAndDemandGenTest {

    @Resource
    private SupplyAndDemandGenService supplyAndDemandGenService;

    @Resource
    private SupplyAndDemandTask supplyAndDemandTask;

    @Test
    public void syncCvmDemandData() {
        supplyAndDemandGenService.syncCvmDemandData(LocalDate.parse("2025-07-07"), true);
    }

    @Test
    public void syncDemandData() {
        supplyAndDemandTask.syncPplJoinOrderNewestData();
    }

}
