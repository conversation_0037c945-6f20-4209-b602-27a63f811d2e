package cloud.demand.lab.modules.forecast;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class DataWarehouseServiceTest {

    @Resource
    DataWarehouseService dataWarehouseService;

    @Test
    void genCkDWH() {
        int i = dataWarehouseService.genCkDWH("705");
        System.out.println(i);
    }

    @Test
    void genAllCkDWH() {
        System.out.println(dataWarehouseService.genCkDWH());
    }
}