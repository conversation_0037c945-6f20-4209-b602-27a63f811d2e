package cloud.demand.lab.modules.common_dict.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.ResPlanService;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
class ResPlanServiceImplTest {

    @Resource
    ResPlanServiceImpl resPlanService;


    @Test
    void getAllHolidayWeekInfos() {
        IntStream.range(1, 10).mapToObj((o) -> CompletableFuture.runAsync(() -> {
                    if (o == 1) {
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    List<ResPlanHolidayWeekDO> all = resPlanService.getAllHolidayWeekInfos();
                    if (o == 1) {
                        System.out.println("1" + all.size());
                    } else {
                        System.out.println(all.size());
                    }
                })
        ).collect(Collectors.toList()).forEach((o) -> {
            try {
                o.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });

    }
}