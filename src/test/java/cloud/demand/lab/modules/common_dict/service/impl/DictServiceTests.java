package cloud.demand.lab.modules.common_dict.service.impl;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.longterm.predict.service.LongtermPredictDictService;
import cloud.demand.lab.modules.longterm.predict.service.impl.LongtermPredictDictServiceImpl;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class DictServiceTests {

    @Resource
    private DictService dictService;
    @Resource
    private LongtermPredictDictService longtermPredictDictService;

    @Test
    public void testGetZone2CampusInfoMap() {
        Map<String, String> zone2CampusInfoMap = dictService.getZone2CampusInfoMap();
        zone2CampusInfoMap.entrySet().forEach(
                entry -> System.out.println(entry.getKey() + ":" + entry.getValue()));
    }

    @Test
    public void testGetCampusToZoneMap() {
        Map<String, StaticZoneDO> campus2ZoneInfoMap = dictService.getCampus2ZoneInfoMap();
        campus2ZoneInfoMap.entrySet().forEach(
                entry -> System.out.println(entry.getKey() + ":" + entry.getValue()));
    }

    @Test
    public void testGetCampusToErpZone() {
        Map<String, LongtermPredictDictServiceImpl.FullCampusBaseDictData> campusToErpRegionInfoMap =
                longtermPredictDictService.getCampusToErpRegionInfoMap();
        campusToErpRegionInfoMap.entrySet().forEach(
                entry -> System.out.println(entry.getKey() + ":" + entry.getValue()));
    }

    @Test
    public void testDeviceTypeToInstanceType() {
        Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
        List<String> deviceType = ListUtils.of("T0-CM62A-100GS", "T0-SC26A-100GS", "T0-CS71AB-100GS", "T0-CS72A-100GS", "T0-CS81X-100GS", "T0-CI71AB-100GS", "T0-CS91X-200GS", "T0-CS83AB-200GS", "T0-CS82AB-200GS", "T0-CS84AB-200GS", "T0-CS85AB-200GS", "T0-CS92XB-200GS", "T0-CS81A-200GS", "T0-CS94XB-200GS");
        deviceType.forEach(type -> System.out.println(type + ":" + deviceTypeToInstanceTypeMap.get(type)));
    }

    @Test
    public void testGetDeviceCore() {
        Map<String, Integer> deviceTypeToCoreNumMap = longtermPredictDictService.getDeviceTypeToCoreNumMap();
        List<String> deviceType = ListUtils.of("T0-CI71A-100GS", "T0-CI71AB-100GS", "T0-CM62A-100GS", "T0-CM81X-100GS", "T0-CS65X-100GS", "T0-CS71AB-100GS", "T0-CS72A-100GS", "T0-CS72AB-100GS", "T0-CS81A-200GS", "T0-CS81X-100GS", "T0-CS82AB-200GS", "T0-CS85AB-200GS", "T0-CS91X-200GS", "T0-CS94XB-200GS", "T0-SC26A-100GS", "X0-CS81A-200GS", "X0-CS83AB-200GS", "X0-CS91X-200GS");
        deviceType.forEach(type -> System.out.println(type + ":" + deviceTypeToCoreNumMap.get(type)));
    }
}
